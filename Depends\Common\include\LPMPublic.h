﻿#pragma once

/*
******		filename	:	LMPPublic.h
******		describe	:	编码格式是 utf-8，	比如日志与路径
******		date		:	2020-12-30
******		author		:	Mike
******		copyright	:	Guangzhou AiYunJi Inc.
******
*/

#include <string>
#include <set>
#include <vector>

using namespace std;

#include "LPMCommonLib.h"


namespace LPM
{
	//////////		编码转换	////////////

	//	Utf8 转 GBK
	string LPM_PUBLIC_API  Utf8ToGBK(const char* _szUtf8In);

	//	GBK 转 Utf8
	string LPM_PUBLIC_API GBKToUtf8(const char* _szGBKIn);

	//	utf-8转Unicode
	wstring LPM_PUBLIC_API U2W(const char* _szUtf8In);

	//	宽字节转Ascii
	string LPM_PUBLIC_API W2AEx(const wstring& _wstrCodeIn);

	//	Utf8 转 ASCII
	string LPM_PUBLIC_API U2A(const string& _strUtf8In);

	string LPM_PUBLIC_API U2AEx(const string& _strUtf8In);

	//	ASCII 转 Unicode
	wstring LPM_PUBLIC_API A2WEx(const string& _strAsciiIn);

	// Unicode转Utf8
	string LPM_PUBLIC_API W2U(const wstring _wstrIn);


	//	时间格式转换
	string LPM_PUBLIC_API GetTimeFromTickCount(string _strTickCount);
	string LPM_PUBLIC_API GetTimeFromTotalSecond(long long _llTotalSecond);

	//	是否是64位系统
	bool LPM_PUBLIC_API Is64BitOS();

	//字符串删首尾空
	void LPM_PUBLIC_API StrTrimEx(string& _strIn);


	//邮箱是否正确
	bool LPM_PUBLIC_API MailIsCorrect(const string& _strMail);


	//获取cpu名称
	string LPM_PUBLIC_API GetCpuName();

	string LPM_PUBLIC_API GetNtVersionNumbers();

	//	网络
	string LPM_PUBLIC_API GetIpAddrRemoteBySock(int _nScokId, int &_nPort);
	string LPM_PUBLIC_API GetIpAddrLocalBySock(int _nScokId, int &_nPort);

	//	字符操作
	string LPM_PUBLIC_API ReplaceStr(const std::string& _strIn, const std::string& _strSub, const std::string& _strNew);
	string LPM_PUBLIC_API HttpParaEscape(const std::string& _strIn);

	//	文件  return 0 创建文件夹成功， 1 文件夹已经存在， -1 创建失败
	int LPM_PUBLIC_API CreateFolder(const char* _szFolderU8);
	int LPM_PUBLIC_API CreateFolder(const std::wstring& _strFolderPath);

	//	遍历文件夹 _setPointSuffix ，指定后缀; _setPointFolder.指定文件夹
	int LPM_PUBLIC_API TraverseFolder(const char* _szFolderU8, std::set<std::wstring>& _setFolder, std::set<std::wstring>& _setFile, const std::set<std::string>& _setPointSuffix = {}, const std::set<std::wstring>& _setPointFolder = {});
	int LPM_PUBLIC_API TraverseFolder(const std::wstring& _strFolderPath, std::set<std::wstring>& _setFolder, std::set<std::wstring>& _setFile, const std::set<std::string>& _setPointSuffix = {}, const std::set<std::wstring>& _setPointFolder = {});

	//	判断文件或文件夹是否存在
	bool LPM_PUBLIC_API IsFileOrFolderExsit(const char* _szFilePathU8);
	bool LPM_PUBLIC_API IsFileOrFolderExsit(const wchar_t* _wszFilePathU8);

	//	删除文件或文件夹
	bool LPM_PUBLIC_API RemoveFileOrFolder(const char* _szFilePathU8);
	bool LPM_PUBLIC_API RemoveFileOrFolder(const std::wstring& _strWFilePath);

	//	获取文件后缀
	std::string LPM_PUBLIC_API GetFileSuffix(const char* _szFilePathU8);
	std::wstring LPM_PUBLIC_API GetFileSuffix(const std::wstring& _wstrFilePath);

	//	获取名称 无后缀的文件名称, 如 123.text , 返回123
	std::string  LPM_PUBLIC_API GetFileNameNoSuffix(const char* _szFileName);
	std::wstring  LPM_PUBLIC_API GetFileNameNoSuffix(const wchar_t* _wszFileName);

	//	获取	exe 文件夹
	wstring LPM_PUBLIC_API GetExePath();
	//	获取时间
	std::string LPM_PUBLIC_API TimerGetStampEx(unsigned long long _llStamp);

	//	拷贝文件
	bool LPM_PUBLIC_API AyjCopyFile(const std::string& _strSrcDirUtf8, const std::string& _strToDirUtf8, bool _bIsCover = false);
	bool LPM_PUBLIC_API AyjCopyFile(const std::wstring& _wstrSrcDirUtf8, const std::wstring& _wstrToDirUtf8, bool _bIsCover = false);

	//	根据文件绝对路径 获取文件拆分信息
	bool LPM_PUBLIC_API GetFileSplitInfo(const char* _szFilePathU8, std::wstring& _strDriverOut, std::wstring& _strNameOut, std::wstring& _strExtOut);

	//	字符串 分割 双字符串 分割，
	std::vector<std::string> LPM_PUBLIC_API SplitString(const std::string& _strIn, const std::string& _strStrdelimiter1, const std::string& _strStrdelimiter2);
	//	单字符 分割
	std::vector<std::string> LPM_PUBLIC_API SplitString(const std::string& _strIn, const std::string& _strStrdelimiter1);

	//	删除所有指定字符串
	void LPM_PUBLIC_API RemoveAllString(std::string& _strIn, const std::string& _strToRemove);
	void LPM_PUBLIC_API RemoveAllString(std::wstring& _strIn, const std::wstring& _strToRemove);
}
