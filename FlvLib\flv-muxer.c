#include "flv-muxer.h"
#include "flv-encryption-helper.h"
#include "flv-encryption.h"
#include "flv-proto.h"
#include "amf0.h"
#include <stdio.h>
#include <errno.h>
#include <stdlib.h>
#include <assert.h>
#include <stdint.h>
#include <string.h>
#include "mpeg4-avc.h"
#include <direct.h>

struct flv_muxer_t
{
	flv_muxer_handler handler;
	void* param;

	uint8_t aac_sequence_header;
	uint8_t avc_sequence_header;

	union
	{
		struct mpeg4_avc_t avc;
	} v;
	int keyframe;

	uint8_t* ptr;
	size_t bytes;
	size_t capacity;
    encry_version version;
};

int h264_update_avc(struct mpeg4_avc_t* avc, const uint8_t* nalu, size_t bytes);
struct flv_muxer_t* flv_muxer_create(flv_muxer_handler handler, void* param, int splice, int encipher)
{
	struct flv_muxer_t* flv;
	flv = (struct flv_muxer_t*)calloc(1, sizeof(struct flv_muxer_t));
    if (NULL == flv) {
		return NULL;
    }

	flv_muxer_reset(flv);
    flv->avc_sequence_header = splice;// If need to splice video, you don't need to write media information
	flv->handler = handler;
	flv->param = param;
    encry_version v = encipher;
    if (encipher) {
        v = encry_version_1;
    }
    flv->version = v;
	return flv;
}

void flv_muxer_destroy(struct flv_muxer_t* flv)
{
	if (flv->ptr)
	{
		assert(flv->capacity > 0);
		free(flv->ptr);
		flv->ptr = NULL;
	}

	free(flv);
}

int flv_muxer_reset(struct flv_muxer_t* flv)
{
	flv->v.avc.nalu = 4;
	flv->aac_sequence_header = 0;
	flv->avc_sequence_header = 0;
	return 0;
}

static int flv_muxer_alloc(struct flv_muxer_t* flv, size_t bytes)
{
	void* p;
	p = realloc(flv->ptr, bytes);
	if (!p)
		return ENOMEM;

	flv->ptr = (uint8_t*)p;
	flv->capacity = bytes;
	return 0;
}

static int flv_muxer_h264(struct flv_muxer_t* flv, uint32_t pts, uint32_t dts)
{
	int r;
	int m, compositionTime;
    // TODO::ENC_TEST
    //char filename[32] = { 0 };
    //_mkdir("./h264");
    //sprintf(filename, "./h264/muxer_h264_%llu.h264", pts);
    //FILE* fp = fopen(filename, "wb+");
    //fwrite(flv->ptr, 1, flv->bytes, fp);
    //fclose(fp);

	if (0 == flv->avc_sequence_header)
	{
		if (flv->v.avc.nb_sps < 1 || flv->v.avc.nb_pps < 1)
			return 0;

		flv->ptr[flv->bytes + 0] = (1 << 4) /*FrameType*/ | FLV_VIDEO_H264 /*CodecID*/;
		flv->ptr[flv->bytes + 1] = 0; // AVC sequence header
		flv->ptr[flv->bytes + 2] = 0; // CompositionTime 0
		flv->ptr[flv->bytes + 3] = 0;
		flv->ptr[flv->bytes + 4] = 0;
		m = mpeg4_avc_decoder_configuration_record_save(&flv->v.avc, flv->ptr + flv->bytes + 5, flv->capacity - flv->bytes - 5);
		if (m <= 0)
			return -1; // invalid data

		flv->avc_sequence_header = 1; // once only
		assert(flv->bytes + m + 5 <= (int)flv->capacity);
		r = flv->handler(flv->param, FLV_TYPE_VIDEO, 1, flv->ptr + flv->bytes, m + 5, dts, flv->version);
		if (0 != r) return r;
	}

	// has video frame
	if (flv->bytes > 5)
	{
		compositionTime = pts - dts;
		flv->ptr[0] = ((flv->keyframe ? 1 : 2) << 4) /*FrameType*/ | FLV_VIDEO_H264 /*CodecID*/;
		flv->ptr[1] = 1; // AVC NALU
		flv->ptr[2] = (compositionTime >> 16) & 0xFF;
		flv->ptr[3] = (compositionTime >> 8) & 0xFF;
		flv->ptr[4] = compositionTime & 0xFF;

        assert(flv->bytes <= (int)flv->capacity);
        return flv->handler(flv->param, FLV_TYPE_VIDEO, 0, flv->ptr, flv->bytes, dts, flv->version);
	}
	return 0;
}

int flv_muxer_avc(struct flv_muxer_t* flv, const void* data, size_t bytes, uint32_t pts, uint32_t dts)
{
	if (flv->capacity < bytes + 2048/*AVCDecoderConfigurationRecord*/)
	{
		if (0 != flv_muxer_alloc(flv, bytes + 2048))
			return ENOMEM;
	}

	flv->bytes = 5;
	flv->bytes += mpeg4_annexbtomp4(&flv->v.avc, data, bytes, flv->ptr + flv->bytes, flv->capacity - flv->bytes);
	if (flv->bytes <= 5)
		return ENOMEM;

	flv->keyframe = flv->v.avc.chroma_format_idc; // hack
	return flv_muxer_h264(flv, pts, dts);
}

int flv_muxer_h264_nalu(struct flv_muxer_t* flv, const void* nalu, size_t bytes, uint32_t pts, uint32_t dts)
{
	int r = 0;
	int type;
    //  TODO::ENC_TEST
    //char filename[32] = { 0 };
    //_mkdir("./h264");
    //sprintf(filename, "./h264/nalu_%llu.h264", pts);
    //FILE* fp = fopen(filename, "wb+");
    //fwrite(nalu, 1, bytes, fp);
    //fclose(fp);

	type = h264_update_avc(&flv->v.avc, (const uint8_t*)nalu, bytes);
	if (type < 0)
		return -1;
	switch (type)
	{
	case 7:
	case 8:
		break;

	default:
		if (flv->capacity < bytes + 2048/*AVCDecoderConfigurationRecord*/)
		{
			if (0 != flv_muxer_alloc(flv, bytes + 2048))
				return ENOMEM;
		}

		flv->ptr[flv->bytes + 5] = (uint8_t)((bytes >> 24) & 0xFF);
		flv->ptr[flv->bytes + 6] = (uint8_t)((bytes >> 16) & 0xFF);
		flv->ptr[flv->bytes + 7] = (uint8_t)((bytes >> 8) & 0xFF);
		flv->ptr[flv->bytes + 8] = (uint8_t)((bytes >> 0) & 0xFF);
		memcpy(flv->ptr + 5 + flv->bytes + 4, nalu, bytes);
		flv->bytes += bytes + 4;
		flv->keyframe = type == 5;
	}

	if (type < 1 || type > 5)
		return 0; // no-VCL

	assert(flv->bytes > 0);
	flv->bytes += 5;
	r = flv_muxer_h264(flv, pts, dts);
	flv->bytes = 0;
	return r;
}

int flv_encrypytion_version(flv_muxer_t* muxer)
{
    if (muxer == NULL) {
        return -1;
    }
    return muxer->version;
}
