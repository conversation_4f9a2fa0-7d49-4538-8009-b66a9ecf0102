﻿/*
 * Filename:  RecorderFlv.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 7/2/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */

#include "AYJMp4Write.h"

#include "ImageScale/ImageScale.h"

namespace AYJ
{
	CAyjMp4Writer::CAyjMp4Writer()
	{

	}
	
	CAyjMp4Writer::~CAyjMp4Writer()
	{
		Mp4WriteClose();
	}

	int CAyjMp4Writer::AddVideoStream(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264)
	{
		//1 视频编码器创建
		AVCodec *pCodec = avcodec_find_encoder(AV_CODEC_ID_H264);
		if (!pCodec)
		{
			WriteLogEx(ModuleVideo, LogError,u8"avcodec_find_encoder AV_CODEC_ID_H264 failed!");
			return -1;
		}
		m_pCodecCnt = avcodec_alloc_context3(pCodec);
		if (!m_pCodecCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_alloc_context3 failed!");
			return -1;
		}
		//比特率，压缩后每秒大小
		m_pCodecCnt->bit_rate = _nBitrate;
		m_pCodecCnt->width = _nVdieoW;
		m_pCodecCnt->height = _nVideoH;

		//时间基数
		m_pCodecCnt->time_base = { 1, (int)_fFps };
		m_pCodecCnt->framerate = { (int)_fFps, 1 };

		//画面组大小，多少帧一个关键帧
		m_pCodecCnt->gop_size = _nGopSize;

		m_pCodecCnt->max_b_frames = 0;


		m_pCodecCnt->pix_fmt = AV_PIX_FMT_YUV420P;
		m_pCodecCnt->codec_id = AV_CODEC_ID_H264;
		//av_opt_set(m_pCodecCnt->priv_data, "preset", "superfast", 0);

		m_pCodecCnt->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;

		AVDictionary *pParam = 0;
		av_dict_set(&pParam, "x264-params", "crf=18", 0);
		//av_dict_set(&pParam, "x264-params", "qp=10", 0);
		av_dict_set(&pParam, "preset", "fast", 0);
		//av_dict_set(&pParam, "tune", "zerolatency", 0);

		//打开编码器
		int nRet = avcodec_open2(m_pCodecCnt, pCodec, &pParam);
		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_open2 failed!");
			return -1;
		}

		//添加视频流到输出上下文
		m_pVideoStream = avformat_new_stream(m_pFormatCnt, nullptr);
		if (nullptr == m_pVideoStream)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avformat_new_stream failed!");
			return -1;
		}
		m_pVideoStream->codecpar->codec_tag = 0;
		m_pVideoStream->id = m_pFormatCnt->nb_streams - 1;
		//m_pVideoStream->time_base = m_pCodecCnt->time_base;
		//m_pVideoStream->avg_frame_rate = m_pCodecCnt->time_base;
		avcodec_parameters_from_context(m_pVideoStream->codecpar, m_pCodecCnt);

		av_dump_format(m_pFormatCnt, 0, m_strMp4Path.c_str(), 1);

		return 0;
	}

	int CAyjMp4Writer::InitSws(int _nVideoFormat, int _nVideoInW, int _nVideoInH, int _nVideoOutW, int _nVideoOutH)
	{
		//像素（尺寸）转换上下文 rgb to yuv
		if (m_pSwsCnt && _nVideoFormat == m_nVideoFormatIn && _nVideoInW == m_nVideoInW && _nVideoInH == m_nVideoInH)
			return 0;

		if (m_pSwsCnt)
			sws_freeContext(m_pSwsCnt);
		AVPixelFormat oo = AV_PIX_FMT_RGB32;
		m_pSwsCnt = sws_getContext(_nVideoInW, _nVideoInH, (AVPixelFormat)_nVideoFormat, _nVideoOutW, _nVideoOutH, AV_PIX_FMT_YUV420P, SWS_BICUBIC, nullptr, nullptr, nullptr);
		if (!m_pSwsCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"sws_getCachedContext failed!");
			return -1;
		}
		m_nVideoInW = _nVideoInW;
		m_nVideoInH = _nVideoInH;

		if (!m_pFrameYuv)
		{
			m_pFrameYuv = av_frame_alloc();
			m_pFrameYuv->format = AV_PIX_FMT_YUV420P;
			m_pFrameYuv->width = _nVideoOutW;
			m_pFrameYuv->height = _nVideoOutH;
			m_pFrameYuv->pts = 0;
			int ret = av_frame_get_buffer(m_pFrameYuv, 32);
			if (ret != 0)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_frame_get_buffer failed!");
				return -1;
			}
		}

		return 0;
	}

	int CAyjMp4Writer::InitInData(unsigned char* _srcData, int _nSrcFormat, int _nSrcW, int _nSrcH)
	{
		if (nullptr == _srcData)
			return -1;

		if (m_pFrameIn != nullptr && (m_pFrameIn->width != _nSrcW || m_pFrameIn->height != _nSrcH))
		{
			av_frame_free(&m_pFrameIn);
		}
		if (m_pFrameIn == nullptr)
		{
			m_pFrameIn = av_frame_alloc();
			m_pFrameIn->format = _nSrcFormat;
			m_pFrameIn->width = _nSrcW;
			m_pFrameIn->height = _nSrcH;
			int nRet = av_frame_get_buffer(m_pFrameIn, 32);
		}

		if (nullptr != m_pFrameIn)
		{
			if (AV_PIX_FMT_YUV420P == _nSrcFormat)
			{
				av_frame_make_writable(m_pFrameIn);

				av_image_copy_plane(m_pFrameIn->data[0], m_pFrameIn->linesize[0], _srcData, _nSrcW, _nSrcW, _nSrcH);
				av_image_copy_plane(m_pFrameIn->data[1], m_pFrameIn->linesize[1], _srcData + _nSrcW * _nSrcH, _nSrcW / 2, _nSrcW / 2, _nSrcH / 2);
				av_image_copy_plane(m_pFrameIn->data[2], m_pFrameIn->linesize[2], _srcData + _nSrcW * _nSrcH + _nSrcW / 2 * (_nSrcH / 2), _nSrcW / 2, _nSrcW / 2, _nSrcH / 2);
			}
			else if (AV_PIX_FMT_RGB24 == _nSrcFormat ||
				AV_PIX_FMT_BGR24 == _nSrcFormat ||
				AV_PIX_FMT_BGR32 == _nSrcFormat ||
				AV_PIX_FMT_RGB32 == _nSrcFormat ||
				AV_PIX_FMT_ARGB == _nSrcFormat ||
				AV_PIX_FMT_ABGR == _nSrcFormat ||
				AV_PIX_FMT_RGBA == _nSrcFormat ||
				AV_PIX_FMT_BGRA == _nSrcFormat ||
				AV_PIX_FMT_YUYV422 == _nSrcFormat)
			{
				int nRet = av_image_fill_arrays(m_pFrameIn->data, m_pFrameIn->linesize, _srcData, (AVPixelFormat)_nSrcFormat, _nSrcW, _nSrcH, 1);
			}
			else
				return -1;
		}

		return 0;
	}

	int CAyjMp4Writer::Mp4WriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool _bFlagH264)
	{
		if (_szSavePath == nullptr)
			return -1;
		
		Mp4WriteClose();

		CAutoLock lockTemp(&m_lockWrite);

		//封装文件输出上下文
		int nRet = avformat_alloc_output_context2(&m_pFormatCnt, nullptr, nullptr, _szSavePath);
		if (!m_pFormatCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"Mp4WriteInit error,avformat_alloc_output_context2 nRet = %d", nRet);
			return nRet;
		}

		m_strMp4Path = _szSavePath;

		nRet = AddVideoStream(_nVdieoW, _nVideoH, _nGopSize, _nBitrate, _fFps, _bFlagH264);

		if (!(m_pFormatCnt->oformat->flags & AVFMT_NOFILE))
		{
			nRet = avio_open2(&m_pFormatCnt->pb, m_strMp4Path.c_str(), AVIO_FLAG_READ_WRITE, nullptr, nullptr);
			if (nRet < 0)
				return nRet;
		}

		if (nRet == 0)
		{
			m_nVideoOutW = _nVdieoW;
			m_nVideoOutH = _nVideoH;
			m_fFps = _fFps;

			m_bIsInit = true;

			WriteHead();
		}

		return nRet;
	}

	int CAyjMp4Writer::Mp4Write(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts)
	{
		CAutoLock lockTemp(&m_lockWrite);

		if (false == m_bIsInit)
			return -1;
		
		int nRet = InitSws(_nFormate, _nW, _nH, m_nVideoOutW, m_nVideoOutH);
		if (nRet != 0)
			return -1;

		CRunTime objRunTime;
		
		if (!m_pFormatCnt || !m_pCodecCnt || !m_pFrameYuv)
			return -1;

		InitInData(_ucData, _nFormate, _nW, _nH);

		//rgb to yuv
		int nH = sws_scale(m_pSwsCnt, m_pFrameIn->data, m_pFrameIn->linesize, 0, m_pFrameIn->height, m_pFrameYuv->data, m_pFrameYuv->linesize);
		if (nH < 0)
			return -1;
		//cout << h << "|";
		m_pFrameYuv->pts = (int)m_llWritePts++;

		//m_llWritePts = _llPts;
		//LPM::SavePic(m_pFrameYuv, "D://Pic//Ttt.png");
		//LPM::SavePicEx(pData, 3,1920,1080, "D://Pic//Ttt.png");
		//encode
		int ret = avcodec_send_frame(m_pCodecCnt, m_pFrameYuv);
		if (ret != 0)
			return -1;
		if (m_pPacketEncode  == nullptr)
			m_pPacketEncode = av_packet_alloc();
		
		av_init_packet(m_pPacketEncode);

		while ( ret>=0 )
		{
			ret = avcodec_receive_packet(m_pCodecCnt, m_pPacketEncode);
			if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
				return 1;

			m_pPacketEncode->stream_index = m_pVideoStream->index;
			WriteFrame(m_pPacketEncode);
		}

		return 0;
	}

	bool CAyjMp4Writer::WriteHead()
	{
		//打开io
		int nRet = avio_open(&m_pFormatCnt->pb, m_strMp4Path.c_str(), AVIO_FLAG_WRITE);
		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avio_open failed!");
			return false;
		}
		//写入封装头
		nRet = avformat_write_header(m_pFormatCnt, nullptr);
		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError,u8"avformat_write_header failed!");
			return false;
		}

		return true;
	}

	bool CAyjMp4Writer::WriteEnd()
	{
		if (!m_pFormatCnt || !m_pFormatCnt->pb ) 
			return false;

		int nRet = avcodec_send_frame(m_pCodecCnt, nullptr);

		while (nRet >= 0 && m_pPacketEncode != nullptr)
		{
			nRet = avcodec_receive_packet(m_pCodecCnt, m_pPacketEncode);

			/*if (nRet == AVERROR(EAGAIN) || nRet == AVERROR_EOF)
				return 0;*/

			if (nRet != 0 || m_pPacketEncode->size <= 0)
				break;

			m_pPacketEncode->stream_index = m_pVideoStream->index;
			//m_pPacketEncode->pts = (int)m_llWritePts++;
			WriteFrame(m_pPacketEncode);
		}

		
		//写入尾部信息索引
		if (av_write_trailer(m_pFormatCnt) != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"av_write_trailer failed!");
			return false;
		}
			
		return true;
	}

	bool CAyjMp4Writer::WriteFrame(AVPacket* _pPkt)
	{
		if (!m_pFormatCnt || !_pPkt || _pPkt->size <= 0) 
			return false;

		//将CodecCtx 的pts转换成AVStream的pts ,这个是对的
		av_packet_rescale_ts(_pPkt, m_pCodecCnt->time_base, m_pVideoStream->time_base);

		_pPkt->pos = -1;
		_pPkt->stream_index = 0;
		//av_write_frame
		if (av_interleaved_write_frame(m_pFormatCnt, _pPkt) != 0)
			return false;

		av_packet_unref(_pPkt);

		return true;
	}

	int CAyjMp4Writer::Mp4WriteClose(void)
	{
		CAutoLock lockTemp(&m_lockWrite);
		
		if (!m_pFormatCnt)
			return -1;

		WriteEnd();

		if (m_pSwsCnt)
		{
			sws_freeContext(m_pSwsCnt);
			m_pSwsCnt = nullptr;
		}

		if (m_pFrameIn)
			av_frame_free(&m_pFrameIn);

		if (m_pFrameYuv)
			av_frame_free(&m_pFrameYuv);

		if (m_pCodecCnt)
		{
			avcodec_close(m_pCodecCnt);
			avcodec_free_context(&m_pCodecCnt);
		}
		if (m_pVideoStream)
		{
			avcodec_close(m_pVideoStream->codec);
			//avcodec_close() and avformat_free_context()
		}

		if (m_pPacketEncode)
			av_packet_free(&m_pPacketEncode);

		if (m_pFormatCnt)
		{
			if (m_pFormatCnt && !(m_pFormatCnt->oformat->flags & AVFMT_NOFILE))
				avio_close(m_pFormatCnt->pb);
			avformat_free_context(m_pFormatCnt);
			//avformat_close_input(&m_pFormatCnt);
			//avformat_close_output();
		}
		

			
		m_pVideoStream = nullptr;
		m_pFormatCnt = nullptr;

		m_strMp4Path = "";

		m_llWritePts = 0;
		m_bIsInit = false;

		return 0;
	}

	long long CAyjMp4Writer::Mp4WriteGetTimes(void)
	{
		return m_llWritePts*(1000/m_fFps);
	}

	IAyjMp4Writer* IAyjMp4Writer::Create()
	{
		IAyjMp4Writer* pInstance = new CAyjMp4Writer();

		return pInstance;
	}

	void IAyjMp4Writer::Release(IAyjMp4Writer*& _pObj)
	{
		if (_pObj)
		{
			CAyjMp4Writer* pInstance = (CAyjMp4Writer*)_pObj;
			delete pInstance;
			_pObj = nullptr;
		}
	}
    
}