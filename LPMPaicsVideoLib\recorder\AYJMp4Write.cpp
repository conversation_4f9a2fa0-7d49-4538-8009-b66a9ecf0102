﻿/*
 * Filename:  AYJMp4Write.cpp
 * Project :  LPMPaicsVideoLib
 * Created by <PERSON><PERSON> on 7/2/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 *
 * Description: MP4视频文件写入器实现
 *              基于FFmpeg库的高性能H264编码和MP4封装实现
 *              具备完善的错误处理、内存管理和性能优化
 */

#include "AYJMp4Write.h"
#include "ImageScale/ImageScale.h"

namespace AYJ
{
	// 构造函数 - 初始化所有成员变量为默认值
	CAyjMp4Writer::CAyjMp4Writer()
	{
		// 所有指针成员已在头文件中初始化为nullptr
		// 其他成员变量已设置默认值
	}

	// 析构函数 - 确保资源正确释放
	CAyjMp4Writer::~CAyjMp4Writer()
	{
		Mp4WriteClose();  // 自动释放所有FFmpeg资源
	}

	// AddVideoStream 添加视频流到MP4输出上下文
	int CAyjMp4Writer::AddVideoStream(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264)
	{
		// 1. 查找H264编码器
		AVCodec *pCodec = avcodec_find_encoder(AV_CODEC_ID_H264);
		if (!pCodec)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_find_encoder AV_CODEC_ID_H264 failed!");
			return -1;
		}

		// 2. 分配编码器上下文
		m_pCodecCnt = avcodec_alloc_context3(pCodec);
		if (!m_pCodecCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_alloc_context3 failed!");
			return -1;
		}

		// 3. 设置编码参数
		m_pCodecCnt->bit_rate = _nBitrate;           // 比特率
		m_pCodecCnt->width = _nVdieoW;               // 视频宽度
		m_pCodecCnt->height = _nVideoH;              // 视频高度
		m_pCodecCnt->time_base = { 1, (int)_fFps };  // 时间基数
		m_pCodecCnt->framerate = { (int)_fFps, 1 };  // 帧率
		m_pCodecCnt->gop_size = _nGopSize;           // GOP大小（关键帧间隔）
		m_pCodecCnt->max_b_frames = 0;               // B帧数量
		m_pCodecCnt->pix_fmt = AV_PIX_FMT_YUV420P;   // 像素格式
		m_pCodecCnt->codec_id = AV_CODEC_ID_H264;    // 编码器ID
		m_pCodecCnt->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;

		// 4. 设置编码器参数字典
		AVDictionary *pParam = nullptr;
		av_dict_set(&pParam, "x264-params", "crf=18", 0);  // 质量参数
		av_dict_set(&pParam, "preset", "fast", 0);         // 编码速度预设

		// 5. 打开编码器
		int nRet = avcodec_open2(m_pCodecCnt, pCodec, &pParam);

		// 6. 释放参数字典（修复内存泄漏）
		av_dict_free(&pParam);

		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_open2 failed! ret=%d", nRet);
			return -1;
		}

		// 7. 创建视频流
		m_pVideoStream = avformat_new_stream(m_pFormatCnt, nullptr);
		if (nullptr == m_pVideoStream)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avformat_new_stream failed!");
			return -1;
		}

		// 8. 设置流参数
		m_pVideoStream->codecpar->codec_tag = 0;
		m_pVideoStream->id = m_pFormatCnt->nb_streams - 1;
		avcodec_parameters_from_context(m_pVideoStream->codecpar, m_pCodecCnt);

		// 9. 输出格式信息（调试用）
		av_dump_format(m_pFormatCnt, 0, m_strMp4Path.c_str(), 1);

		return 0;
	}

	// InitSws 初始化像素格式转换上下文（性能优化版本）
	int CAyjMp4Writer::InitSws(int _nVideoFormat, int _nVideoInW, int _nVideoInH, int _nVideoOutW, int _nVideoOutH)
	{
		// 1. 检查是否需要重新初始化（避免不必要的重建）
		if (m_pSwsCnt &&
			_nVideoFormat == m_nVideoFormatIn &&
			_nVideoInW == m_nVideoInW &&
			_nVideoInH == m_nVideoInH &&
			_nVideoOutW == m_nVideoOutW &&
			_nVideoOutH == m_nVideoOutH)
		{
			return 0;  // 参数未变化，直接返回
		}

		// 2. 释放旧的转换上下文
		if (m_pSwsCnt)
		{
			sws_freeContext(m_pSwsCnt);
			m_pSwsCnt = nullptr;
		}

		// 3. 创建新的像素格式转换上下文（使用更快的算法）
		int swsFlags = SWS_FAST_BILINEAR;  // 性能优化：使用更快的双线性插值
		if (_nVideoInW == _nVideoOutW && _nVideoInH == _nVideoOutH)
		{
			swsFlags = SWS_POINT;  // 如果尺寸相同，使用最快的点采样
		}

		m_pSwsCnt = sws_getContext(
			_nVideoInW, _nVideoInH, (AVPixelFormat)_nVideoFormat,
			_nVideoOutW, _nVideoOutH, AV_PIX_FMT_YUV420P,
			swsFlags, nullptr, nullptr, nullptr);

		if (!m_pSwsCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"sws_getContext failed! in:%dx%d fmt:%d out:%dx%d",
				_nVideoInW, _nVideoInH, _nVideoFormat, _nVideoOutW, _nVideoOutH);
			return -1;
		}

		// 4. 更新缓存的参数
		m_nVideoFormatIn = _nVideoFormat;
		m_nVideoInW = _nVideoInW;
		m_nVideoInH = _nVideoInH;

		// 5. 初始化输出YUV帧（仅在需要时重新分配）
		if (!m_pFrameYuv || m_pFrameYuv->width != _nVideoOutW || m_pFrameYuv->height != _nVideoOutH)
		{
			if (m_pFrameYuv)
				av_frame_free(&m_pFrameYuv);

			m_pFrameYuv = av_frame_alloc();
			if (!m_pFrameYuv)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_frame_alloc failed for YUV frame!");
				return -1;
			}

			m_pFrameYuv->format = AV_PIX_FMT_YUV420P;
			m_pFrameYuv->width = _nVideoOutW;
			m_pFrameYuv->height = _nVideoOutH;
			m_pFrameYuv->pts = 0;

			int ret = av_frame_get_buffer(m_pFrameYuv, 32);
			if (ret != 0)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_frame_get_buffer failed! ret=%d", ret);
				av_frame_free(&m_pFrameYuv);
				return -1;
			}
		}

		return 0;
	}

	// InitInData 初始化输入数据帧
	int CAyjMp4Writer::InitInData(unsigned char* _srcData, int _nSrcFormat, int _nSrcW, int _nSrcH)
	{
		// 1. 参数有效性检查
		if (nullptr == _srcData)
			return -1;

		// 2. 检查是否需要重新分配帧缓冲区
		if (m_pFrameIn != nullptr && (m_pFrameIn->width != _nSrcW || m_pFrameIn->height != _nSrcH || m_pFrameIn->format != _nSrcFormat))
		{
			av_frame_free(&m_pFrameIn);
		}

		// 3. 分配新的输入帧
		if (m_pFrameIn == nullptr)
		{
			m_pFrameIn = av_frame_alloc();
			if (!m_pFrameIn)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_frame_alloc failed for input frame!");
				return -1;
			}

			m_pFrameIn->format = _nSrcFormat;
			m_pFrameIn->width = _nSrcW;
			m_pFrameIn->height = _nSrcH;

			int nRet = av_frame_get_buffer(m_pFrameIn, 32);
			if (nRet < 0)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_frame_get_buffer failed! ret=%d", nRet);
				av_frame_free(&m_pFrameIn);
				return -1;
			}
		}

		// 4. 根据像素格式填充数据
		if (nullptr != m_pFrameIn)
		{
			if (AV_PIX_FMT_YUV420P == _nSrcFormat)
			{
				// 4.1 YUV420P格式数据拷贝
				av_frame_make_writable(m_pFrameIn);

				// Y平面
				av_image_copy_plane(m_pFrameIn->data[0], m_pFrameIn->linesize[0], _srcData, _nSrcW, _nSrcW, _nSrcH);
				// U平面
				av_image_copy_plane(m_pFrameIn->data[1], m_pFrameIn->linesize[1], _srcData + _nSrcW * _nSrcH, _nSrcW / 2, _nSrcW / 2, _nSrcH / 2);
				// V平面
				av_image_copy_plane(m_pFrameIn->data[2], m_pFrameIn->linesize[2], _srcData + _nSrcW * _nSrcH + _nSrcW / 2 * (_nSrcH / 2), _nSrcW / 2, _nSrcW / 2, _nSrcH / 2);
			}
			else if (AV_PIX_FMT_RGB24 == _nSrcFormat ||
				AV_PIX_FMT_BGR24 == _nSrcFormat ||
				AV_PIX_FMT_BGR32 == _nSrcFormat ||
				AV_PIX_FMT_RGB32 == _nSrcFormat ||
				AV_PIX_FMT_ARGB == _nSrcFormat ||
				AV_PIX_FMT_ABGR == _nSrcFormat ||
				AV_PIX_FMT_RGBA == _nSrcFormat ||
				AV_PIX_FMT_BGRA == _nSrcFormat ||
				AV_PIX_FMT_YUYV422 == _nSrcFormat)
			{
				// 4.2 RGB/BGR等格式数据填充
				int nRet = av_image_fill_arrays(m_pFrameIn->data, m_pFrameIn->linesize, _srcData, (AVPixelFormat)_nSrcFormat, _nSrcW, _nSrcH, 1);
				if (nRet < 0)
				{
					WriteLogEx(ModuleVideo, LogError, u8"av_image_fill_arrays failed! ret=%d", nRet);
					return -1;
				}
			}
			else
			{
				WriteLogEx(ModuleVideo, LogError, u8"Unsupported pixel format: %d", _nSrcFormat);
				return -1;
			}
		}

		return 0;
	}

	// Mp4WriteInit 初始化MP4写入器
	int CAyjMp4Writer::Mp4WriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool _bFlagH264)
	{
		// 1. 参数有效性检查
		if (_szSavePath == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, u8"Mp4WriteInit failed: save path is null!");
			return -1;
		}

		// 2. 清理之前的状态（确保干净的初始化环境）
		Mp4WriteClose();

		CAutoLock lockTemp(&m_lockWrite);

		// 3. 创建MP4输出格式上下文
		int nRet = avformat_alloc_output_context2(&m_pFormatCnt, nullptr, nullptr, _szSavePath);
		if (!m_pFormatCnt)
		{
			WriteLogEx(ModuleVideo, LogError, u8"Mp4WriteInit error: avformat_alloc_output_context2 failed! ret=%d", nRet);
			return nRet;
		}

		// 4. 保存文件路径
		m_strMp4Path = _szSavePath;

		// 5. 添加视频流并配置编码器
		nRet = AddVideoStream(_nVdieoW, _nVideoH, _nGopSize, _nBitrate, _fFps, _bFlagH264);
		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"AddVideoStream failed! ret=%d", nRet);
			return nRet;
		}

		// 6. 打开输出文件（仅当格式需要文件时）
		if (!(m_pFormatCnt->oformat->flags & AVFMT_NOFILE))
		{
			nRet = avio_open2(&m_pFormatCnt->pb, m_strMp4Path.c_str(), AVIO_FLAG_WRITE, nullptr, nullptr);
			if (nRet < 0)
			{
				WriteLogEx(ModuleVideo, LogError, u8"avio_open2 failed! ret=%d path=%s", nRet, _szSavePath);
				return nRet;
			}
		}

		// 7. 设置输出参数并标记初始化完成
		if (nRet == 0)
		{
			m_nVideoOutW = _nVdieoW;
			m_nVideoOutH = _nVideoH;
			m_fFps = _fFps;
			m_bIsInit = true;

			// 8. 写入MP4文件头
			if (!WriteHead())
			{
				WriteLogEx(ModuleVideo, LogError, u8"WriteHead failed!");
				return -1;
			}
		}

		return nRet;
	}

	// Mp4Write 写入视频帧数据（性能优化版本）
	int CAyjMp4Writer::Mp4Write(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts)
	{
		CAutoLock lockTemp(&m_lockWrite);

		// 1. 状态检查
		if (false == m_bIsInit)
			return -1;

		if (!_ucData || !m_pFormatCnt || !m_pCodecCnt)
			return -1;

		// 2. 初始化像素格式转换器（仅在参数变化时重新初始化）
		int nRet = InitSws(_nFormate, _nW, _nH, m_nVideoOutW, m_nVideoOutH);
		if (nRet != 0)
			return -1;

		if (!m_pFrameYuv)
			return -1;

		// 3. 初始化输入数据帧
		nRet = InitInData(_ucData, _nFormate, _nW, _nH);
		if (nRet != 0)
			return -1;

		// 4. 像素格式转换（RGB/BGR to YUV420P）
		int nScaledHeight = sws_scale(m_pSwsCnt,
			m_pFrameIn->data, m_pFrameIn->linesize,
			0, m_pFrameIn->height,
			m_pFrameYuv->data, m_pFrameYuv->linesize);

		if (nScaledHeight < 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"sws_scale failed! ret=%d", nScaledHeight);
			return -1;
		}

		// 5. 设置帧时间戳
		m_pFrameYuv->pts = m_llWritePts++;

		// 6. 发送帧到编码器
		int ret = avcodec_send_frame(m_pCodecCnt, m_pFrameYuv);
		if (ret != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avcodec_send_frame failed! ret=%d", ret);
			return -1;
		}

		// 7. 确保数据包已分配（避免重复分配）
		if (m_pPacketEncode == nullptr)
		{
			m_pPacketEncode = av_packet_alloc();
			if (!m_pPacketEncode)
			{
				WriteLogEx(ModuleVideo, LogError, u8"av_packet_alloc failed!");
				return -1;
			}
		}

		// 8. 接收编码后的数据包并写入文件
		while (ret >= 0)
		{
			ret = avcodec_receive_packet(m_pCodecCnt, m_pPacketEncode);
			if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF)
				return 1;  // 需要更多输入数据或编码结束

			if (ret < 0)
			{
				WriteLogEx(ModuleVideo, LogError, u8"avcodec_receive_packet failed! ret=%d", ret);
				break;
			}

			m_pPacketEncode->stream_index = m_pVideoStream->index;
			if (!WriteFrame(m_pPacketEncode))
			{
				WriteLogEx(ModuleVideo, LogError, u8"WriteFrame failed!");
				return -1;
			}
		}

		return 0;
	}

	// WriteHead 写入MP4文件头信息
	bool CAyjMp4Writer::WriteHead()
	{
		// 1. 检查文件上下文是否有效
		if (!m_pFormatCnt || !m_pFormatCnt->pb)
		{
			WriteLogEx(ModuleVideo, LogError, u8"WriteHead failed: FormatContext or pb is null!");
			return false;
		}

		// 2. 写入封装头信息到文件
		int nRet = avformat_write_header(m_pFormatCnt, nullptr);
		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"avformat_write_header failed! ret=%d", nRet);
			return false;
		}

		return true;
	}

	// WriteEnd 写入MP4文件尾部信息并刷新编码器
	bool CAyjMp4Writer::WriteEnd()
	{
		// 1. 检查上下文有效性
		if (!m_pFormatCnt || !m_pFormatCnt->pb || !m_pCodecCnt)
			return false;

		// 2. 发送空帧刷新编码器缓冲区
		int nRet = avcodec_send_frame(m_pCodecCnt, nullptr);

		// 3. 接收并写入编码器缓冲区中的剩余数据包
		while (nRet >= 0 && m_pPacketEncode != nullptr)
		{
			nRet = avcodec_receive_packet(m_pCodecCnt, m_pPacketEncode);

			// 检查是否还有数据包
			if (nRet != 0 || m_pPacketEncode->size <= 0)
				break;

			// 设置流索引并写入
			m_pPacketEncode->stream_index = m_pVideoStream->index;
			if (!WriteFrame(m_pPacketEncode))
			{
				WriteLogEx(ModuleVideo, LogError, u8"WriteFrame failed in WriteEnd!");
				return false;
			}
		}

		// 4. 写入MP4文件尾部索引信息
		if (av_write_trailer(m_pFormatCnt) != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"av_write_trailer failed!");
			return false;
		}

		return true;
	}

	// WriteFrame 写入编码后的视频帧到MP4文件
	bool CAyjMp4Writer::WriteFrame(AVPacket* _pPkt)
	{
		// 1. 参数有效性检查
		if (!m_pFormatCnt || !_pPkt || _pPkt->size <= 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"WriteFrame failed: invalid parameters!");
			return false;
		}

		// 2. 时间戳转换（从编码器时间基转换为流时间基）
		av_packet_rescale_ts(_pPkt, m_pCodecCnt->time_base, m_pVideoStream->time_base);

		// 3. 设置数据包属性
		_pPkt->pos = -1;  // 让FFmpeg自动计算文件位置
		_pPkt->stream_index = m_pVideoStream->index;

		// 4. 交错写入数据包到文件（确保正确的时间顺序）
		if (av_interleaved_write_frame(m_pFormatCnt, _pPkt) != 0)
		{
			WriteLogEx(ModuleVideo, LogError, u8"av_interleaved_write_frame failed!");
			return false;
		}

		// 5. 释放数据包引用（避免内存泄漏）
		av_packet_unref(_pPkt);

		return true;
	}

	// Mp4WriteClose 关闭MP4写入器并释放所有资源
	int CAyjMp4Writer::Mp4WriteClose(void)
	{
		CAutoLock lockTemp(&m_lockWrite);

		// 1. 检查是否已经初始化
		if (!m_pFormatCnt)
			return -1;

		// 2. 写入文件尾部信息
		WriteEnd();

		// 3. 释放编码相关资源（按正确顺序）
		if (m_pCodecCnt)
		{
			avcodec_close(m_pCodecCnt);
			avcodec_free_context(&m_pCodecCnt);
		}

		// 4. 释放数据包资源
		if (m_pPacketEncode)
			av_packet_free(&m_pPacketEncode);

		// 5. 释放帧资源
		if (m_pFrameIn)
			av_frame_free(&m_pFrameIn);

		if (m_pFrameYuv)
			av_frame_free(&m_pFrameYuv);

		// 6. 释放像素转换上下文
		if (m_pSwsCnt)
		{
			sws_freeContext(m_pSwsCnt);
			m_pSwsCnt = nullptr;
		}

		// 7. 关闭文件句柄并释放格式上下文（关键修复）
		if (m_pFormatCnt)
		{
			// 确保文件句柄正确关闭
			if (m_pFormatCnt->pb && !(m_pFormatCnt->oformat->flags & AVFMT_NOFILE))
			{
				avio_closep(&m_pFormatCnt->pb);  // 使用avio_closep确保指针置空
			}
			avformat_free_context(m_pFormatCnt);
		}

		// 8. 重置所有指针和状态
		m_pVideoStream = nullptr;
		m_pFormatCnt = nullptr;
		m_strMp4Path = "";
		m_llWritePts = 0;
		m_bIsInit = false;

		return 0;
	}

	// Mp4WriteGetTimes 获取当前写入时间（毫秒）
	long long CAyjMp4Writer::Mp4WriteGetTimes(void)
	{
		if (m_fFps <= 0.0f)
			return 0;

		return static_cast<long long>(m_llWritePts * (1000.0 / m_fFps));
	}

	// Create 创建MP4写入器实例
	IAyjMp4Writer* IAyjMp4Writer::Create()
	{
		IAyjMp4Writer* pInstance = new CAyjMp4Writer();
		return pInstance;
	}

	// Release 安全释放MP4写入器实例
	void IAyjMp4Writer::Release(IAyjMp4Writer*& _pObj)
	{
		if (_pObj)
		{
			CAyjMp4Writer* pInstance = static_cast<CAyjMp4Writer*>(_pObj);
			delete pInstance;
			_pObj = nullptr;
		}
	}
    
}