﻿/*
 * Filename:  flv-encryption.h
 * Project :  libflv
 * Created by <PERSON><PERSON> on 12/06/2020.
 * Copyright © 2020 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef __FLV_ENC_DICTIONARY_H__
#define __FLV_ENC_DICTIONARY_H__
#include "flv-muxer.h"
#include "flv-demuxer.h"
#include <stdio.h>
#include <string.h>

static unsigned char g_key[64] = { 
0x9a,0xca,0x32,0xb6,0x10,0x96,0xd2,0xed,
0xda,0x82,0x35,0x7b,0x37,0xba,0x85,0x34,
0xa3,0xac,0xe4,0x77,0x20,0x3b,0x65,0x8d,
0xa7,0xcf,0x30,0x46,0x2d,0xd7,0x23,0xe9,
0x68,0x30,0x5e,0xd3,0xed,0x0e,0xa4,0x9a,
0xed,0xc6,0xde,0xd7,0x25,0x36,0x31,0x33,
0xbd,0x2e,0xf1,0xcc,0xa8,0xfa,0x0b,0x78,
0xe4,0x11,0x30,0xf6,0x3a,0xe0,0x08,0x06,
};
static int g_key_len = sizeof(g_key);
static const unsigned char dictionary[256] = {
0xB3, 0x03, 0x59, 0xF3, 0x04, 0x22, 0x09, 0x03, 0xB4, 0xB5, 0xA5, 0x68, 0x19, 0x0A, 0x5D, 0x63,
0x01, 0x06, 0x88, 0xBC, 0xA3, 0x55, 0x3B, 0x07, 0xD6, 0xC4, 0x1B, 0x26, 0x81, 0xEA, 0x6F, 0x0F,
0x53, 0x0E, 0x7A, 0xD0, 0xB4, 0x45, 0x07, 0xD0, 0xFB, 0xDB, 0x45, 0x57, 0xE1, 0xF4, 0xD5, 0x50,
0x35, 0x73, 0x22, 0x4E, 0xD2, 0x1B, 0xF1, 0xCE, 0xCF, 0xF4, 0x39, 0xBC, 0xF5, 0xF1, 0x34, 0x38,
0x76, 0xCF, 0xB6, 0x99, 0xDB, 0x40, 0xBE, 0xB2, 0x3F, 0x49, 0x4A, 0x55, 0xBB, 0xE9, 0x70, 0x16,
0x21, 0xFB, 0xAB, 0x51, 0xED, 0x5D, 0x73, 0x6F, 0x78, 0x52, 0x10, 0x64, 0x6F, 0x25, 0xAF, 0x7C,
0x84, 0x10, 0xB6, 0x58, 0x63, 0x5B, 0x55, 0x33, 0xE8, 0xC8, 0x5D, 0x6B, 0x8F, 0x2F, 0x56, 0x3C,
0x2C, 0x66, 0xCC, 0xCE, 0xDC, 0x63, 0xE9, 0x72, 0x3B, 0xA5, 0x49, 0x28, 0xD7, 0xD0, 0x09, 0x66,
0xE5, 0x97, 0x21, 0x14, 0x33, 0xDE, 0xF4, 0xDB, 0x5E, 0x20, 0x26, 0x9F, 0x45, 0x0F, 0xAE, 0x4A,
0xBD, 0x7B, 0x2C, 0xCC, 0x87, 0x75, 0x7C, 0x60, 0x7F, 0xB4, 0x8B, 0x10, 0x15, 0x38, 0x69, 0x7B,
0x01, 0x2C, 0xA0, 0xD6, 0x33, 0x11, 0xC5, 0x31, 0x09, 0x19, 0x4C, 0xFB, 0xC5, 0xD1, 0xA0, 0xC9,
0x3E, 0x03, 0x74, 0x53, 0xD6, 0xDB, 0x53, 0xBF, 0xAB, 0x48, 0x7F, 0x23, 0x12, 0xA5, 0xF8, 0x45,
0x41, 0x99, 0xDB, 0xA5, 0x4B, 0x3C, 0xED, 0xBD, 0x52, 0x7B, 0x78, 0x87, 0xF7, 0xBD, 0x56, 0x40,
0x16, 0xC7, 0x4B, 0x6C, 0xB1, 0xDC, 0x98, 0x1A, 0x29, 0x29, 0xCC, 0x69, 0xB4, 0x61, 0xDE, 0x4B,
0x0B, 0xA5, 0x79, 0x89, 0x63, 0xA6, 0x97, 0x07, 0x9F, 0x0D, 0x51, 0x4A, 0xC4, 0x1B, 0xF5, 0x37,
0xAD, 0x8C, 0x5A, 0x1D, 0x00, 0xC2, 0x70, 0xF6, 0x60, 0x1F, 0x1B, 0xEA, 0xE4, 0xB3, 0x42, 0x15
};

static int set_flv_enc_key(const char * key, int size)
{
    if (NULL == key) {
        return -1;
    }
    memset(g_key, 0, sizeof(g_key));
    g_key_len = size > sizeof(g_key) ? sizeof(g_key) : size;
    for (int i = 0; i < g_key_len; ++i) {
        g_key[i] = dictionary[key[i]];
    }
}

static int flv_encrypt(char * data, int size)
{
    if (NULL == data) {
        return -1;
    }
    for (int i = 0; i < size; ++i) {
        data[i] ^= g_key[i % g_key_len];
    }
    return 0;
}

#endif // !__FLV_ENC_DICTIONARY_H__

