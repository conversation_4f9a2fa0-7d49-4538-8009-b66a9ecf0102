﻿/*
 * Filename:  RecorderFlv.cpp
 * Project :  LMPCore * Created by <PERSON><PERSON> on 7/2/2019. 
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef __AYJ_VIDEO_WRITE_H__
#define __AYJ_VIDEO_WRITE_H__

#include "common/typedefs.h"
#include "IAYJVideoWriter.h"
#include "Encoder/IAYJVideoEncoder.h"
#include "Encoder/AYJVideoEncoder.h"

#include <fstream>

using namespace  LPM;

namespace AYJ 
{
    class CAyjVideoWriter : public IAyjVideoWriter   ,public  IOVideoEncoderNotify
    {
    public:
		CAyjVideoWriter(OnWriterDataCb _pVideoWriterDataFun, void* _pUserData, bool _bNeedLocalSave = true);
        ~CAyjVideoWriter();

		virtual int VideoWriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool _bIsContine = false,  bool bFlagH264 = true) override;

		virtual int VideoWrite(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) override;

		virtual int VideoWriteClose(void) override;

		virtual long long VideoWriteGetTimes(void) override;

		virtual int ClearData(bool _bDelete = false) override;

		virtual int VideoWriteGetFps() override;

		virtual int VideoWriteMoidfyFps(int _nFps) override;

	protected:

		virtual void OnEncoderDataNotify(unsigned char* _ucEncodeData, int _nDataSize, int _nW, int _nH, long long _llWritePts, long long _llFramePts, long long _llInPts, bool _bKeyFrame);

	private:

		int VideoWriteToFile(unsigned char* _ucEncodeData, int _nDataSize, int _nW, int _nH, long long _llWritePts, long long _llFramePts, long long _llInPts,  bool _bKeyFrame);
    
	private:
		bool m_bH264 = true;
		int  m_nGop = 0;
		float m_fFps = 0.0;
		std::wstring m_wstrPath;
		std::ofstream  m_fVideoWrite;
		std::ofstream  m_fVideoDataWrite;
		CAyjVideoEncoder* m_pObjEncoder = nullptr;

		tagVideoData m_objVideoInfo;
		tagVideoDataIndex m_objVideoDataIndex;

		CLock m_lockWrite;

		bool m_bNeedLocalSave = true;	//	是否需要本地存储
		
		void* m_pUserData = nullptr;

		//	编码完成后 ，写入文件的数据
		OnWriterDataCb m_pVideoWriterDataFun = nullptr;
       
    };
} // namespace LPM
#endif // !__RECORDER_FLV_H__
