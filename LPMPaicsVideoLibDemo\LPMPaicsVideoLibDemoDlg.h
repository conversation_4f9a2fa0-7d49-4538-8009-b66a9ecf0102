﻿
// LPMPaicsVideoLibDemoDlg.h: 头文件
//

#pragma once

#include "../LPMPaicsVideoLib/LPMPaicsVideoLib.h"
#include "../Depends/Common/include/LPMCommonLib.h"

#include "../LPMPaicsVideoLib/LPMPaicsVideoLib.h"



#ifdef _DEBUG

#pragma comment(lib, "../x64/debug/LPMPaicsVideoLib.lib")
#pragma comment(lib, "../Depends/Common/lib/LPMCommonLib.lib")


#else

#pragma comment(lib, "../x64/Release/LPMPaicsVideoLib.lib")
#pragma comment(lib, "../Depends/Common/lib/LPMCommonLib.lib")

#endif // _DEBUG




using namespace LPM;
using namespace AYJ;

// CLPMPaicsVideoLibDemoDlg 对话框
class CLPMPaicsVideoLibDemoDlg :
    public CDialogEx,public AYJ::IAyjVideoPlayerCallback, public AYJ::IVideoCaptureCallbackEx
{
    // 构造
public:
    CLPMPaicsVideoLibDemoDlg(CWnd* pParent = nullptr);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
    enum { IDD = IDD_LPMPAICSVIDEOLIBDEMO_DIALOG };
#endif
protected:
    virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持
    
    bool JpegToYuv420p(unsigned char * jpgData, int jpgSize, char * yuvData, int yuvSize);
    void ReleaseVideoCapturer(bool showHit);
    void ReleaseFrame(void);
    void ReleaseRender(void);

    void UpdateFpsAdapter(void);
    void UpdatePlayerPlayTime(int64_t time);
    void UpdatePlayerTotalTime(int64_t time);

protected:

	virtual int OnPlayerStatusCb(enPlayerStatus _enStatus, long long _llCurPts, long long _llVideoLength, void* _pUserData);
	//  播放器显示帧回调
	virtual int OnPlayerFrameCb(tagVdieoFrame* _objFrame, void* _pUserData);

	std::string TimerGetStampEx(unsigned long long _lStamp)
	{
		unsigned long long lTime = _lStamp;

		int ms = lTime % 1000;				//  毫秒
		int secs = (lTime /= 1000) % 60;	//	秒
		int mins = (lTime /= 60) % 60;		//	分
		int hours = (lTime /= 60) % 60;		//	时

		char sz[50] = {};
		sprintf_s(sz, 50, "%02d:%02d:%02d %03d", hours, mins, secs, ms);

		return std::string(sz);
	}
public:
   
	AYJ::IVideoCapturerEx* m_pVideocapturerEx = nullptr;
    

    void*	m_pObjRenderCap = nullptr;
    void*   m_pObjRenderPlay = nullptr;

    void*   m_pObjDecoder = nullptr;

    tagVdieoFrame * m_pRenderFramePaly = nullptr;

    CEdit m_editVideoPath;
    CEdit m_editMp4Path;
    CEdit m_ctrlfFps;
    CButton m_ctrlPlayVideo;
    CStatic m_ctrlCurTime;
    CStatic m_ctrlTimeLength;
    CEdit m_editPlayPath;
    CSliderCtrl m_ctrlPlayStep;
private:
	tagVdieoFrame * m_pFrameBase = nullptr;
	tagVdieoFrame * m_pRenderFrame = nullptr;
    CLock	m_frameMutex;
    CLock   m_apiMutex;
    CMFCButton m_ctrlShowVideo;

	IAyjVideoPlayer* m_pReadAyjVideo = nullptr;
	IAyjCommVideoPlayer* m_pCommVideiPlayer = nullptr;

	IAyjMp4Writer* m_pMp4 = nullptr;
	IAyjVideoWriter* m_pVideoWrite = nullptr;


	virtual int OnFrameCallbackEx(uint32_t _unCapId, double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nW, int _nH, int _nVideoFormate);
	/**
	 * 摄像头状态变化通知回调接口
	 * @param capId 回调采集实例的编号
	 * @param status 当前采集实例的状态
	 */
	virtual int OnCapturerStatusEx(uint32_t _unCapId, AYJ::eCapDevState status);

    // 实现
protected:
    HICON m_hIcon;

    // 生成的消息映射函数
    virtual BOOL OnInitDialog();
    afx_msg void OnPaint();
    afx_msg HCURSOR OnQueryDragIcon();
    DECLARE_MESSAGE_MAP()

private:


public:
    afx_msg void OnBnClickedCancel();
    afx_msg void OnBnClickedOk();
    afx_msg void OnBnClickedBtnInitcapture();

    afx_msg void OnBnClickedBtnStart();
    afx_msg void OnBnClickedBtnStop();
    afx_msg void OnBnClickedBtnReleaseCapturer();
    afx_msg void OnBnClickedBtnBeginflv();
    afx_msg void OnBnClickedBtnEndflv();
    afx_msg void OnBnClickedBtnBeginmp4();
    afx_msg void OnBnClickedBtnEndmp4();

    afx_msg void OnBnClickedBtnPlayfileget();
    afx_msg void OnBnClickedBtnplaystart();
    afx_msg void OnBnClickedBtnplaypause();
    afx_msg void OnBnClickedBtnplaycontinue();
    afx_msg void OnBnClickedBtnplayend();

    afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
    afx_msg void OnBnClickedBtnFps();

    afx_msg LRESULT  OnUpdatePlayerStatus(WPARAM wParam, LPARAM lParam);
    afx_msg LRESULT  OnUpdatePlayingTime(WPARAM wParam, LPARAM lParam);
	CEdit m_editSeekTime;
	afx_msg void OnBnClickedBtngetseekframe();
	afx_msg void OnBnClickedBtnplaycontinue2();
	afx_msg void OnBnClickedBtnplaytime();
	afx_msg void OnBnClickedBtnwritereset();

	bool m_bCaptrueNew = 1;
	CComboBox m_ctrlComboxDevIndex;
	CComboBox m_ctrlComboxPixformat;
	afx_msg void OnSelchangeComboDevindex();

	std::vector<AYJ::tagDevInfo> m_vectDevInfo;
	afx_msg void OnBnClickedBtnPause();
	CStatic m_ctrlCapFps;
	CStatic m_ctrlCaptureStatus;
	afx_msg void OnBnClickedBtngetmic();
	CComboBox m_ctrlMicCombox;
	afx_msg void OnBnClickedBtnreleasemic();
	afx_msg void OnBnClickedBtnstartmic();
	afx_msg void OnBnClickedBtnpausemic();
	afx_msg void OnBnClickedBtninitmic();
	CComboBox m_ctrlComboxMicSupportList;
	afx_msg void OnSelchangeCombomic();
	CStatic m_ctrlPlayerFps;
};
