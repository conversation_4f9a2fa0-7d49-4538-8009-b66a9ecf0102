﻿
// AyjVideoToolsDlg.h: 头文件
//

#pragma once

//#include "../LPMPaicsVideoLib/LPMPaicsVideoLib.h"
#include "../Depends/Common/include/LPMCommonLib.h"
//#include "../LPMPaicsVideoLib/LPMPaicsVideoLib.h"
#include "../LPMPaicsVideoLib/Decoder/IAYJVideoPlayer.h"

#pragma comment(lib, "../x64/debug/LPMPaicsVideoLib.lib")
#pragma comment(lib, "../Depends/Common/bin/x64_debug/LPMCommonLib.lib")

#include "AyjVideoExport.h"

#define UM_UPDATE_PALYER_STATUS (WM_USER+100)
#define UM_UPDATE_PLAY_TIME (WM_USER+101)
#define UM_EXPORT_VIDEO_PRO (WM_USER+102)

// CAyjVideoToolsDlg 对话框
class CAyjVideoToolsDlg : public CDialogEx ,public LPM::IAyjVideoPlayerCallback
{
// 构造
public:
	CAyjVideoToolsDlg(CWnd* pParent = nullptr);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_AYJVIDEOTOOLS_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持

// 实现
protected:
	HICON m_hIcon;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg void OnBnClickedCancel();

	DECLARE_MESSAGE_MAP()

protected:
	//	播放器状态回调
	virtual int OnPlayerStatusCb(LPM::enPlayerStatus _enStatus);
	//  播放器帧回调
	virtual int OnPlayerFrameCb(LPM::FrameBase* _objFrame);

	std::string TimerGetStamp(unsigned long long _lStamp);
	std::string TimerGetStampEx(unsigned long long _lStamp);

private:
	LPM::IAyjVideoPlayer* m_pVideoPlayer = nullptr;
	void*   m_pObjRenderPlay = nullptr;

	int m_nRenderPlayWidth = 1600;
	int n_nRenderPlayHeight = 900;

	int m_nRange = 300;

	long long m_llVideoLength = 0;

public:
	CMFCButton m_ctrlVideoShow;
	CEdit m_editVideoPath;
	afx_msg void OnBnClickedBtnVideoPathGet();
	afx_msg void OnBnClickedBtnVideoInit();
	afx_msg void OnEnChangeEditVideopath();
	CEdit m_editExportPath;
	afx_msg void OnBnClickedBtnVideoExport();
	CSliderCtrl m_ctrlVideoProgress;
	afx_msg void OnBnClickedBtnVideoRelease();
	afx_msg void OnBnClickedBtnVideoPlay();
	afx_msg void OnBnClickedBtnVideoPause();
	afx_msg void OnBnClickedBtnVideoPathGetout();
	afx_msg LRESULT  OnUpdatePlayerStatus(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT  OnUpdatePlayingTime(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT  OnExportProgress(WPARAM wParam, LPARAM lParam);

	CButton m_btnVideoInit;
	CButton m_btnVideoRelease;
	CButton m_btnVideoPlay;
	CButton m_btnVideoPause;
	CButton m_ctrlShowCurTime;
	CButton m_ctrlShowVideoLength;
	CStatic m_ctrlPlayStatus;
	
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);

	//	flv视频写入
	//LPM::IVideoRecorder * m_pFlvVideoRecorder = nullptr;
	CAyjVideoExport* m_pVideoExport = nullptr;
	CStatic m_ctrlExportProgress;
	CProgressCtrl m_ctrlExportPorgress1;
	CStatic m_ctrlExportProgerss2;
	afx_msg void OnBnClickedBtnVideoStopexport();
	CStatic m_ctrlExportPath;
	CButton m_btnExport;
	CButton m_btnStopExport;
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
};
