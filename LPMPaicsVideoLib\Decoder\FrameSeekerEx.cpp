﻿/*
 * Filename:  CFrameSeekerEx.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 9/9/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#include "FrameSeekerEx.h"

extern "C" 
{
#include "libavformat/avformat.h"
#include "libswscale/swscale.h"
#include "libavcodec/avcodec.h"
#include "libavutil/time.h"
}

#include <fstream>

namespace AYJ 
{
	CFrameSeekerEx::CFrameSeekerEx(void)
    {
      
    }

    CFrameSeekerEx::~CFrameSeekerEx(void)
    {
		CAutoLock lockTemp(&m_lock);

		Release();
    }
	
    long long CFrameSeekerEx::FrameInit(const char* _FilePathUtf8)
    {
		std::string strPathTemp(_FilePathUtf8);
		/*int nIndex = strPathTemp.find(".");
		if (nIndex>0)
			strPathTemp = strPathTemp.substr(0, nIndex);*/

		CAutoLock lockTemp(&m_lock);

		if (m_strFilePath == strPathTemp)
			return m_llVideoLength;
		else
			Release();

		m_pPlayer = new CAyjVideoReader(true);
		int nRet = m_pPlayer->PlayerInit(strPathTemp.c_str(), nullptr, nullptr);
		if (nRet < 0)
		{
			Release();
			return -1;
		}

		m_llVideoLength = m_pPlayer->PlayerGetLength();
		m_strFilePath = strPathTemp;

        return m_llVideoLength;
    }

	tagVdieoFrame * CFrameSeekerEx::FrameSeek(long long _llSeekTime)
	{
		CAutoLock lockTemp(&m_lock);

		if (nullptr == m_pPlayer)
			return nullptr;
		
		return m_pPlayer->PlayerSeekFrame(_llSeekTime);
	}

	long long CFrameSeekerEx::FrameGetLength(void)
	{
		return m_llVideoLength;
	}

	void CFrameSeekerEx::Release()
	{
		m_strFilePath = "";
		if (m_pPlayer)
		{
			delete m_pPlayer;
			m_pPlayer = nullptr;
		}
		m_llVideoLength = -1;

	}
	
} // namespace LPM
