﻿/*
******		FileName	:	IAYJVideoEncoder.cpp
******		Describe	:	此文件是c++ 接口,主要负责视频的编码接口
******		Date		:	2021-12-17
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
*/

#include "IAYJVideoEncoder.h"
#include "Encoder/AYJVideoEncoder.h"

namespace AYJ 
{
	IAyjVideoEncoder* IAyjVideoEncoder::Create(IOVideoEncoderNotify* _pEncodeNotify)
	{
		IAyjVideoEncoder* pInstance = new CAyjVideoEncoder(_pEncodeNotify);

		return pInstance;
	}
	void IAyjVideoEncoder::Release(IAyjVideoEncoder*& _pObj)
	{
		if (_pObj)
		{
			IAyjVideoEncoder* pInstance = (IAyjVideoEncoder*)_pObj;
			delete pInstance;
			_pObj = nullptr;
		}
	}    
}