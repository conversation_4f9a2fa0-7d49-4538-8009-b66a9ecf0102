﻿/**
 * windows摄像头影像采集器
 * <AUTHOR>
 */
#ifndef I__AUDIO_CAPTURER_H
#define I__AUDIO_CAPTURER_H

#include "../Common/Typedefs.h"
#include <vector>

#include "IVideoCapturerEx.h"

namespace AYJ 
{
	// 获取麦克风的 的基类
     
    class LPM_VIDEO_API IAudioCapture
    {
	public:
		virtual ~IAudioCapture(void) {}
		
		//	获取视频设备数量
		virtual int CaptureGetDevCount() = 0;
		//	获取视频设备信息
		virtual int CaptureGetDevInfo(const int _nDevIndex, tagDevInfo& _objDevInfo) = 0;

		//	初始化采集设备
		virtual int CaptureInit(const int _nDevIndex, IVideoCaptureCallbackEx* _pCapCallBack) = 0;
		//	释放采集设备
		virtual int CaptureRelease() = 0;

		//	设置设备的参数
		virtual int CaptureDevFormatSet(const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut) = 0;
		//	采集开始
		virtual int CaptureStart() = 0;
		//	采集暂停
		virtual int CapturePause() = 0;
		//	采集停止
		virtual int CaptureStop() = 0;

		//	获取采集状态
		virtual eCapDevState CaptureGetState() = 0;

		//	创建	采集对象
		static IAudioCapture * Create();
		//	释放	采集对象
		static void Release(IAudioCapture*& _pObj);
    };
} // namespace AYJ

#endif // !IVIDOE_CAPTURER_H

