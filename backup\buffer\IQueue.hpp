/*
 * Filename:  IQueue.hpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/16/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/
#ifndef __IQUEUE_H__
#define __IQUEUE_H__

#include <IMutex.h>
#include <ICondition.h>
#include <common/typedefs.h>

#define GET_OBJ_FROM_CACHE(cache, pobj, T)          \
    if (!cache->Pick(pobj)) {                       \
        pobj = new T();                             \
    }                                               \

#define PUT_OBJ_TO_CACHE(cache, pobj)               \
    if (!cache->Push(pobj)) {                       \
        SAFE_DELETE(pobj);                          \
    }                                               \

#define CREATE_CACHE(type, hook)                    \
type::Create(type::Deque, true, false, hook); 

#define CREATE_DEQUE(type, lock, wait, hook)        \
type::Create(type::Deque, lock, wait, hook);

template <typename T>
class IQueue
{
public:
    typedef T value_type;
    typedef void(*MemoryReleaseHook)(value_type & obj);

public:
    enum QueueType
    {
        Deque = 0,
        RingQueue,
    };

public:
    /*
    * capacity only works when you create the ringqueue
    */
    static IQueue<T> * Create(QueueType type = Deque, bool lock = false,
        bool wait = false, MemoryReleaseHook hook = NULL,
        const size_t capacity = 0);

public:
    IQueue(bool lock = false, bool wait = false);
    virtual ~IQueue(void);
    virtual void Start(void) = 0;
    virtual bool Push(T & t) = 0;
    virtual bool PushFront(T & t) = 0;
    virtual bool PopFront(void) = 0;
    virtual bool Empty(void) = 0;
    virtual size_t Size(void) = 0;
    virtual bool Pick(T & t) = 0;
    virtual bool Peek(T & t) = 0;
    virtual void Stop(void) = 0;
    virtual void Clear(void) = 0;

protected:
    bool Lock(void);
    bool Unlock(void);
    bool Wait(void);
    bool Notify(void);
    bool NotifyAll(void);

protected:
    const bool  m_isLock;
    bool        m_isWait;
    IMutex *    m_lock;
    ICondition* m_condition;
};

#include <buffer/DequeQueue.hpp>

template <typename T>
IQueue<T> * IQueue<T>::Create(QueueType type, bool lock, bool wait,
    MemoryReleaseHook hook, const size_t capacity)
{
    IQueue<T> * pQueue = NULL;

    switch (type) {
    case Deque:
        pQueue = new DequeQueue<T>(lock, wait, hook);
        break;
    case RingQueue:
        break;
    default:
        break;
    }
    return pQueue;
}

template <typename T>
IQueue<T>::IQueue(bool lock, bool wait)
    : m_isLock(lock)
    , m_isWait(wait)
    , m_lock(NULL)
    , m_condition(NULL)
{
    if (lock) {
        m_lock = IMutex::Create();
        if (wait) {
            m_condition = ICondition::Create(m_lock);
        }
    } else {
        m_isWait = false;
    }
}

template <typename T>
IQueue<T>::~IQueue(void)
{
    SAFE_DELETE(m_lock);
    SAFE_DELETE(m_condition);
}

template <typename T>
inline bool IQueue<T>::Lock(void)
{
    if (m_lock) {
        m_lock->Lock();
    }
    return m_lock;
}

template <typename T>
inline bool IQueue<T>::Unlock(void)
{
    if (m_lock) {
        m_lock->Unlock();
    }
    return m_lock;
}

template <typename T>
inline bool IQueue<T>::Wait(void)
{
    if (m_isWait) {
        m_condition->Sleep();
    }
    return m_isWait;
}

template <typename T>
inline bool IQueue<T>::Notify(void)
{
    if (m_isWait) {
        m_condition->Wake();
    }
    return m_isWait;
}

template <typename T>
inline bool IQueue<T>::NotifyAll(void)
{
    if (m_isWait) {
        m_condition->WakeAll();
    }
    return m_isWait;
}
#endif
