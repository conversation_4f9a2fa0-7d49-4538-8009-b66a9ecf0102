﻿#pragma once

#include "../LPMPaicsVideoLib.h"

#include <stdint.h>

struct AVFrame;
struct SwsContext;

namespace AYJ 
{
    LPM_VIDEO_API int I420Scale(const uint8_t* srcI420, int srcw, int srch,
		uint8_t* dstI420, int dstw, int dsth);

    LPM_VIDEO_API int I420ToBGR24(const uint8_t* src, uint8_t* dst, int w, int h);
	

	LPM_VIDEO_API int YUY2ToI420(const uint8_t* srcYUY2, uint8_t* dstI420, int w, int h);

	LPM_VIDEO_API int NV12ToYUV420P(void* nv12, void* yuv, int w, int h);

	LPM_VIDEO_API int RGB24ToYUV420P(void* rgb, void* yuv, int w, int h);
	LPM_VIDEO_API int BGR24ToYUV420P(void* bgr, void* yuv, int w, int h);

    LPM_VIDEO_API int BGR24ToRGB24(uint8_t * srcRGB, int w, int h);
	
	LPM_VIDEO_API int RotateBGR(void *src, int src_w, int src_h,
		void *dst, int dst_w, int dst_h,
		int rotation_mode);

	//	裁剪
	LPM_VIDEO_API int BGR24Crop(uint8_t*_pSrc, int _nSizeSrc, int _nSrcW, int _nSrcH,
		uint8_t*& _pDest, int _nCropX, int _nCropY, int _nDestW, int _nDestH);

	LPM_VIDEO_API int Yuv420pCrop(uint8_t *_pSrc, int _nSrcW, int _nSrcH, 
		uint8_t* _pDest, int _nCropX, int _nCropY, int _nDestW, int _nDestH);

	LPM_VIDEO_API int FillBlackBorder(uint8_t * src, uint32_t srcw, uint32_t srch,
		uint8_t * dst, uint32_t dstw, uint32_t dsth);

	/*
	支持的输入格式
	AV_PIX_FMT_YUV420P		0
	AV_PIX_FMT_YUYV422		1
	AV_PIX_FMT_RGB24		2
	AV_PIX_FMT_BGR24		3
	AV_PIX_FMT_BGR32
	AV_PIX_FMT_RGB32
	AV_PIX_FMT_ARGB
	AV_PIX_FMT_ABGR
	AV_PIX_FMT_RGBA
	AV_PIX_FMT_BGRA
	AV_PIX_FMT_YUYV422
	支持的输出格式  AV_PIX_FMT_YUV420P AV_PIX_FMT_RGB24 AV_PIX_FMT_BGR24

	return > 0 ，为成功
	*/
	

	LPM_VIDEO_API int ImageConvertEx(AVFrame* _pFrameIn, uint8_t*& _szDstData, int _nDstFmt, int _nDstW, int _nDstH,const char* _pSavePicPath = nullptr);

	LPM_VIDEO_API bool  SavePic(AVFrame *_pFrameSrc, const char* _pFileName);
	LPM_VIDEO_API bool  SavePicEx(uint8_t* _srcData, int _nSrcFormat, int _nSrcW, int _nSrcH, const char* _pFileName);


	class LPM_VIDEO_API CImageConvert
	{
	public:
		CImageConvert();
		~CImageConvert();

		int ImageConvert(uint8_t* _srcData, int _nSrcFormat, int _nSrcW, int _nSrcH, uint8_t*& _szDstData, int _nDstFmt, int _nDstW, int _nDstH, char* _pSavePicPath = nullptr);

	private:
		AVFrame* m_pFrameIn = nullptr;
		AVFrame* m_pFrameOut = nullptr;
		SwsContext* m_pImgConvertCtx = nullptr;
	};


	
} 


