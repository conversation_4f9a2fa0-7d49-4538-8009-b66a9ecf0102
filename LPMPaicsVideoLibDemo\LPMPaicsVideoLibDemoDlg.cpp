﻿
// LPMPaicsVideoLibDemoDlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "LPMPaicsVideoLibDemo.h"
#include "LPMPaicsVideoLibDemoDlg.h"
#include "afxdialogex.h"
#include "../LPMPaicsVideoLib/ImageScale/ImageScale.h"
#include "../LPMPaicsVideoLib/VideoInterface.h"

#include <winuser.h>
#include <windows.h>
#include<DbgHelp.h>  
#pragma comment(lib,"DbgHelp.lib")  

#//include "opencv\cv.h"
#include "opencv2\opencv.hpp"
#include "opencv2/imgproc/types_c.h"
//#include "opencv\highgui.h"

extern "C" {
#include "../Depends/FFmpeg-4.0/include/libavcodec/avcodec.h"
#include "../Depends/FFmpeg-4.0/include/libavformat/avformat.h"
#include "../Depends/FFmpeg-4.0/include/libavutil/avutil.h"
#include "../Depends/FFmpeg-4.0/include/libavutil/frame.h"
}
#pragma comment(lib, "../Depends/FFmpeg-4.0/lib/avcodec.lib")
#pragma comment(lib, "../Depends/FFmpeg-4.0/lib/avutil.lib")

#ifdef _DEBUG
#define DBG_NEW new ( _NORMAL_BLOCK , __FILE__ , __LINE__ )
// Replace _NORMAL_BLOCK with _CLIENT_BLOCK if you want the
// allocations to be of _CLIENT_BLOCK type
#else
#define DBG_NEW new
#endif

#define UM_UPDATE_PALYER_STATUS (WM_USER+100)
#define UM_UPDATE_PLAY_TIME (WM_USER+101)

#ifdef _DEBUG
#pragma  comment(lib , "../Depends/OpenCv/lib/opencv_world480d.lib")
#else
#pragma  comment(lib , "../Depends/OpenCv/lib/opencv_world480.lib")
#endif
//using namespace cv;

// 创建Dump文件  
void CreateDumpFile(LPCSTR lpstrDumpFilePathName, EXCEPTION_POINTERS *pException)
{
    HANDLE hDumpFile = CreateFileA(lpstrDumpFilePathName, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    // Dump信息  
    MINIDUMP_EXCEPTION_INFORMATION dumpInfo;
    dumpInfo.ExceptionPointers = pException;
    dumpInfo.ThreadId = GetCurrentThreadId();
    dumpInfo.ClientPointers = TRUE;
    // 写入Dump文件内容  
    MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hDumpFile, MiniDumpNormal, &dumpInfo, NULL, NULL);
    CloseHandle(hDumpFile);
}
// 处理Unhandled Exception的回调函数  
LONG ApplicationCrashHandler(EXCEPTION_POINTERS *pException)
{
    time_t curTime;
    time(&curTime); // UTC时间
    struct tm *p = new struct tm;
    gmtime_s(p, &curTime);
    char filename[64] = { 0 };
    sprintf_s(filename, "Demo-%d%d%d%d%d%d.dmp", 1900 + p->tm_year, 1 + p->tm_mon,
        p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    localtime_s(p,&curTime); // 转为当前时区时间
    CreateDumpFile(filename, pException);
    delete p;
    return EXCEPTION_EXECUTE_HANDLER;
}

CLPMPaicsVideoLibDemoDlg::CLPMPaicsVideoLibDemoDlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_LPMPAICSVIDEOLIBDEMO_DIALOG, pParent)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CLPMPaicsVideoLibDemoDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, ID_BtnShowVideo, m_ctrlShowVideo);
	DDX_Control(pDX, IDC_Editor_mp4_path, m_editVideoPath);
	DDX_Control(pDX, IDC_Editor_Flv_path2, m_editMp4Path);
	DDX_Control(pDX, ID_BtnPlayVideo, m_ctrlPlayVideo);
	DDX_Control(pDX, IDC_STATIC_CurTime, m_ctrlCurTime);
	DDX_Control(pDX, IDC_STATIC_TimeLength, m_ctrlTimeLength);
	DDX_Control(pDX, IDC_Editor_PlayPath, m_editPlayPath);
	DDX_Control(pDX, IDC_SLIDER_PlayStep, m_ctrlPlayStep);
	DDX_Control(pDX, IDC_Editor_Fps, m_ctrlfFps);

	DDX_Control(pDX, IDC_Edit_Seek, m_editSeekTime);
	DDX_Control(pDX, IDC_COMBO_DevIndex, m_ctrlComboxDevIndex);
	DDX_Control(pDX, IDC_COMBOFixfamate, m_ctrlComboxPixformat);
	DDX_Control(pDX, IDC_STATIC_CAP_STATUS, m_ctrlCapFps);
	DDX_Control(pDX, IDC_STATIC_captureStatus, m_ctrlCaptureStatus);
	DDX_Control(pDX, IDC_COMBOMic, m_ctrlMicCombox);
	DDX_Control(pDX, IDC_COMBOMicSupport, m_ctrlComboxMicSupportList);
	DDX_Control(pDX, IDC_STATIC_Player_Fps, m_ctrlPlayerFps);
}



//读取数据的回调函数-------------------------
//AVIOContext使用的回调函数！
//注意：返回值是读取的字节数
//手动初始化AVIOContext只需要两个东西：内容来源的buffer，和读取这个Buffer到FFmpeg中的函数
//回调函数，功能就是：把buf_size字节数据送入buf即可
//第一个参数(void *opaque)一般情况下可以不用


bool CLPMPaicsVideoLibDemoDlg::JpegToYuv420p(unsigned char * jpgData, int jpgSize, char * yuvData, int yuvSize)
{
	/*
	AVFormatContext *fmt_ctx = NULL;
	AVIOContext *avio_ctx = NULL;
	uint8_t *buffer = NULL, *avio_ctx_buffer = NULL;
	size_t buffer_size, avio_ctx_buffer_size = 4096;
	char *input_filename = NULL;
	int ret = 0;
	struct buffer_data bd = { 0 };
	if (argc != 2) {
		fprintf(stderr, "usage: %s input_file\n"
			"API example program to show how to read from a custom buffer "
			"accessed through AVIOContext.\n", argv[0]);
		return 1;
	}
	input_filename = argv[1];
	/* slurp file content into buffer * /
	ret = av_file_map(input_filename, &buffer, &buffer_size, 0, NULL);
	if (ret < 0)
		goto end;
	/* fill opaque structure used by the AVIOContext read callback * /
	bd.ptr = buffer;
	bd.size = buffer_size;
	if (!(fmt_ctx = avformat_alloc_context())) {
		ret = AVERROR(ENOMEM);
		goto end;
	}
	avio_ctx_buffer = av_malloc(avio_ctx_buffer_size);
	if (!avio_ctx_buffer) {
		ret = AVERROR(ENOMEM);
		goto end;
	}
	avio_ctx = avio_alloc_context(avio_ctx_buffer, avio_ctx_buffer_size,
		0, &bd, &read_packet, NULL, NULL);
	if (!avio_ctx) {
		ret = AVERROR(ENOMEM);
		goto end;
	}
	fmt_ctx->pb = avio_ctx;
	ret = avformat_open_input(&fmt_ctx, NULL, NULL, NULL);
	if (ret < 0) {
		fprintf(stderr, "Could not open input\n");
		goto end;
	}
	ret = avformat_find_stream_info(fmt_ctx, NULL);
	if (ret < 0) {
		fprintf(stderr, "Could not find stream information\n");
		goto end;
	}
	av_dump_format(fmt_ctx, 0, input_filename, 0);
end:
	avformat_close_input(&fmt_ctx);
	/* note: the internal buffer could have changed, and be != avio_ctx_buffer * /
	if (avio_ctx) {
		av_freep(&avio_ctx->buffer);
		av_freep(&avio_ctx);
	}
	av_file_unmap(buffer, buffer_size);
	if (ret < 0) {
		fprintf(stderr, "Error occurred: %s\n", av_err2str(ret));
		return 1;
	}
	return 0;
	AVFormatContext *ic = NULL;
	ic = avformat_alloc_context();

	AVFormatContext *ic = NULL;
	ic = avformat_alloc_context();
	unsigned char * iobuffer = (unsigned char *)av_malloc(32768);
	AVIOContext *avio = avio_alloc_context(iobuffer, 32768, 0, NULL, , NULL, NULL);
	ic->pb = avio;
	err = avformat_open_input(&ic, "nothing", NULL, NULL);


    bool ret = false;
    AVCodecContext *pCodecCtx = NULL;
    AVFrame *pFrame = NULL;
    AVCodec *pCodec = NULL;

    if (!jpgData || jpgSize <= 0 || !yuvData || yuvSize <= 0) {
        goto err;
    }

    pCodec = avcodec_find_decoder(AV_CODEC_ID_MJPEG);
    if (pCodec == NULL) {
        goto err;
    }
    pCodecCtx = avcodec_alloc_context3(pCodec);
    if (pCodecCtx == NULL) {
        goto err;
    }

	pCodecCtx->coded_width = 1920;
	pCodecCtx->coded_height = 1080;
    if (avcodec_open2(pCodecCtx, pCodec, nullptr) < 0) {
        goto err; 
    }
    pFrame = av_frame_alloc();
    if (pFrame == NULL) {
        goto err;
    }

    AVPacket packet;
	av_init_packet(&packet);
    av_new_packet(&packet, jpgSize);
	packet.data = jpgData;
	packet.size = jpgSize;
	packet.stream_index = 0;

	av_dict_set(&options, "rtsp_transport", "tcp", 0);//采用tcp传输(默认udp)
	av_dict_set(&options, "stimeout", "2000000", 0);
	av_dict_set(&options, "probesize", "5000000000", 0);
	av_dict_set(&options, "analyzeduration", "100000000", 0);


	AVFormatContext *input_ctx = NULL;
	avformat_find_stream_info(pCodecCtx->, nullptr );
	if (packet.stream_index == AVMEDIA_TYPE_VIDEO)
	{
		int result = avcodec_send_packet(pCodecCtx, &packet);
		if (result < 0) {
			goto err;
		}
		result = avcodec_receive_frame(pCodecCtx, pFrame);
		if (result == AVERROR(EAGAIN) || result == AVERROR_EOF) {
			goto err;
		}

	}
   
    int width = pCodecCtx->width, height = pCodecCtx->height;
    int frameSize = width * height * 3 >> 1;
    if (frameSize > yuvSize) {
        goto err;
    }
    int i = 0;
    frameSize = 0;
    int height_half = height / 2, width_half = width / 2;
    int y_wrap = pFrame->linesize[0];
    int u_wrap = pFrame->linesize[1];
    int v_wrap = pFrame->linesize[2];

    unsigned char *y_buf = pFrame->data[0];
    unsigned char *u_buf = pFrame->data[1];
    unsigned char *v_buf = pFrame->data[2];

    //save y
    for (i = 0; i < height; i++) {
        memcpy(yuvData + frameSize, y_buf + i * y_wrap, width);
        ++frameSize;
    }
    //save u
    for (i = 0; i < height_half; i++) {
        memcpy(yuvData + frameSize, u_buf + i * u_wrap, width_half);
        ++frameSize;
    }
    //save v
    for (i = 0; i < height_half; i++) {
        memcpy(yuvData + frameSize, v_buf + i * v_wrap, width_half);
        ++frameSize;
    }
    ret = true;
err:
    av_frame_free(&pFrame);
    av_packet_unref(&packet);
    avcodec_close(pCodecCtx);*/
    return 0;
}

void CLPMPaicsVideoLibDemoDlg::ReleaseVideoCapturer(bool showHit)
{

}

void CLPMPaicsVideoLibDemoDlg::ReleaseFrame(void)
{
    CAutoLock lockTemp(&m_frameMutex);
    if (m_pFrameBase != nullptr) {
        delete m_pFrameBase;
        m_pFrameBase = nullptr;
    }
    if (m_pRenderFrame != nullptr) {
        delete m_pRenderFrame;
        m_pRenderFrame = nullptr;
    }

    if (m_pRenderFramePaly != nullptr) {
        delete m_pRenderFramePaly;
        m_pRenderFramePaly = nullptr;
    }
}

void CLPMPaicsVideoLibDemoDlg::ReleaseRender(void)
{
    if (m_pObjRenderCap)
        RenderRelease(m_pObjRenderCap);

    if (m_pObjRenderPlay)
        RenderRelease(m_pObjRenderPlay);
}

void CLPMPaicsVideoLibDemoDlg::UpdateFpsAdapter(void)
{
    CAutoLock locker(&m_apiMutex);
    CStringW strFps;
    m_ctrlfFps.GetWindowTextW(strFps);
    int fps = std::atoi(CStringA(strFps).GetBuffer());


 
    GetDlgItem(IDC_STATIC_FPS)->SetWindowText(L"当前FPS："+strFps);
}

void CLPMPaicsVideoLibDemoDlg::UpdatePlayerPlayTime(int64_t time)
{
    CString str(TimerGetStampEx(time).c_str());

	
    m_ctrlCurTime.SetWindowTextW(str);
}

void CLPMPaicsVideoLibDemoDlg::UpdatePlayerTotalTime(int64_t time)
{
	CString str(TimerGetStampEx(time).c_str());
    m_ctrlTimeLength.SetWindowTextW(str);
}

BEGIN_MESSAGE_MAP(CLPMPaicsVideoLibDemoDlg, CDialogEx)
    ON_WM_PAINT()
    ON_WM_QUERYDRAGICON()
    ON_BN_CLICKED(IDCANCEL, &CLPMPaicsVideoLibDemoDlg::OnBnClickedCancel)
    ON_BN_CLICKED(IDOK, &CLPMPaicsVideoLibDemoDlg::OnBnClickedOk)
    ON_BN_CLICKED(ID_Btn_InitCapture, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnInitcapture)
    ON_BN_CLICKED(ID_Btn_Start, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnStart)
    ON_BN_CLICKED(ID_Btn_Stop, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnStop)
    ON_BN_CLICKED(ID_Btn_ReleaseCaputer, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnReleaseCapturer)
    ON_BN_CLICKED(ID_Btn_BeginFlv, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnBeginflv)
    ON_BN_CLICKED(ID_Btn_EndFlv, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnEndflv)
    ON_BN_CLICKED(ID_Btn_BeginMp4, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnBeginmp4)
    ON_BN_CLICKED(ID_Btn_EndMp4, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnEndmp4)
    ON_BN_CLICKED(ID_Btn_PlayFileGet, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnPlayfileget)
    ON_BN_CLICKED(ID_BtnPlayStart, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaystart)
    ON_BN_CLICKED(ID_BtnPlayPause, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaypause)
    ON_BN_CLICKED(ID_BtnPlayContinue, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaycontinue)
    ON_BN_CLICKED(ID_BtnPlayEnd, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplayend)
    ON_WM_HSCROLL()
    ON_BN_CLICKED(IDC_BTN_FPS, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnFps)
    ON_MESSAGE(UM_UPDATE_PALYER_STATUS, &CLPMPaicsVideoLibDemoDlg::OnUpdatePlayerStatus)
    ON_MESSAGE(UM_UPDATE_PLAY_TIME, &CLPMPaicsVideoLibDemoDlg::OnUpdatePlayingTime)
	ON_BN_CLICKED(ID_BtnGetSeekFrame, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtngetseekframe)
	ON_BN_CLICKED(ID_BtnPlayContinue2, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaycontinue2)
	ON_BN_CLICKED(ID_BtnPlayTime, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaytime)
	ON_BN_CLICKED(ID_BtnWriteReset, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnwritereset)
	ON_CBN_SELCHANGE(IDC_COMBO_DevIndex, &CLPMPaicsVideoLibDemoDlg::OnSelchangeComboDevindex)
	ON_BN_CLICKED(ID_Btn_Pause, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnPause)
	ON_BN_CLICKED(ID_BtnGetMic, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtngetmic)
	ON_BN_CLICKED(ID_BtnReleaseMic, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnreleasemic)
	ON_BN_CLICKED(ID_BtnStartMic, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnstartmic)
	ON_BN_CLICKED(ID_BtnPauseMic, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnpausemic)
	ON_BN_CLICKED(ID_BtnInitMic, &CLPMPaicsVideoLibDemoDlg::OnBnClickedBtninitmic)
	ON_CBN_SELCHANGE(IDC_COMBOMic, &CLPMPaicsVideoLibDemoDlg::OnSelchangeCombomic)
END_MESSAGE_MAP();


static int g_nRenderCapWidth = 1600;
static int g_nRenderCapHeight = 900;

static int g_nRenderPlayWidth = 1600;
static int g_nRenderPlayHeight = 900;

// CLPMPaicsVideoLibDemoDlg 消息处理程序
#include <unordered_map>
#include <map>
BOOL CLPMPaicsVideoLibDemoDlg::OnInitDialog()
{
	LPM::SetLogPath(nullptr, "video.log", 5);
	CPicReader pic(u8"E:\\陈医⽣-20220108-何早1\\腹部横切面.jpg");

	
	//获取屏幕数量
	int screenNUm = GetSystemMetrics(SM_CMONITORS);

	char sz[] = "123456";

	std::vector<char> vt;
	vt.resize(6);
	//copy(vt.begin(), vt.end(), sz);
	vt.assign(sz, sz + 6);

	char* ppp = (char*)&vt[0];
	int nt = 1603 / 4;
	CDialogEx::OnInitDialog();
    // 监听转储回调
    SetUnhandledExceptionFilter((LPTOP_LEVEL_EXCEPTION_FILTER)ApplicationCrashHandler);
	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	std::unordered_map<int, std::string> mapDd;
	mapDd[2] = "1234";
	mapDd[1] = "3234";
	mapDd[3] = "34234";
	mapDd[23] = "a34234";
	mapDd[33] = "b34234";
	mapDd[13] = "c34234";
 	// TODO: 在此添加额外的初始化代码
	string str1, str2, str3;
	for (auto it : mapDd)
	{
		str1 = it.second;
	}
	mapDd[2] = "abder";
	mapDd.erase(4);
	for (auto it : mapDd)
	{
		str2 = it.second;
	}
	SetLogPath(nullptr, "VideoLog");
	WriteLogEx(ModuleVideo, LogInfo, "Video model demo init");

	CWnd *pWnd = GetDlgItem(ID_BtnShowVideo); // 取得控件的指针
	CRect rect;
	//CWnd *pWnd = GetDlgItem(ID_BtnShowVideo); // 取得控件的指针
	HWND hWnd = pWnd->GetSafeHwnd(); // 取得控件的句柄
	pWnd->GetWindowRect(rect);

    g_nRenderCapWidth = rect.Width() - rect.Width() % 16;
    g_nRenderCapHeight = rect.Height() - rect.Height() % 4;

	pWnd->MoveWindow(3, 4, g_nRenderCapWidth, g_nRenderCapHeight);

	m_editVideoPath.SetWindowTextW(L"D://Pic//MyVideo");
	

	m_editMp4Path.SetWindowTextW(L"D://Pic//test.mp4");
	m_editPlayPath.SetWindowTextW(L"D://Pic//MyVideo");
	//m_editPlayPath.SetWindowTextW(L"D://Pic//123.MP4");
	m_ctrlfFps.SetWindowTextW(L"25");

	m_ctrlPlayVideo.GetWindowRect(rect);
	g_nRenderPlayWidth = g_nRenderCapWidth;// rect.Width();
	g_nRenderPlayHeight = g_nRenderCapHeight;// rect.Height();
	
    // 暂时未测试的按钮禁用
    //GetDlgItem(ID_Btn_BeginMp4)->EnableWindow(FALSE);
   // GetDlgItem(ID_Btn_EndMp4)->EnableWindow(FALSE);

    // 播放器按钮禁用
    //GetDlgItem(ID_BtnPlayPause)->EnableWindow(FALSE);
    //GetDlgItem(ID_BtnPlayEnd)->EnableWindow(FALSE);
   // GetDlgItem(ID_BtnPlayContinue)->EnableWindow(FALSE);

 

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CLPMPaicsVideoLibDemoDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CLPMPaicsVideoLibDemoDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}
static bool g_bFlag = true;



static unsigned long long GetCurTimeStampEx()
{

	return GetTickCount();
}
static unsigned long long lTempCur = 0;
static unsigned long long lTempPre = 0;

static int nCount = 0;



int CLPMPaicsVideoLibDemoDlg::OnPlayerStatusCb(enPlayerStatus _enStatus, long long _llCurPts, long long _llVideoLength, void* _pUserData)
{
	PostMessage(UM_UPDATE_PALYER_STATUS, _enStatus, 0);
	return 0;
}


IFrameSeekerEx* g_pFrame = nullptr;

void CLPMPaicsVideoLibDemoDlg::OnBnClickedCancel()
{
    CAutoLock locker(&m_apiMutex);
   

	AYJ::IVideoCapturerEx::Release(m_pVideocapturerEx);
    ReleaseVideoCapturer(false);
    ReleaseFrame();
    ReleaseRender();


	if (g_pFrame)
	{
		IFrameSeekerEx::Release(g_pFrame);
	}
	WriteLogEx(ModuleVideo, LogInfo, "Demo end !!");

	CDialogEx::OnCancel();
}




void CLPMPaicsVideoLibDemoDlg::OnBnClickedOk()
{
	
}

AYJ::CImageConvert g_objImageCvt;

int CLPMPaicsVideoLibDemoDlg::OnFrameCallbackEx(uint32_t _unCapId, double _dSampleTime, unsigned char* _pFrameData, long _lDataSize, int _nW, int _nH, int _nVideoFormate)
{
	/*if (g_bFlag)
	{
		g_bFlag = false;
		return 0;
	}*/
	g_bFlag = true;
	//if ()
	{
		static  long long llTimeCur = 0;
		static  long long llTimePre = 0;
		static int nCount2 = 0;
		llTimeCur = GetCurTimeStampEx();

		if (llTimeCur - llTimePre >= 1000)
		{
			//wstring strFps = L"FPS:" + std::to_wstring(nCount);

			PostMessage(UM_UPDATE_PALYER_STATUS, 100, nCount2);
			//::PostMessage(this->GetSafeHwnd(), UM_UPDATE_PALYER_STATUS, nCount2, 0);
			nCount2 = 1;
			llTimePre = llTimeCur;
		}
		else
			nCount2++;

		
	}
	//return 0;
	//WriteLogEx(ModuleUI, LogDebug, "OnFrameCallbackEx, %0.5f", _dSampleTime);
	

	CAutoLock lockTemp(&m_frameMutex);

	CRunTime timeTmep;

	if (m_pObjRenderCap == nullptr) {
		HWND hWnd = m_ctrlShowVideo.GetSafeHwnd(); // 取得控件的句柄

		m_pObjRenderCap = RenderCreate((unsigned long)hWnd, g_nRenderCapWidth, g_nRenderCapHeight, true);
	}
	if (m_pFrameBase == nullptr) {
		m_pFrameBase = new tagVdieoFrame(AyjImageFormat::eImageYuv420p, _nW, _nH);

	}

	if (m_pRenderFrame == nullptr) {
		m_pRenderFrame = new tagVdieoFrame(AyjImageFormat::eImageYuv420p, g_nRenderCapWidth, g_nRenderCapHeight);
	}

	// 图像格式转化
	/*if (_nVideoFormate == AYJ::AyjImageFormat::eImageYUY422) 
	{
		g_objImageCvt.ImageConvert(_pFrameData, _nVideoFormate, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
		
		if (m_pMp4)
			m_pMp4->Mp4Write(_pFrameData, _lDataSize, _nVideoFormate, _nW, _nH, 0);
		
		if (m_pVideoWrite)
		{
			static int llPts = 0;
			llPts += 40;
			m_pVideoWrite->VideoWrite(m_pRenderFrame->pVideoData, m_pRenderFrame->unDataSize, m_pRenderFrame->eFrameFormat, _nW, _nH, llPts);
		}
	}
	else if (_nVideoFormate == eImageMJPG)
	{
		cv::Mat rawData(1, _lDataSize, CV_8UC1, (void*)_pFrameData);
		cv::Mat decodedImage = imdecode(rawData, cv::ImreadModes::IMREAD_COLOR);

		if (decodedImage.data != nullptr)
		{
			cv::Mat yuvImage;
			cv::cvtColor(decodedImage, yuvImage, cv::COLOR_BGR2YUV_I420); 
			
			g_objImageCvt.ImageConvert(yuvImage.data, AYJ::AyjImageFormat::eImageYuv420p, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
			
		}
		//g_objImageCvt.ImageConvert(_pFrameData, AYJ::AyjImageFormat::eImageRGB24, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
		//return 0;
	}
	else if (_nVideoFormate == eImageRGB24)
	{
		cv::Mat  decodedImage = cv::Mat(cv::Size(_nW, _nH), CV_8UC3, _pFrameData);

		if (decodedImage.data != nullptr)
		{
			cv::Mat yuvImage;
			cv::cvtColor(decodedImage, yuvImage, cv::COLOR_BGR2YUV_I420);

			g_objImageCvt.ImageConvert(yuvImage.data, AYJ::AyjImageFormat::eImageYuv420p, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);

		}
		//return 0;
	}
	else if (_nVideoFormate == eImageBGR24)
	{
		cv::Mat  decodedImage = cv::Mat(cv::Size(_nW, _nH), CV_8UC3, _pFrameData);

		if (decodedImage.data != nullptr)
		{
			//cv::Mat yuvImage;
			//cv::cvtColor(decodedImage, yuvImage, cv::COLOR_BGR2YUV_I420);

			g_objImageCvt.ImageConvert(decodedImage.data, AYJ::AyjImageFormat::eImageYuv420p, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);

		}
		//return 0;
	}*/
	if (_nVideoFormate !=  eImageNone)
	{
		if (_nVideoFormate == eImageMJPG)	//	需要先解码
		{
			cv::Mat rawData(1, _lDataSize, CV_8UC1, (void*)_pFrameData);
			cv::Mat decodedImage = imdecode(rawData, cv::ImreadModes::IMREAD_COLOR);

			if (decodedImage.data != nullptr)
			{
				cv::Mat yuvImage;
				cv::cvtColor(decodedImage, yuvImage, cv::COLOR_BGR2YUV_I420); 
			
				g_objImageCvt.ImageConvert(yuvImage.data, AYJ::AyjImageFormat::eImageYuv420p, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
			}
		}
		else if (_nVideoFormate == eImageBGR24 || eImageBGRA32 == _nVideoFormate)
		{
			cv::Mat  decodedImage;
			if(_nVideoFormate == eImageBGR24)
				decodedImage  = cv::Mat(cv::Size(_nW, _nH), CV_8UC3, _pFrameData);
			else if(eImageBGRA32 == _nVideoFormate)
				decodedImage = cv::Mat(cv::Size(_nW, _nH), CV_8UC4, _pFrameData);

			 cv::flip(decodedImage, decodedImage, 0);

			 g_objImageCvt.ImageConvert(decodedImage.data, _nVideoFormate, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
		}
		else 
		{
			g_objImageCvt.ImageConvert(_pFrameData, _nVideoFormate, _nW, _nH, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
		}
		

		double dScale = timeTmep.GetRunTime();
		unsigned char* pDataYuv420p = m_pRenderFrame->pVideoData;

		CRunTime timeTmep2;
		if (m_pObjRenderCap)
			RenderPutYuv420pData(m_pObjRenderCap, pDataYuv420p, g_nRenderCapWidth, g_nRenderCapHeight);
	}
	
	// 如果与控件尺寸不一致需要进行缩放
	/*if (m_pFrameBase->unWidth != g_nRenderCapWidth || m_pFrameBase->unHeight != g_nRenderCapHeight) 
	{
		//I420Scale(m_pFrameBase->pVideoData, m_pFrameBase->unWidth, m_pFrameBase->unHeight,m_pRenderFrame->pVideoData, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);

		g_objImageCvt.ImageConvert(m_pFrameBase->pVideoData, m_pFrameBase->eFrameFormat, m_pFrameBase->unWidth, m_pFrameBase->unHeight, m_pRenderFrame->pVideoData, m_pRenderFrame->eFrameFormat, m_pRenderFrame->unWidth, m_pRenderFrame->unHeight);
	}*/

	

	return 0;
}
/**
 * 摄像头状态变化通知回调接口
 * @param capId 回调采集实例的编号
 * @param status 当前采集实例的状态
 */
int CLPMPaicsVideoLibDemoDlg::OnCapturerStatusEx(uint32_t _unCapId, AYJ::eCapDevState status)
{
	CStringW strTips(L"采集卡状态:");
	if (status == AYJ::CapStateDevBreak)
	{
		strTips += L"未插入信号";
		m_ctrlCaptureStatus.SetWindowTextW(strTips);
	}
	else if (status == AYJ::CapStateStopped)
	{
		strTips += L"采集停止";
		m_ctrlCaptureStatus.SetWindowTextW(strTips);
	}
	else if (status == AYJ::CapStateRunning)
	{
		strTips += L"采集中";
		m_ctrlCaptureStatus.SetWindowTextW(strTips);
	}
	else if (status == AYJ::CapStatePaused)
	{
		strTips += L"采集暂停";
		m_ctrlCaptureStatus.SetWindowTextW(strTips);
	}else if (status == AYJ::CapStateDevInsertReset)
	{
		strTips += L"插入信号了";
		m_ctrlCaptureStatus.SetWindowTextW(strTips);
	}

	return 0;
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnInitcapture()
{
	CAutoLock lcok(&m_apiMutex);
	if (!m_pVideocapturerEx && m_bCaptrueNew)
	{
		m_vectDevInfo.clear();
		m_pVideocapturerEx = AYJ::IVideoCapturerEx::Create();

		m_ctrlComboxDevIndex.Clear();
		m_ctrlComboxDevIndex.ResetContent();
		int nCount = m_pVideocapturerEx->CaptureGetDevCount();

		int nSelectCuptrue = 1;

		for (int i =0 ; i < nCount ; i++)
		{
			AYJ::tagDevInfo objDevInfo;
			m_pVideocapturerEx->CaptureGetDevInfo(i, objDevInfo);

			m_ctrlComboxDevIndex.InsertString(i, CString(LPM::U2W(objDevInfo.strDevName.c_str()).c_str()));

			m_vectDevInfo.push_back(objDevInfo);
		}

		m_ctrlComboxDevIndex.SetCurSel(0);
		
		m_ctrlComboxPixformat.SetCurSel(0);
		OnSelchangeComboDevindex();

		return ;
	}

	if (m_bCaptrueNew)
	{
		return;
	}



}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnReleaseCapturer()
{
	if (m_pVideocapturerEx && m_bCaptrueNew)
	{
		AYJ::IVideoCapturerEx::Release(m_pVideocapturerEx);
		
	}

	m_ctrlComboxPixformat.ResetContent();
	m_ctrlComboxDevIndex.ResetContent();

	ReleaseFrame();

	m_ctrlCapFps.SetWindowText(L"");
	if (m_bCaptrueNew)
	{
		return;
	}
	

    ReleaseVideoCapturer(true);
   
    ReleaseRender();
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnStart()
{
	CAutoLock lcok(&m_apiMutex);
	if (m_pVideocapturerEx && m_bCaptrueNew)
	{
		
		int nIndex = m_ctrlComboxDevIndex.GetCurSel();
		
		int nIndex2 = m_ctrlComboxPixformat.GetCurSel();
		if (nIndex < m_vectDevInfo.size() && m_vectDevInfo[nIndex].vectDevSupportedFormate.size() > nIndex2)
		{
			m_pVideocapturerEx->CaptureInit(nIndex, this);

			AYJ::tagDevSupportedFormate tagOut;

			bool bRet = m_pVideocapturerEx->CaptureDevFormatSet(m_vectDevInfo[nIndex].vectDevSupportedFormate[nIndex2], tagOut);
			
			m_pVideocapturerEx->CaptureStart();
			int i = 0;
		}
		
		return;
	}


}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnStop()
{
	CAutoLock lcok(&m_apiMutex);
	if (m_pVideocapturerEx && m_bCaptrueNew)
	{
		m_pVideocapturerEx->CaptureStop();

		return;
	}
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnBeginflv()
{
	

	CAutoLock locker(&m_apiMutex);
	CStringW strMp4Path;
	m_editMp4Path.GetWindowTextW(strMp4Path);

	if (nullptr==m_pMp4)
	{
		m_pMp4 = IAyjMp4Writer::Create();
		m_pMp4->Mp4WriteInit(LPM::W2U(strMp4Path.GetBuffer()).c_str(), 1920, 1080, 6, 1024 * 1024 * 2, 30, true);
	}

	return;
	//MP4MuxerCreate(0, 0, 0, 0, 0);
    

    CStringW strFps;
    m_ctrlfFps.GetWindowTextW(strFps);


	if (strFps.IsEmpty()) {
		AfxMessageBox(L"FLV地址为空，无法写入文件");
		return;
	}
	int nFps = std::atoi(CStringA(strFps).GetBuffer());


}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnEndflv()
{
	CAutoLock lockTemp(&m_frameMutex);
	if (m_pMp4)
	{
		m_pMp4->Mp4WriteClose();
		IAyjMp4Writer::Release(m_pMp4);

		AfxMessageBox(L"停止写mp4 成功");
	}
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnBeginmp4()
{
	
	CStringW strFps;
	m_ctrlfFps.GetWindowTextW(strFps);
	if (strFps.IsEmpty()) {
		AfxMessageBox(L"MP4地址为空，无法写入文件");
		return;
	}
	int nFps = std::atoi(CStringA(strFps).GetBuffer());



	CStringW strMp4Path;
	m_editVideoPath.GetWindowTextW(strMp4Path);

	if (m_pVideoWrite == nullptr)
	{
		m_pVideoWrite = IAyjVideoWriter::Create(nullptr, nullptr);
		m_pVideoWrite->VideoWriteInit(CStringA(strMp4Path).GetBuffer(), 1920, 1080, 12, 1024 * 1024 * 2, 30, false);
	}

	
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnEndmp4()
{
	if (m_pVideoWrite)
	{
		CAutoLock lockTemp(&m_frameMutex);

		m_pVideoWrite->VideoWriteClose();
		IAyjVideoWriter::Release(m_pVideoWrite);

	}
	/*if (m_pObjMp4)
	{
		//CloseEncoder(m_pObjMp4);
		MP4MuxerRelease(m_pObjMp4);
		m_pObjMp4 = nullptr;
	}
	else {
		AfxMessageBox(L"未打开MP4编写，无法结束");
	}*/
	
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnPlayfileget()
{
	
	BOOL bIsOpen = TRUE;		
	CString defaultDir = L"D:\\";	
	CString fileName = L"";			
	CString filter = L"文件 (*.mp4; *.flv; *.mov)|*.mp4;*.flv;*.mov||";	
	CFileDialog openFileDlg(bIsOpen, defaultDir, fileName, OFN_HIDEREADONLY | OFN_READONLY, filter, NULL);
	openFileDlg.GetOFN().lpstrInitialDir = defaultDir;
	INT_PTR result = openFileDlg.DoModal();
	CString filePath = defaultDir + "\\test.mp4";
	if (result == IDOK) 
	{
		filePath = openFileDlg.GetPathName();
		m_editPlayPath.SetWindowTextW(filePath);
	}
	
}

static int VideoDecoderCallback(char* pData, int nWidth, int nHeight, int nFormat, uint64_t pts, void* pContext)
{
	CLPMPaicsVideoLibDemoDlg* pThis = (CLPMPaicsVideoLibDemoDlg*)pContext;
	if (pThis)
	{
		// 如果与控件尺寸不一致需要进行缩放
		if (pThis->m_pRenderFramePaly->unWidth != g_nRenderPlayWidth || pThis->m_pRenderFramePaly->unHeight != g_nRenderPlayHeight) {
			I420Scale((unsigned char*)pData, nWidth, nHeight,
				pThis->m_pRenderFramePaly->pVideoData, g_nRenderPlayWidth, g_nRenderPlayHeight);
		}

		RenderPutYuv420pData(pThis->m_pObjRenderPlay, pThis->m_pRenderFramePaly->pVideoData, g_nRenderPlayWidth, g_nRenderPlayHeight);
	}

	return 0;
}

double ll = 0;
int CLPMPaicsVideoLibDemoDlg::OnPlayerFrameCb(tagVdieoFrame* _objFrame, void* _pUserData)
{
	static unsigned long long llShowPre = 0;
	if (!m_pRenderFramePaly)
	{
		return 0;
	}
	// 如果与控件尺寸不一致需要进行缩放
	if (1)
	{
		//I420Scale(_objFrame->pVideoData, _objFrame->unWidth, _objFrame->unHeight,
		//	m_pRenderFramePaly->pVideoData, g_nRenderPlayWidth, g_nRenderPlayHeight);

		g_objImageCvt.ImageConvert(_objFrame->pVideoData, _objFrame->eFrameFormat, _objFrame->unWidth, _objFrame->unHeight,  m_pRenderFramePaly->pVideoData, m_pRenderFramePaly->eFrameFormat, m_pRenderFramePaly->unWidth, m_pRenderFramePaly->unHeight);
	}
	
	lTempCur = GetCurTimeStampEx();
	int nCurInter = lTempCur - llShowPre;
	if (nCurInter < 30)
		SleepExEx(30 - nCurInter - ll);
	CRunTime timeTmep;
	//SavePicEx(_objFrame->data, 0, _objFrame->width, _objFrame->height, "D://Pic//TestPng.png");
	RenderPutYuv420pData(m_pObjRenderPlay, m_pRenderFramePaly->pVideoData, g_nRenderPlayWidth, g_nRenderPlayHeight);
	ll = timeTmep.GetRunTime();
	//lTempCur = GetCurTimeStampEx();

	if (lTempCur - lTempPre >= 1000)
	{
		PostMessage(UM_UPDATE_PLAY_TIME, nCount, 2);
		nCount = 1;
		lTempPre = lTempCur;

		PostMessage(UM_UPDATE_PLAY_TIME, _objFrame->llPts, 1);
	}
	else
		nCount++;

	llShowPre = GetCurTimeStampEx();

	

	return 0;
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaystart()
{
	if (m_pObjRenderPlay == nullptr)
	{
		//HWND hWnd = m_ctrlPlayVideo.GetSafeHwnd(); // 取得控件的句柄
		CWnd *pWnd = GetDlgItem(ID_BtnShowVideo); // 取得控件的指针
		//CRect rect;
		//CWnd *pWnd = GetDlgItem(ID_BtnShowVideo); // 取得控件的指针
		HWND hWnd = pWnd->GetSafeHwnd(); // 取得控件的句柄
		m_pObjRenderPlay = RenderCreate((unsigned long)hWnd, g_nRenderPlayWidth, g_nRenderPlayHeight, true);
	}

	//if (!m_pObjDecoder)
	//{
	//	m_pObjDecoder = DecoderCreate(VideoDecoderCallback, this);
	//	if (m_pObjDecoder)
	//	{
	//		CStringW strPath;
	//		m_editPlayPath.GetWindowTextW(strPath);
	//		if (strPath.IsEmpty())
	//		{
	//			AfxMessageBox(L"播放文件为空！");
	//			return;
	//		}
	//		int nRet = DecoderFile(m_pObjDecoder, CStringA(strPath).GetBuffer());
	//		if (nRet != 0)
	//		{
	//			AfxMessageBox(L"播放文件打开失败！");
	//			return;
	//		}
	//		double dTimeLength = 0;
	//		long  lFrameCount = 0;
	//		DecoderGetInfo(m_pObjDecoder, dTimeLength, lFrameCount);

	//		CStringW strLength;
	//		strLength.Format(L"%0.3f", dTimeLength);
	//		m_ctrlTimeLength.SetWindowTextW(strLength);

	//		//DecoderFile
	//	}
	//	
		if (m_pRenderFramePaly == nullptr)
		{
			m_pRenderFramePaly = new tagVdieoFrame(eImageYuv420p, g_nRenderPlayWidth, g_nRenderPlayHeight);
		}
		if (!m_pReadAyjVideo || !m_pCommVideiPlayer)
		{
			CStringW strPlayPath;
			m_editPlayPath.GetWindowTextW(strPlayPath);
			int nW = 0, nH = 0;
			float nFps = 0;
			if (strPlayPath.Find(L".mp4") >= 0 || strPlayPath.Find(L".MP4")>= 0 )
			{
				m_pCommVideiPlayer = IAyjCommVideoPlayer::Create();
				m_pCommVideiPlayer->PlayerInit(CStringA(strPlayPath).GetBuffer(),false, this, nullptr);
				UpdatePlayerTotalTime(m_pCommVideiPlayer->PlayerGetLength());
			}
			else
			{
				m_pReadAyjVideo = IAyjVideoPlayer::Create(true);
				m_pReadAyjVideo->PlayerInit(CStringA(strPlayPath).GetBuffer(), this, nullptr);
				UpdatePlayerTotalTime(m_pReadAyjVideo->PlayerGetLength());
				
				m_pReadAyjVideo->PlayerGetInfo(nW, nH, nFps);
			}
			int i = 0;
			
			//m_pReadAyjVideo->PlayerPlay(20*1000, 30*1000);
		}
		return ;
        int ret = -1;
        

        if (ret < 0) {
            AfxMessageBox(L"视频开始播放失败");
        } else {
            GetDlgItem(ID_BtnPlayPause)->EnableWindow(TRUE);
            GetDlgItem(ID_BtnPlayEnd)->EnableWindow(TRUE);
            GetDlgItem(ID_BtnPlayContinue)->EnableWindow(TRUE);

           
          
            m_ctrlPlayStep.SetTicFreq(1);
            m_ctrlPlayStep.SetPos(0);
            UpdatePlayerPlayTime(0);
        }
		//m_pVideoPlayer->SetPlayTime(0);
		//m_pVideoPlayer->Play();

}

//	播放器暂停
void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaypause()
{
	if (m_pReadAyjVideo)
	{
		m_pReadAyjVideo->PlayerPause();
	}
	if (m_pCommVideiPlayer)
	{
		m_pCommVideiPlayer->PlayerPause();
	}
	
	return;



    AfxMessageBox(L"视频暂停播放失败");
    //if (m_pObjDecoder)
	//	DecoderPause(m_pObjDecoder);
}

//	播放器继续播放
void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaycontinue()
{
    if (m_pReadAyjVideo)
    {
		m_pReadAyjVideo->PlayerPlay();
    }
	if (m_pCommVideiPlayer)
	{
		m_pCommVideiPlayer->PlayerPlay();
	}
   
	//	DecoderContinue(m_pObjDecoder);
	//
}

//	播放结束
void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplayend()
{
    if (m_pReadAyjVideo)
	{
		m_pReadAyjVideo->PlayerClose();
		IAyjVideoPlayer::Release(m_pReadAyjVideo);
    }
	if (m_pCommVideiPlayer)
	{
		m_pCommVideiPlayer->PlayerClose();
		IAyjCommVideoPlayer::Release(m_pCommVideiPlayer);
	}
    //AfxMessageBox(L"视频停止播放失败");

    //if (m_pObjDecoder)
	//	DecoderRelease(m_pObjDecoder);
}

void CLPMPaicsVideoLibDemoDlg::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar)
{
	
	if (pScrollBar->GetSafeHwnd() == m_ctrlPlayStep.GetSafeHwnd())
	{
		
	}
	__super::OnHScroll(nSBCode, nPos, pScrollBar);
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnFps()
{
    UpdateFpsAdapter();
}

LRESULT CLPMPaicsVideoLibDemoDlg::OnUpdatePlayerStatus(WPARAM wParam, LPARAM lParam)
{
	int event = (int)wParam;
    CString text = L"状态：";
    switch (event) {
    case enReaderPlaying:
        text.Append(L"播放中");
        break;
    case enReaderStopped:
        text.Append(L"已停止");
        break;
    case enReaderPaused:
        text.Append(L"已暂停");
        break;
    case enReaderEndReached:
        text.Append(L"已播放结束");
        break;
	case 100:
	{
		int nCount = (int)lParam;

		m_ctrlCapFps.SetWindowText(CString(std::to_string(nCount).c_str()));
		break;
	}
    default:
        text.Append(L"未知状态");
        break;
    }

    GetDlgItem(IDC_STATIC_Player_Status)->SetWindowText(text);
    
    return 0;
}

LRESULT CLPMPaicsVideoLibDemoDlg::OnUpdatePlayingTime(WPARAM wParam, LPARAM lParam)
{
    UpdatePlayerPlayTime((int64_t)wParam);
	if (2 == lParam)
	{
		CString str;
		str.Format(L"Fps:%lld", wParam);
		m_ctrlPlayerFps.SetWindowTextW(str);
	}
    return LRESULT();
}



void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtngetseekframe()
{
	CString strSeekTime;
	m_editSeekTime.GetWindowText(strSeekTime);
	long long llTime = 0;
	if (g_pFrame == nullptr)
	{
		g_pFrame = IFrameSeekerEx::Create();
		
		
	}
	//g_pFrame->FrameInit("D://video//201115020841.flv");
	g_pFrame->FrameInit("D://Pic//VideoTest.exdt");
	llTime = g_pFrame->FrameGetLength();
	long long llSeekTime = std::atoll(CStringA(strSeekTime).GetBuffer());

	tagVdieoFrame* pTest = g_pFrame->FrameSeek(llSeekTime);
	//g_pFrame->
	int i = 0;
	
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaycontinue2()
{
	if (m_pReadAyjVideo)
	{
		m_pReadAyjVideo->PlayerPlay(1000 * 20, 1000 * 30);
	}
	if (m_pCommVideiPlayer)
	{
		m_pCommVideiPlayer->PlayerSeek(1000 * 6);
	}
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnplaytime()
{
	CStringW strTime;
	m_editSeekTime.GetWindowTextW(strTime);
	if (strTime.GetLength()<=0)
	{
		return;
	}
	long long llSeekTime = std::stoll(strTime.GetBuffer());
	if (m_pReadAyjVideo)
	{
		m_pReadAyjVideo->PlayerSeek(llSeekTime);
	}
	if (m_pCommVideiPlayer)
	{
		m_pCommVideiPlayer->PlayerSeek(llSeekTime);
	}
	//m_pVideoPlayer->Play(80, 2000);
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnwritereset()
{
	
}




void CLPMPaicsVideoLibDemoDlg::OnSelchangeComboDevindex()
{
	int nIndex = m_ctrlComboxDevIndex.GetCurSel();

	m_ctrlComboxPixformat.Clear();
	m_ctrlComboxPixformat.ResetContent();

	if (m_vectDevInfo.size() > nIndex)
	{
		int nIndexInsert = 0;
		for (auto it : m_vectDevInfo[nIndex].vectDevSupportedFormate)
		{
			char sz[100] = {};
			sprintf_s(sz, 90, "w:%d h:%d,fps:%d,Formate:%s", it.nVideoW, it.nVideoH, (int)it.nFps, it.strFormate.c_str());
			m_ctrlComboxPixformat.InsertString(nIndexInsert, CString(sz));
			nIndexInsert++;
		}
		m_ctrlComboxPixformat.SetCurSel(1);
	}
	
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnPause()
{
	if (m_pVideocapturerEx)
	{
		m_pVideocapturerEx->CapturePause();
	}
}

AYJ::IAudioCapture* pAudio = nullptr;
void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtngetmic()
{
	pAudio = AYJ::IAudioCapture::Create();
	m_ctrlMicCombox.Clear();
	m_vectDevInfo.clear();

	if (pAudio)
	{
		int nCount= pAudio->CaptureGetDevCount();
		for (int i=0; i < nCount ;i ++)
		{
			AYJ::tagDevInfo objInfo;
			pAudio->CaptureGetDevInfo(i, objInfo);

			m_ctrlMicCombox.InsertString(i, CString(LPM::U2W(objInfo.strDevName.c_str()).c_str()));

			m_vectDevInfo.push_back(objInfo);
		}

		m_ctrlMicCombox.SetCurSel(0);
		OnSelchangeCombomic();

		
	
	}
}

void CLPMPaicsVideoLibDemoDlg::OnSelchangeCombomic()
{
	int nIndex = m_ctrlMicCombox.GetCurSel();

	 m_ctrlComboxMicSupportList .Clear();
	 m_ctrlComboxMicSupportList.ResetContent();

	if (m_vectDevInfo.size() > nIndex)
	{
		int nIndexInsert = 0;
		for (auto it : m_vectDevInfo[nIndex].vectDevSupportedFormate)
		{
			char sz[100] = {};
			sprintf_s(sz, 90, "采样率:%d 通道:%d,位深度:%d,比特率:%d,Formate:%s", it.nSamplesPerSec, it.nChannels, (int)it.wBitsPerSample, (int)it.nAvgBytesPerSec, it.strFormate.c_str());
			m_ctrlComboxMicSupportList.InsertString(nIndexInsert, CString(sz));
			nIndexInsert++;
		}
		m_ctrlComboxMicSupportList.SetCurSel(1);
	}
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtninitmic()
{
	if (pAudio)
	{
		int nIndex = m_ctrlMicCombox.GetCurSel();
		pAudio->CaptureInit(nIndex, this);

		int nIndex2 = m_ctrlComboxMicSupportList.GetCurSel();
		if (nIndex < m_vectDevInfo.size() && m_vectDevInfo[nIndex].vectDevSupportedFormate.size() > nIndex2)
		{
			pAudio->CaptureInit(nIndex, this);

			AYJ::tagDevSupportedFormate tagOut;

			bool bRet = pAudio->CaptureDevFormatSet(m_vectDevInfo[nIndex].vectDevSupportedFormate[nIndex2], tagOut);
		}
	}
}

void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnreleasemic()
{
	AYJ::IAudioCapture::Release(pAudio);
	m_ctrlComboxMicSupportList.Clear();
	m_ctrlMicCombox.Clear();
	m_vectDevInfo.clear();
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnstartmic()
{
	if (pAudio)
	{
		pAudio->CaptureStart();
	}
}


void CLPMPaicsVideoLibDemoDlg::OnBnClickedBtnpausemic()
{
	if (pAudio)
	{
		pAudio->CaptureStop();
	}
}