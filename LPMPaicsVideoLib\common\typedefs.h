﻿/*
 * Filename:  typedefs.h
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/15/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/
#ifndef TYPEDEFS_H
#define TYPEDEFS_H

#include <stdio.h>            //define NULL.
#include <stdint.h>
#include <vector>
#include <map>
#include <functional>
// thread
#include <thread>
#include <mutex>
#include <condition_variable>
#include "AyjVideoCommDefine.h"

#ifdef	_MSC_VER
// disable some level-4 warnings, use #pragma warning(enable:###) to re-enable
#pragma warning(disable:4100) // warning C4100: unreferenced formal parameter
#pragma warning(disable:4201) // warning C4201: nonstandard extension used : nameless struct/union
#pragma warning(disable:4511) // warning C4511: copy constructor could not be generated
#pragma warning(disable:4512) // warning C4512: assignment operator could not be generated
#pragma warning(disable:4514) // warning C4514: "unreferenced inline function has been removed"
#pragma warning(disable:4996)
#pragma warning(disable:4068)
#pragma warning(disable:4146)
#pragma warning(disable:4819)
#pragma warning(disable:4251)
#pragma warning(disable:4172) // warning C4514: returns the address of a local variable or temporary
#endif // _MSC_VER



#define SAFE_DELETE(ptr)        \
    do {                        \
        if (ptr != NULL) {      \
            delete ptr;         \
            ptr = NULL;         \
        }                       \
    } while (0)

#define SAFE_FREE(ptr)          \
    do {                        \
        if (ptr != NULL) {      \
            free(ptr);          \
            ptr = NULL;         \
        }                       \
    } while (0)

#define SAFE_RELEASE(ptr)       \
    do {                        \
        if (ptr != NULL) {		\
            ptr->Release();     \
			ptr = NULL;			\
        }                       \
    } while(0)

#define SAFE_STOP(ptr)          \
    do {                        \
        if (ptr != NULL) {		\
            ptr->Stop();        \
        }                       \
    } while(0)

#define SAFE_CLOSE(ptr)         \
    do {                        \
        if (ptr != NULL) {		\
            ptr->Close();       \
        }                       \
    } while(0)

#define SAFE_CLOSE_FILE(file)   \
    do {                        \
        if (file != NULL) {		\
            fclose(file);       \
            file = NULL;        \
        }                       \
    } while(0)

namespace AYJ {

    /**
     * 采集图像格式
     * I420: YYYYYYYY UU VV    =>YUV420P
     * YV12: YYYYYYYY VV UU    =>YUV420P
     * NV12: YYYYYYYY UVUV     =>YUV420SP
     * NV21: YYYYYYYY VUVU     =>YUV420SP
     * YUYV/YUY2: YU YV YU YV  =>YUV422
     */
    /*enum ImageFormat
    {
        kImageNone = -1,
        kImageI420,
        kImageYUY2,
        kImageYV12,
        kImageNV21,
        kImageNV12,
        kImageYVYU,
        kImageYUYV,
        kImageUYVY,

        kImageRGB32,
        kImageRGB24,
        kImageRGB565,
        kImageRGB555,
        kImageMJPG,
		
		KAudioPcm = 101,
    };

     //* h264帧类型 I帧/B帧/P帧
    enum H264FrameType
    {
        kUnkownFrame = -1,
        kIFrame,
        kPFrame,
        kBFrame
    };

#pragma  pack(1)
    struct LPM_VIDEO_API FrameBase
    {
        uint64_t seq = 0;
        uint64_t pts = 0;
        uint32_t width = 0;
        uint32_t height = 0;
        
        ImageFormat format = kImageNone;
        uint8_t* data = nullptr;
		uint32_t nDataBufferSize = 0;	//	数据的缓存大小
		uint32_t nDataSize = 0;			//	数据的实际大小

        FrameBase();
		virtual ~FrameBase();
        FrameBase(ImageFormat format, uint32_t w, uint32_t h);
        FrameBase(const FrameBase & other);
        virtual FrameBase & operator=(const FrameBase & other);
        bool ReSizeFrame(uint32_t size);
    };*/

#pragma  pack(1)
	typedef struct tagVideoData
	{
		bool bH264 = true;
		bool bKeyFrame = false;

		int nVideoW = 0;
		int nVideoH = 0;
		int nGop = 0;
		float fFps = 0.0;
		long long llPts = 0;
		int  nDataLength = 0;
		void ClearData()
		{
			bH264 = true;
			nVideoW = 0;
			nVideoH = 0;
			llPts = 0;
			nDataLength = 0;
			nGop = 0;
			fFps = 0.0;
			bKeyFrame = false;
		}

	}tagVideoData;
#pragma  pack()

#pragma  pack(1)
	typedef struct tagVideoDataIndex
	{
		long long llPts = 0;
		long long llDataIndex = 0;
		bool bFrameKey = false;

		void ClearData()
		{
			llPts = 0;
			llDataIndex = 0;
			bFrameKey = false;
		}

	}tagVideoDataIndex;
#pragma  pack()
} // namespace LPM


#endif // TYPEDEFS_H
