﻿#pragma once

/*
******		FileName	:	LMPThread.h
******		Describe	:	此文件是c++ 接口， 主要用于线程的操作
******		Date		:	2020-12-30
******		Author		:	En
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库, 2、所有字符编码采用u8格式 3、只支持32位 4、需要管理权限等
*/


#include <string>
#include <mutex>
#include <condition_variable>
#include <chrono>

#include <Windows.h>

#include "LPMCommonLib.h"

using namespace std;

namespace LPM
{
	//	线程类
	class LPM_PUBLIC_API CThreadEx
	{
	public:
		CThreadEx(char* _szThreadInfo = (char*)"" );
		virtual ~CThreadEx();

		//	线程级别 0 普通 ， -1 低 ， 1 高
		bool ThreadStart(int _nThreadLevel = 0);
		bool ThreadStop(void);
		bool ThreadIsStop();

		void ThreadSetWantToExit();	//	设置线程想退出
		bool ThreadIsWantExit();	//	线程是否想退出

		//	获取线程id
		long ThreadGetId();
		//	获取线程返回值
		long ThreadGetRet();

		//	挂起线程
		bool ThreadSuspend();	
		//	继续线程
		bool ThreadContine();	
		//	线程是否被挂起
		bool ThreadIsSuspend();	

	protected:

		virtual int ThreadMainLoop(void) = 0;

		//	线程挂起， 被唤醒后，返回true， 未被挂起， 返回false
		bool ThreadCtrl();

	private:

		static unsigned long __stdcall WorkThread(void *param)
		{
			return ((CThreadEx *)param)->ThreadProc();
		}
		unsigned long ThreadProc();

		void ThreadWaitForStop(void);

		bool ApplyPriority(unsigned int _nPriority);

	private:

		bool m_bIsExitThread = false;
		bool m_bWantToExistThread = false;

		void* m_pThead = nullptr;
		void* m_pSafeThread = nullptr;
		long  m_lThreadId = 0;

		std::string* m_pStrThreadInfo = nullptr;

		//	
		std::mutex* m_pMutexThread = nullptr;
		std::condition_variable* m_pCvThread = nullptr;

		bool m_bThreadCtl = false;
		long m_lThreadRet = 0;

		int m_nThreadLevel = 0;

	};

	//	事件
	class LPM_PUBLIC_API CEventEx
	{
	public:
		CEventEx();
		~CEventEx();

		//	阻塞等待， 必需要唤醒，才能继续
		unsigned int EventWait();

		//	阻塞等待， 超时设置(毫秒),  0 是被唤醒, 大于0 是超时
		unsigned int EventWaitFor(long _lDuration);
		//	随机唤醒
		unsigned int EventNotify(unsigned int _unId);
		//	获取被唤醒的值
		unsigned int EventNofifyGet(unsigned int& _unId);
		//	唤醒所有
		unsigned int EventNotifyAll(unsigned int _unId);

	private:
		CEventEx(const CEventEx &) = delete;
		CEventEx & operator = (const CEventEx &) = delete;

	private:

		std::mutex* m_mutexWait = nullptr;
		std::condition_variable* m_cvWait = nullptr;

		unsigned int m_nId = 0;
	};
	
	//	线程锁
	class LPM_PUBLIC_API CLock
	{
	public:
		CLock(void);

		~CLock(void);

		void Lock();

		void UnLock();

	private:
		#ifdef WIN32
		//CRITICAL_SECTION m_cs;

		SRWLOCK m_lock;
		#endif
	};

	//	线程自动锁
	class LPM_PUBLIC_API CAutoLock
	{
	public:
		CAutoLock(CLock* _pLock);

		~CAutoLock();

	private:
		CLock* m_lock= nullptr;
	};

	class LPM_PUBLIC_API CRunTime
	{
	public:
		CRunTime();
		~CRunTime();

		void Reset();
		double GetRunTime();

	public:
		
		static long long GetCurTickCount();
		//	毫秒
		static double GetintervalTime(const long long _llCur, const long long _llPre);

	private:

		long long m_llStartTime =0;
		long long m_llEndTime = 0;

		double m_dbPart = 0;
	};

}