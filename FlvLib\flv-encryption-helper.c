﻿/*
 * Filename:  flv-encryption-helper.c
 * Project :  libflv
 * Created by <PERSON><PERSON> on 12/06/2020.
 * Copyright © 2020 Guangzhou AiYunJi Inc. All rights reserved.
 */
#include "flv-encryption-helper.h"
#include "flv-encryption.h"
#include <stdio.h>
#include <assert.h>

int get_flv_encry_version(const char * filename)
{
    FILE * fp = fopen(filename, "rb");
    if (NULL == fp) {
        return -1;
    }

    char ret = -1;
    // Offset to the first STREAM ID field
    fseek(fp, 21, SEEK_SET);
    fread(&ret, 1, 1, fp);
    fclose(fp);
    return ret & 0xff;
}

static int flv_encrypt_h264_v1(char * h264, int size)
{
    if (NULL == h264 || size <= 4) {
        return -1;
    }
    int offset = -1;
    for (int i = 0; i < size - 4; ++i) {
        char * p = h264;
        unsigned char c = p[i] & 0xff;
        unsigned char c1 = p[i+1] & 0xff;
        unsigned char c2 = p[i+2] & 0xff;
        unsigned char c3 = p[i+3] & 0xff;
        // 06 05 ff ff
        if (c == 0x06 && c1 == 0x05 && c2 == 0xff && c3 == 0xff) {
            offset = i + 705; // The fixed encryption offset for the AVC sequence header is 705
            break;
        }
        // NALU Header + Slice
        // 65 88
        if (c == 0x65 && c1 == 0x88) {
            offset = i + 40;
            break;
        }
    }
    assert(offset != -1);
    assert(size >= offset);
    return flv_encrypt(h264 + offset, size - offset);
}

int flv_encrypt_h264(char * h264, int size, encry_version version)
{
    if (NULL == h264) {
        return -1;
    }
    
    int ret = -1;
    switch (version) {
    case encry_version_0:
        break;
    case encry_version_1:
        ret = flv_encrypt_h264_v1(h264, size);
        break;
    default:
        break;
    }
    return ret;
}

int flv_dencrypt_h264(char * h264, int size, encry_version version)
{
    return flv_encrypt_h264(h264, size, version);
}

