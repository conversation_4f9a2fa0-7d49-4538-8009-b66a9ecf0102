#include "VideoCapturerEx.h"

#include "../../Depends/Common/include/LPMCommonLib.h"



#include "resource.h"
#include <list>

#pragma  comment(lib , "../Depends/Common/lib/LPMCommonLib.lib")
#ifdef _DEBUG

#pragma  comment(lib , "../Depends/OpenCv/lib/opencv_world480d.lib")

#else

#pragma  comment(lib , "../Depends/OpenCv/lib/opencv_world480.lib")

#endif // _DEBUG

#include <opencv2/imgproc.hpp>

#include <opencv2/imgproc/types_c.h>

using namespace  LPM;
namespace AYJ
{
	
	CVideoCapturerEx::CVideoCapturerEx()
	{
		
	}
	CVideoCapturerEx::~CVideoCapturerEx()
	{
		CaptureRelease();
	}
	//	获取视频设备数量
	int CVideoCapturerEx::CaptureGetDevCount()
	{
		return m_objCaptureBase.GetCapDevCount();
	}
	//	获取视频设备信息
	int CVideoCapturerEx::CaptureGetDevInfo(const int _nDevIndex, tagDevInfo& _objDevInfo)
	{
		return m_objCaptureBase.GetCapDevInfo(m_nCaptureType, _nDevIndex, _objDevInfo);
	}

	//	初始化采集设备
	int CVideoCapturerEx::CaptureInit(const int _nDevIndex, IVideoCaptureCallbackEx* _pCapCallBack)
	{
		m_pCallBack = _pCapCallBack;
		m_nCaptureId = _nDevIndex;

		return m_objCaptureBase.CaptureInit(m_nCaptureType, _nDevIndex,this, &FuncFrameCapturedCallback, &FuncStateCapturedCallback);
	}
	//	释放采集设备
	int CVideoCapturerEx::CaptureRelease()
	{
		int nRet =  m_objCaptureBase.CaptureRelease();

		m_tagSetFormat.nFormate = eImageNone;
		m_tagSetFormat.nVideoH = 0;
		m_tagSetFormat.nVideoW = 0;
		m_tagSetFormat.nFps = 0;

		ThreadSetWantToExit();
		EventNotify(0);

		ThreadStop();

		if (g_pObjNoSignalFrame1)
		{
			delete g_pObjNoSignalFrame1;
			g_pObjNoSignalFrame1 = nullptr;
		}
		if (g_pObjNoSignalFrame2)
		{
			delete g_pObjNoSignalFrame2;
			g_pObjNoSignalFrame2 = nullptr;
		}

		return nRet;
	}

	//	设置设备的参数
	int CVideoCapturerEx::CaptureDevFormatSet(const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut)
	{
		const int nRet = m_objCaptureBase.SetCapDevFormat(m_nCaptureType, _tagFormatmIn, m_tagSetFormat);
		if (nRet == 0)
		{
			_tagFormatOut = m_tagSetFormat;
		}

		return nRet;
	}

	// CaptureStart 开始视频采集
	int CVideoCapturerEx::CaptureStart()
	{
		// 1. 启动底层采集
		int nRet = m_objCaptureBase.CaptureStart();
		if (0 == nRet)
		{
			// 2. 启动处理线程（仅在未运行时启动）
			if (!ThreadIsRunning())
			{
				ThreadStart();
			}

			// 3. 重置断线标志
			m_bIsCaptureBreak = false;
		}
		return nRet;
	}

	// CapturePause 暂停视频采集
	int CVideoCapturerEx::CapturePause()
	{
		// 暂停底层采集，但保持线程运行以便快速恢复
		return m_objCaptureBase.CapturePause();
	}

	// CaptureStop 停止视频采集
	int CVideoCapturerEx::CaptureStop()
	{
		// 1. 停止底层采集
		int nRet = m_objCaptureBase.CaptureStop();

		// 2. 通知线程退出并等待
		ThreadSetWantToExit();
		EventNotify(0);

		return nRet;
	}

	eCapDevState CVideoCapturerEx::CaptureGetState()
	{
		return m_objCaptureBase.CaptureGetState();
	}

	// FuncFrameCapturedCallback 帧数据回调（性能优化版本）
	void CVideoCapturerEx::FuncFrameCapturedCallback(double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nVideoFormate, void* _pUserData)
	{
		CVideoCapturerEx* pThis = static_cast<CVideoCapturerEx*>(_pUserData);
		if (!pThis || !_pFrame || _lFrameSize <= 0)
			return;

		// 1. 性能优化：仅在需要时调整缓冲区大小
		if (pThis->m_vectFrameDataTemp.size() != static_cast<size_t>(_lFrameSize))
		{
			CAutoLock lockTemp(&pThis->m_lockTemp);
			pThis->m_vectFrameDataTemp.resize(_lFrameSize);
		}

		// 2. 性能优化：减少数据拷贝，直接传递原始指针
		if (pThis->m_pCallBack)
		{
			pThis->m_pCallBack->OnFrameCallbackEx(
				pThis->m_nCaptureId,
				_dSampleTime,
				_pFrame,  // 直接使用原始指针，避免拷贝
				_lFrameSize,
				pThis->m_tagSetFormat.nVideoW,
				pThis->m_tagSetFormat.nVideoH,
				_nVideoFormate);
		}

		// 3. 仅在需要时拷贝数据到临时缓冲区（用于无信号检测）
		if (pThis->m_vectFrameDataTemp.size() > 0)
		{
			CAutoLock lockTemp(&pThis->m_lockTemp);
			// 使用更高效的内存拷贝
			std::memcpy(pThis->m_vectFrameDataTemp.data(), _pFrame, _lFrameSize);
		}
	}

	void CVideoCapturerEx::FuncStateCapturedCallback(eCapDevState _nState, void* _pUserData)
	{
		CVideoCapturerEx* pThis = (CVideoCapturerEx*)_pUserData;

		WriteLogEx(ModuleVideo, LogDebug, "Capture Index:%d, state :%d", pThis->m_nCaptureId, _nState);

		if (_nState == CapStateDevBreak)
		{
			
		}else if (_nState == CapStateDevInsertReset)
		{
			pThis->m_objCaptureBase.CaptureRelease();

			pThis->m_objCaptureBase.CaptureInit(pThis->m_nCaptureType, pThis->m_nCaptureId, pThis, FuncFrameCapturedCallback, FuncStateCapturedCallback);
			tagDevSupportedFormate tagTemp;
			pThis->m_objCaptureBase.SetCapDevFormat(pThis->m_nCaptureType, pThis->m_tagSetFormat, tagTemp);
			pThis->m_objCaptureBase.CaptureStart();

			return;
		}

		if (pThis->m_pCallBack)
		{
			pThis->m_pCallBack->OnCapturerStatusEx(pThis->m_nCaptureId, _nState);
		}
	}

	bool CVideoCapturerEx::GetNoSignalBmp1(tagVdieoFrame *& nosignalBmp)
	{
		WCHAR DllFilePath[MAX_PATH];
#ifdef UNICODE	
		HMODULE h = GetModuleHandle(L"LPMPaicsVideoLib.dll");
#else // !UNICODE
		HMODULE h = GetModuleHandle("LPMPaicsVideoLib.dll");
#endif
		if (!h) {
			WriteLogEx(ModuleVideo, LogError, "Cann't get LPMPaicsVideoLib.dll module handle");
			return false;
		}
		if (!GetModuleFileName(h, DllFilePath, MAX_PATH)) {
			std::string filename = W2AEx(DllFilePath);
			WriteLogEx(ModuleVideo, LogError, "Cann't get LPMCore.dll module file name[%s]", filename.c_str());
			return false;
		}

		HMODULE hModule;
		hModule = ::LoadLibrary(DllFilePath);
		HRSRC hsrc = FindResource(hModule, MAKEINTRESOURCE(NOSIGNAL1_BITMAP), RT_BITMAP);
		if (!hsrc) {
			WriteLogEx(ModuleVideo, LogError, "Failded to find resource[%x]", hsrc);
			return false;
		}
		HGLOBAL hgbl = LoadResource(hModule, hsrc);
		if (!hgbl) {
			FreeResource(hsrc);
			WriteLogEx(ModuleVideo, LogError, "Failded to load no signal resource[%x]", hgbl);
			return false;
		}

		static const int bmpWidth = 420;
		static const int bmpHeight = 138;
		static const int bitmapHeaderSize = 40;
		nosignalBmp = new tagVdieoFrame(eImageRGB24, bmpWidth, bmpHeight);

		DWORD dwSize = ::SizeofResource(hModule, hsrc);
		if (!dwSize || dwSize < (bmpWidth * bmpHeight * 3 + bitmapHeaderSize)) {
			FreeResource(hsrc);
			WriteLogEx(ModuleVideo, LogError, "Failded to sizeof no signal resource, size[%d]", dwSize);
			return false;
		}
		BYTE *pBt = (BYTE *)LockResource(hgbl);
		pBt += bitmapHeaderSize;
		for (int i = 0; i < bmpHeight; ++i) {
			int step = i * bmpWidth * 3;
			for (int j = 0; j < bmpWidth * 3; ++j) {
				nosignalBmp->pVideoData[step + j]
					= pBt[dwSize - bitmapHeaderSize - step - bmpWidth * 3 + j];
			}
		}
		FreeResource(hsrc);

		return true;
	}

	bool CVideoCapturerEx::GetNoSignalBmp2(tagVdieoFrame *& nosignalBmp)
	{
		WCHAR DllFilePath[MAX_PATH];
#ifdef UNICODE	
		HMODULE h = GetModuleHandle(L"LPMPaicsVideoLib.dll");
#else // !UNICODE
		HMODULE h = GetModuleHandle("LPMPaicsVideoLib.dll");
#endif
		if (!h) {
			WriteLogEx(ModuleVideo, LogError, "Cann't get LPMPaicsVideoLib.dll module handle");
			return false;
		}
		if (!GetModuleFileName(h, DllFilePath, MAX_PATH)) {
			std::string filename = W2AEx(DllFilePath);
			WriteLogEx(ModuleVideo, LogError, "Cann't get LPMCore.dll module file name[%s]", filename.c_str());
			return false;
		}

		HMODULE hModule;
		hModule = ::LoadLibrary(DllFilePath);
		HRSRC hsrc = FindResource(hModule, MAKEINTRESOURCE(NOSIGNAL2_BITMAP), RT_BITMAP);
		if (!hsrc) {
			WriteLogEx(ModuleVideo, LogError, "Failded to find resource[%x]", hsrc);
			return false;
		}
		HGLOBAL hgbl = LoadResource(hModule, hsrc);
		if (!hgbl) {
			FreeResource(hsrc);
			WriteLogEx(ModuleVideo, LogError, "Failded to load no signal resource[%x]", hgbl);
			return false;
		}

		static const int bmpWidth = 120;
		static const int bmpHeight = 120;
		static const int bitmapHeaderSize = 40;
		nosignalBmp = new tagVdieoFrame(eImageRGB24, bmpWidth, bmpHeight);

		DWORD dwSize = ::SizeofResource(hModule, hsrc);
		if (!dwSize || dwSize < (bmpWidth * bmpHeight * 3 + bitmapHeaderSize)) {
			FreeResource(hsrc);
			WriteLogEx(ModuleVideo, LogError, "Failded to sizeof no signal resource, size[%d]", dwSize);
			return false;
		}
		BYTE *pBt = (BYTE *)LockResource(hgbl);
		pBt += bitmapHeaderSize;
		for (int i = 0; i < bmpHeight; ++i) {
			int step = i * bmpWidth * 3;
			for (int j = 0; j < bmpWidth * 3; ++j) {
				nosignalBmp->pVideoData[step + j]
					= pBt[dwSize - bitmapHeaderSize - step - bmpWidth * 3 + j];
			}
		}
		FreeResource(hsrc);

		return true;
	}


	// NoSignal1 无信号检测算法1（性能优化版本）
	bool CVideoCapturerEx::NoSignal1(cv::Mat _objDest, cv::Mat _ObjSrcNosignal)
	{
		// 1. 快速参数检查
		if (_objDest.empty() || _ObjSrcNosignal.empty() ||
			_objDest.type() != CV_8UC3 || _ObjSrcNosignal.type() != CV_8UC3)
			return false;

		// 2. 初始化边界检测变量
		int x_min = _objDest.cols - 1;
		int y_min = _objDest.rows - 1;
		int x_max = 0;
		int y_max = 0;
		const uchar* data = _objDest.data;
		const int cols = _objDest.cols;
		const int rows = _objDest.rows;

		const int bin = 20;  // 二值化阈值
		int threshold_img = 150;

		for (int j = 0; j < _objDest.rows; j += bin)
		{
			int step = _objDest.cols*j * 3;
			for (int i = 0; i < _objDest.cols; ++i)
			{
				if (data[step + i * 3 + 2] > threshold_img)
				{
					if (j + 50 < _objDest.rows)
					{
						if (data[step + (_objDest.cols * 25 + i) * 3 + 2] > threshold_img && data[step + (_objDest.cols * 50 + i) * 3 + 2] > threshold_img)
						{
							if (j < y_min)	y_min = j;
							if (j > y_max)	y_max = j;
							if (i < x_min)	x_min = i;
							if (i > x_max)	x_max = i;
						}
					}
				}
			}
		}

		for (int j = 0; j < _objDest.rows; ++j)
		{
			int step = _objDest.cols*j * 3;
			for (int i = 0; i < _objDest.cols; i += bin)
			{
				if (data[step + i * 3 + 2] > threshold_img)
				{
					if (i + 50 < _objDest.cols)
					{
						if (data[step + (i + 25) * 3 + 2] > threshold_img && data[step + (i + 50) * 3 + 2] > threshold_img)
						{
							if (j < y_min)	y_min = j;
							if (j > y_max)	y_max = j;
							if (i < x_min)	x_min = i;
							if (i > x_max)	x_max = i;
						}
					}
				}
			}
		}

		
		int iwidth = x_max - x_min + 1;
		int iheight = y_max - y_min + 1;

		if (iwidth < 30 || iheight < 30)
			return false;

		int y_start = 0;
		int y_end = _ObjSrcNosignal.rows;
		int x_start = 0;
		int x_end = _ObjSrcNosignal.cols;

		
		if (iwidth < _ObjSrcNosignal.cols)
		{
			if (x_min == 0)
				x_start = x_end - iwidth;
			else if (x_min + iwidth == _objDest.cols)
				x_end = iwidth;
			else
				x_end = x_start + iwidth;
		}
		if (iheight < _ObjSrcNosignal.rows)
		{
			if (y_max == _objDest.rows - 1)
				y_end = y_start + iheight;
			else
				y_end = y_start + iheight;
		}

		double ave = 0;
		uchar* temp_data = _ObjSrcNosignal.data;
		uchar* src_data = _objDest.data;
		for (int j = y_start; j < y_end; ++j)
		{
			for (int i = x_start; i < x_end; ++i)
			{
				int src_y = j + y_min - y_start;
				int src_x = i + x_min - x_start;
				if (src_y < _objDest.rows &&src_x < _objDest.cols)
				{
					int sub = abs(temp_data[(j*_ObjSrcNosignal.cols + i) * 3] - src_data[((j + y_min - y_start)*_objDest.cols + i + x_min - x_start) * 3]);
					ave += sub;
				}
			}
		}
		ave = (double)ave / ((y_end - y_start)*(x_end - x_start));

		if (ave < 20)
			return true;
		return false;
	}


	bool CVideoCapturerEx::NoSignal2(cv::Mat _objDest, cv::Mat _ObjSrcNosignal)
	{
		if (_objDest.empty() || _ObjSrcNosignal.empty())
			return false;

		
		int x_min = _objDest.cols - 1;
		int y_min = _objDest.rows - 1;
		int x_max = 0;
		int y_max = 0;
		uchar* data = _objDest.data;

		int bin = 20;
		int threshold_img = 150;

		for (int j = 0; j < _objDest.rows; j += bin)
		{
			int step = _objDest.cols*j * 3;
			for (int i = 0; i < _objDest.cols; ++i)
			{
				if (data[step + i * 3 + 1] > threshold_img)
				{
					if (j < y_min)	y_min = j;
					if (j > y_max)	y_max = j;
					if (i < x_min)	x_min = i;
					if (i > x_max)	x_max = i;
				}
			}
		}

		for (int j = 0; j < _objDest.rows; ++j)
		{
			int step = _objDest.cols*j * 3;
			for (int i = 0; i < _objDest.cols; i += bin)
			{
				if (data[step + i * 3 + 1] > threshold_img)
				{
					if (j < y_min)	y_min = j;
					if (j > y_max)	y_max = j;
					if (i < x_min)	x_min = i;
					if (i > x_max)	x_max = i;
				}
			}
		}

		x_min = __max(0, x_min - 50);
		x_max = __min(_objDest.cols-1, x_max + 50);
		y_min = __max(0, y_min - 50);
		y_max = __min(_objDest.rows-1, y_max + 50);

		int iwidth = x_max - x_min + 1;
		int iheight = y_max - y_min + 1;

		if (iwidth < 100 || iheight < 100)
			return false;

		cv::Mat src_mini = _objDest(cv::Rect(x_min, y_min, iwidth, iheight));
		cv::Mat src_hsv;
		cv::cvtColor(src_mini, src_hsv, CV_BGR2HSV);
		cv::Mat green_mask;
		inRange(src_hsv, cv::Scalar(35, 43, 2), cv::Scalar(99, 255, 255), green_mask);

		uchar* data_gray = green_mask.data;
		x_min = green_mask.cols-1;
		x_max = 0;
		y_min = green_mask.rows-1;
		y_max = 0;
		for (int j = 1; j < green_mask.rows - 1; ++j)
		{
			int step = green_mask.cols*j;
			for (int i = 1; i < green_mask.cols - 1; ++i)
			{
				if (data_gray[step + i] == 255)
				{
					int pixel_count = (data_gray[step + i + 1] == 255)
						+ (data_gray[step + i - 1] == 255)
						+ (data_gray[step + i + green_mask.cols] == 255)
						+ (data_gray[step + i - green_mask.cols] == 255);
					if (pixel_count >= 2)
					{
						if (j < y_min)	y_min = j;
						if (j > y_max)	y_max = j;
						if (i < x_min)	x_min = i;
						if (i > x_max)	x_max = i;
					}
				}
			}
		}

		int y_start = 0;
		int y_end = _ObjSrcNosignal.rows;
		int x_start = 0;
		int x_end = _ObjSrcNosignal.cols;
		iwidth = x_max - x_min + 1;
		iheight = y_max - y_min + 1;

		if (iwidth < 100 || iheight < 100)
			return false;

		//ֻ��ʾ������No Signal
		if (iwidth < _ObjSrcNosignal.cols)
		{
			if (x_min == 0)
				x_start = x_end - iwidth;
			else if (x_min + iwidth == _objDest.cols)
				x_end = iwidth;
			else
				x_end = x_start + iwidth;
		}
		if (iheight < _ObjSrcNosignal.rows)
		{
			if (y_max == _objDest.rows - 1)
				y_end = y_start + iheight;
			else
				y_end = y_start + iheight;
		}

		double ave = 0;
		uchar* temp_data = _ObjSrcNosignal.data;
		uchar* src_data = src_mini.data;
		for (int j = y_start; j < y_end; ++j)
		{
			for (int i = x_start; i < x_end; ++i)
			{
				int src_y = j + y_min - y_start;
				int src_x = i + x_min - x_start;
				if (src_y < _objDest.rows &&src_x < _objDest.cols)
				{
					int sub = abs(temp_data[(j*_ObjSrcNosignal.cols + i) * 3] - src_data[((j + y_min - y_start)*_objDest.cols + i + x_min - x_start) * 3]);
					ave += sub;
				}
			}
		}
		ave = (double)ave / ((y_end - y_start)*(x_end - x_start));

		if (ave < 20)
			return true;
		return false;
	}

	//	�߳�
	int CVideoCapturerEx::ThreadMainLoop(void)
	{
		//	��ȡ�����Ѿ���bgr 24 ��ͼ����
		if (nullptr == g_pObjNoSignalFrame1)
			GetNoSignalBmp1(g_pObjNoSignalFrame1);
		
		if (nullptr == g_pObjNoSignalFrame2)
			GetNoSignalBmp2(g_pObjNoSignalFrame2);
		

		cv::Mat  matNoSignal1(cv::Size(g_pObjNoSignalFrame1->unWidth, g_pObjNoSignalFrame1->unHeight), CV_8UC3, g_pObjNoSignalFrame1->pVideoData);
		cv::Mat  matNoSignal2(cv::Size(g_pObjNoSignalFrame2->unWidth, g_pObjNoSignalFrame2->unHeight), CV_8UC3, g_pObjNoSignalFrame2->pVideoData);

		// 创建临时帧缓冲区（需要在循环结束后释放）
		tagVdieoFrame* pFrameTempYuv420p = new tagVdieoFrame(eImageYuv420p, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH);
		tagVdieoFrame* pFrameTempBgr24 = new tagVdieoFrame(eImageRGB24, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH);
		
		//cv::Mat  matFrameDest1 = cv::imread(	"Z:\\�㷨��Դ\\true_2.png", CV_LOAD_IMAGE_UNCHANGED);

		while (!ThreadIsStop())
		{
			if (m_vectFrameDataTemp.size() > 0)
			{
				if (pFrameTempYuv420p->unHeight != m_tagSetFormat.nVideoH || m_tagSetFormat.nVideoW != pFrameTempYuv420p->unWidth)
				{
					delete pFrameTempYuv420p;
					pFrameTempYuv420p = new tagVdieoFrame(eImageYuv420p, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH);
				}

				if (pFrameTempBgr24->unHeight != m_tagSetFormat.nVideoH || m_tagSetFormat.nVideoW != pFrameTempBgr24->unWidth)
				{
					delete pFrameTempBgr24;
					pFrameTempBgr24 = new tagVdieoFrame(eImageRGB24, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH);
				}

				{
					/*if (m_tagSetFormat.nFormate == eImageMJPG)
					{
						LPM::CAutoLock lockTemp(&m_lockTemp);
						cv::Mat rawData(1, m_vectFrameDataTemp.size(), CV_8UC1, (void*)&m_vectFrameDataTemp[0]);
						
						cv::Mat decodedImage = imdecode(rawData, cv::ImreadModes::IMREAD_COLOR );
						if (decodedImage.data != nullptr)
						{
							memcpy(pFrameTempBgr24->pVideoData, decodedImage.data, pFrameTempBgr24->unDataSize);
						}
						//cv::imshow("df", decodedImage);
						//cv::waitKey(0);
					}
					else //	yuv��ʽ
					{
						CAutoLock lockTemp(&m_lockTemp);
						YUY2ToI420(&m_vectFrameDataTemp[0], pFrameTempYuv420p->pVideoData, pFrameTempYuv420p->unWidth, pFrameTempYuv420p->unHeight);

						I420ToBGR24(pFrameTempYuv420p->pVideoData, pFrameTempBgr24->pVideoData, pFrameTempYuv420p->unWidth, pFrameTempYuv420p->unHeight);
					}*/
					
						if (m_tagSetFormat.nFormate == eImageMJPG)	//	��Ҫ�Ƚ���
						{
							cv::Mat rawData(1, m_vectFrameDataTemp.size(), CV_8UC1, (uint8_t* )&m_vectFrameDataTemp[0]);
							cv::Mat decodedImage = imdecode(rawData, cv::ImreadModes::IMREAD_COLOR);

							if (decodedImage.data != nullptr)
							{
								memcpy(pFrameTempBgr24->pVideoData, decodedImage.data, pFrameTempBgr24->unDataSize);
							}
						}
						else if (m_tagSetFormat.nFormate == eImageBGR24 || eImageBGRA32 == m_tagSetFormat.nFormate)
						{
							cv::Mat  decodedImage;
							if (m_tagSetFormat.nFormate == eImageBGR24)
								decodedImage = cv::Mat(cv::Size(m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH), CV_8UC3, (void*)&m_vectFrameDataTemp[0]);
							else if (eImageBGRA32 == m_tagSetFormat.nFormate)
								decodedImage = cv::Mat(cv::Size(m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH), CV_8UC4, (void*)&m_vectFrameDataTemp[0]);

							cv::flip(decodedImage, decodedImage, 0);

							g_objImageCvt.ImageConvert(decodedImage.data, m_tagSetFormat.nFormate, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH, pFrameTempBgr24->pVideoData, pFrameTempBgr24->eFrameFormat, pFrameTempBgr24->unWidth, pFrameTempBgr24->unHeight);
						}
						else
						{
							g_objImageCvt.ImageConvert((uint8_t*)&m_vectFrameDataTemp[0], m_tagSetFormat.nFormate, m_tagSetFormat.nVideoW, m_tagSetFormat.nVideoH, pFrameTempBgr24->pVideoData, pFrameTempBgr24->eFrameFormat, pFrameTempBgr24->unWidth, pFrameTempBgr24->unHeight);
						}
				
				}
				//BGR24ToRGB24(pFrameTempRgb24->data, pFrameTempRgb24->width, pFrameTempRgb24->height);

				cv::Mat  matFrameDest = cv::Mat(cv::Size(pFrameTempBgr24->unWidth, pFrameTempBgr24->unHeight), CV_8UC3, pFrameTempBgr24->pVideoData);

				bool bRet = NoSignal1(matFrameDest, matNoSignal1);
				if (bRet && m_pCallBack)
				{
					if (!m_bIsCaptureBreak)
					{
						m_pCallBack->OnCapturerStatusEx(m_nCaptureId, CapStateDevBreak);
						m_bIsCaptureBreak = true;
					}
				}
				else
				{
					bRet = NoSignal2(matFrameDest, matNoSignal2);

					if (bRet && m_pCallBack)
					{
						if (!m_bIsCaptureBreak)
						{
							m_pCallBack->OnCapturerStatusEx(m_nCaptureId, CapStateDevBreak);
							m_bIsCaptureBreak = true;
						}
					}
				}

				if (!bRet)
				{
					if (m_bIsCaptureBreak)
					{
						m_pCallBack->OnCapturerStatusEx(m_nCaptureId, CapStateDevInsertReset);
						m_bIsCaptureBreak = false;
					}	
				}
			}

			EventWaitFor(4.5 * 1000);
			if(ThreadIsWantExit())
				break;
		}

		// 释放临时帧缓冲区（修复内存泄漏）
		if (pFrameTempYuv420p)
		{
			delete pFrameTempYuv420p;
			pFrameTempYuv420p = nullptr;
		}
		if (pFrameTempBgr24)
		{
			delete pFrameTempBgr24;
			pFrameTempBgr24 = nullptr;
		}

		WriteLogEx(ModuleVideo, LogInfo, "ThreadMainLoop exited for capture device %d", m_nCaptureId);
		return 0;
	}

	IVideoCapturerEx* IVideoCapturerEx::Create()
	{
		IVideoCapturerEx* pInstance = new CVideoCapturerEx();

		return pInstance;
	}

	void IVideoCapturerEx::Release(IVideoCapturerEx*& _pObj)
	{
		if (_pObj)
		{
			CVideoCapturerEx* pInstance = (CVideoCapturerEx*)_pObj;

			delete pInstance;
			_pObj = nullptr;
		}
	}
}