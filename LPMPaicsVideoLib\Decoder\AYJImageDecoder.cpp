#include "AYJImageDecoder.h"
#include <io.h>


#define FLV_PACKET_SIZE (2 * 1024 * 1024)

namespace AYJ
{
	CAyjImageDecoder::CAyjImageDecoder()
	{
		
	}

	CAyjImageDecoder::~CAyjImageDecoder()
	{
		
	}

	int CAyjImageDecoder::DecoderInit(int _nVideoW, int _nVideoH, int _nImageFormate)
	{
		if (m_bInit)
			return -2;

		if (_nVideoW <= 0 || _nVideoH <= 0)
		{
			WriteLogEx(ModuleVideo, LogError, "CAyjImageDecoder::DecoderInit error _nVideoW:%d,_nVideoH:%d ", _nVideoW, _nVideoH);
			return -1;
		}
		
		CAutoLock lockTemp(&m_lockApi);

		m_pCodec = avcodec_find_decoder((AVCodecID)_nImageFormate);
		
		
		if (!m_pCodec)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_find_decoder fail");
			return -1;
		}
		m_pCodecCtx = avcodec_alloc_context3(m_pCodec);
		if (!m_pCodecCtx)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_alloc_context3 fail");
			return -1;
		}
		m_pCodecCtx->coded_width = _nVideoW;
		m_pCodecCtx->coded_height = _nVideoH ;

		if (avcodec_open2(m_pCodecCtx, m_pCodec, NULL) < 0)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_open2 fail");
			return -1;
		}

		m_bInit = true;
		av_init_packet(&m_objPacket);

		return 0;

	}
	
	int CAyjImageDecoder::DecoderClose()
	{
		CAutoLock lockTemp(&m_lockApi);

		if (m_pCodecCtx != nullptr) {
			avcodec_free_context(&m_pCodecCtx);
			m_pCodecCtx = nullptr;
		}

		avcodec_close(m_pCodecCtx);

		return 0;
	}

	int CAyjImageDecoder::DecoderFlush()
	{
		if (m_pCodecCtx)
			avcodec_flush_buffers(m_pCodecCtx);
		if (m_pCodecCtx && m_pCodecCtx->codec->flush)
			m_pCodecCtx->codec->flush(m_pCodecCtx);
		return 0;
	}

	int CAyjImageDecoder::Decoder(unsigned char* _ucData, int _nDataSize, unsigned char* _unDataOut)
	{
		if (!m_bInit)
			return -1;
		
		CAutoLock lockTemp(&m_lockApi);

		//av_packet_from_data(&m_objPacket, _ucData, _nDataSize);
		m_objPacket.data = _ucData;
		m_objPacket.size = _nDataSize;
		m_objPacket.stream_index = 0;
		if (m_objPacket.stream_index == AVMEDIA_TYPE_VIDEO)
		{
			int nRet = avcodec_send_packet(m_pCodecCtx, &m_objPacket);
			if (nRet >=0)
			{
				nRet = avcodec_receive_frame(m_pCodecCtx, &m_pAvFrameOut);
				if (nRet >= 0)
				{
					return 0;
				}
			}
		}

		return -1;
	}

}