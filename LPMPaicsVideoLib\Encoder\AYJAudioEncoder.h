﻿/*
******		FileName	:	AYJVideoEncoder.h
******		Describe	:	此文件是c++ 接口,主要负责视频的编解码
******		Date		:	2021-07-20
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
*/
#ifndef __AJY_AUDIO_ENCODER_H__
#define __AJY_AUDIO_ENCODER_H__

#ifdef __cplusplus
extern "C" {
#endif
#include "libavcodec/avcodec.h"
#include "libavutil/imgutils.h"
#include "libavfilter/avfilter.h"
#include "libavfilter/buffersink.h"
#include "libavfilter/buffersrc.h"
#include "libswscale/swscale.h"
#include "libavutil/imgutils.h"
#include "libavformat/avformat.h"
#include "libavutil/opt.h"
#include "libswresample/swresample.h"
#ifdef __cplusplus
}
#endif


#include "../../Depends/Common/include/LPMCommonLib.h"

#include <tuple>

//	音频编码 ， pcm 转 aac
namespace AYJ
{

    class CAyjAuidoEncoder 
    {
    public:
		CAyjAuidoEncoder();
        ~CAyjAuidoEncoder();

		//	编码器初始化
        int EncoderInit(int _nSampleRate, int _nChannels, int _nBitsPerSample, int _nAvgBytesPerSec, const char* _pSaveAacPath = nullptr);
		//	编码器关闭
		int EncoderClose(void);

		//	编码数据
		int Encoder(unsigned char* _ucDataIn, int _nDataSize, unsigned char**  _szDataOut, int& _nSizeOut, long long _llPts);
		

		int EncoderRefresh();

		long long EncoderGetWritePts();

    private:
       

	private:

		int InitFilters(const char* _szFiltersDescr);
		AVFilterGraph *m_pFilterGraph = nullptr;
		AVFilterContext *buffersink_ctx = nullptr;
		AVFilterContext *buffersrc_ctx = nullptr;

    private:
	
		bool  m_bH264 = true;
        bool  m_bOpen = false;
		
		long long m_llEncodePts = 0;

		AVSampleFormat m_nSrcFormate = AV_SAMPLE_FMT_NONE;
		int m_nBitsPerSample = 8;	//	位深

		AVCodec *				m_pAVCodec = nullptr;
		AVCodecContext *		m_pAVContext = nullptr;
		SwrContext*				m_pAudioConvertCtx = nullptr;

		AVPacket *             m_pAVPacket = nullptr;
		AVFrame *              m_pAVFrame = nullptr;
		AVFrame *			   m_pFrameTemp = nullptr;
	
		//音频流  用于写aac 测试文件
		AVFormatContext*		m_pFormateContext = nullptr;
		AVStream*				m_pAudioStream = nullptr;
		string					m_strAacFileName = "D://audio//myTest.aac";

        int   m_bframes = 0;
       
        LPM::CLock				   m_lockApi;

		unsigned char*		m_szEncoderAacData = nullptr;
		int					m_nEncoderAacDataSize = 0;
    };
} // namespace lpm

#endif // !__FF_VIDEO_ENCODER_H__
