﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Packet|Win32">
      <Configuration>Packet</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Packet|x64">
      <Configuration>Packet</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5F182351-5169-450F-8BDA-53A68DEDE39C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>LPMPaicsVideoLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Packet|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Packet|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>../Depends\DShowBaseclasses\include;../Depends\FlvLib\include;../Depends/YuvLib/include;../Depends/FFmpeg-4.0/include;../Depends\OpenCv\include;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>../Depends\DShowBaseclasses\include;../Depends/FlvLib/include;../Depends/YuvLib/include;../Depends\OpenCv\include;../Depends/FFmpeg-4.0/include;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Packet|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IncludePath>../Depends\DShowBaseclasses\include;../Depends/FlvLib/include;../Depends/YuvLib/include;../Depends\OpenCv\include;../Depends/FFmpeg-4.0/include;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>$(ProjectDir);$(SolutionDir)Depends\Common\include;</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <PreBuildEvent>
      <Command>
      </Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Command>
md "$(SolutionDir)VideoLibPack\include\Capture"
md "$(SolutionDir)VideoLibPack\include\Common"
md "$(SolutionDir)VideoLibPack\include\Encoder"
md "$(SolutionDir)VideoLibPack\include\Recorder"
md "$(SolutionDir)VideoLibPack\include\Player"

md "$(SolutionDir)VideoLibPack\lib\$(Platform)"

md "$(SolutionDir)VideoLibPack\bin\$(Platform)"

xcopy "$(SolutionDir)LPMPaicsVideoLib\LPMPaicsVideoLib.h" "$(SolutionDir)VideoLibPack\include"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Capture\IVideoCapturer.h" "$(SolutionDir)VideoLibPack\include\Capture"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Capture\IFpsAdapter.h" "$(SolutionDir)VideoLibPack\include\Capture"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Common\typedefs.h" "$(SolutionDir)VideoLibPack\include\Common"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Encoder\IVideoEncoder.h" "$(SolutionDir)VideoLibPack\include\Encoder"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Recorder\IVideoRecorder.h" "$(SolutionDir)VideoLibPack\include\Recorder"  /s /h /d /y
xcopy "$(SolutionDir)LPMPaicsVideoLib\Player\IVideoPlayer.h" "$(SolutionDir)VideoLibPack\include\Player"  /s /h /d /y

xcopy "$(OutDir)$(ProjectName).lib" "$(SolutionDir)VideoLibPack\lib\$(Platform)" /s /h /d /y
xcopy "$(OutDir)*.dll" "$(SolutionDir)VideoLibPack\bin\$(Platform)" /s /h /d /y

</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(SolutionDir)Depends\Common\include;</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Packet|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;LPMPAICSVIDEOLIB_EXPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <AdditionalIncludeDirectories>$(ProjectDir);$(SolutionDir)Depends\Common\include;</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>
      </IgnoreSpecificDefaultLibraries>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Capture\AudioCapturer.h" />
    <ClInclude Include="Capture\CaptureQEdit.h" />
    <ClInclude Include="Capture\IAudioCapturer.h" />
    <ClInclude Include="Capture\VideoCaptureBase.h" />
    <ClInclude Include="Capture\IVideoCapturerEx.h" />
    <ClInclude Include="Capture\VideoCapturerEx.h" />
    <ClInclude Include="common\AyjVideoCommDefine.h" />
    <ClInclude Include="Common\Typedefs.h" />
    <ClInclude Include="Decoder\AYJCommVideoPlayer.h" />
    <ClInclude Include="Decoder\AYJImageDecoder.h" />
    <ClInclude Include="Decoder\AYJVideoDecoder.h" />
    <ClInclude Include="Decoder\AYJVideoReader.h" />
    <ClInclude Include="Decoder\FrameSeekerEx.h" />
    <ClInclude Include="Decoder\IAYJVideoDecoder.h" />
    <ClInclude Include="Decoder\IAYJVideoPlayer.h" />
    <ClInclude Include="Encoder\AYJAudioEncoder.h" />
    <ClInclude Include="Encoder\AYJVideoEncoder.h" />
    <ClInclude Include="Encoder\IAYJVideoEncoder.h" />
    <ClInclude Include="ImageScale\ImageScale.h" />
    <ClInclude Include="recorder\AYJMp4Write.h" />
    <ClInclude Include="recorder\AYJVideoWrite.h" />
    <ClInclude Include="recorder\IAYJMp4Writer.h" />
    <ClInclude Include="recorder\IAYJVideoWriter.h" />
    <ClInclude Include="RenderSdl\InterfaceRender.h" />
    <ClInclude Include="RenderSdl\RenderDx.h" />
    <ClInclude Include="RenderSdl\RenderSdl.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="VideoInterface.h" />
    <ClInclude Include="LPMPaicsVideoLib.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Capture\AudioCapturer.cpp" />
    <ClCompile Include="Capture\VideoCaptureBase.cpp" />
    <ClCompile Include="Capture\VideoCapturerEx.cpp" />
    <ClCompile Include="Common\Typedefs.cpp" />
    <ClCompile Include="Decoder\AYJCommVideoPlayer.cpp" />
    <ClCompile Include="Decoder\AYJImageDecoder.cpp" />
    <ClCompile Include="Decoder\AYJVideoDecoder.cpp" />
    <ClCompile Include="Decoder\AYJVideoReader.cpp" />
    <ClCompile Include="Decoder\FrameSeekerEx.cpp" />
    <ClCompile Include="dllmain.cpp">
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</CompileAsManaged>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Packet|Win32'">
      </PrecompiledHeader>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</CompileAsManaged>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Packet|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Packet|x64'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Encoder\AYJAudioEncoder.cpp" />
    <ClCompile Include="Encoder\AYJVideoEncoder.cpp" />
    <ClCompile Include="Encoder\IAYJVideoEncoder.cpp" />
    <ClCompile Include="ImageScale\ImageScale.cpp" />
    <ClCompile Include="recorder\AYJMp4Write.cpp" />
    <ClCompile Include="recorder\AYJVideoWrite.cpp" />
    <ClCompile Include="RenderSdlInterface.cpp" />
    <ClCompile Include="RenderSdl\RenderDx.cpp" />
    <ClCompile Include="RenderSdl\RenderSdl.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="LPMPaicsVideoLib.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\bitmap1.bmp" />
    <Image Include="res\NoSignal.bmp" />
    <Image Include="res\NoSignal2.bmp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>