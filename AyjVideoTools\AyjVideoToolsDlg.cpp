﻿
// AyjVideoToolsDlg.cpp: 实现文件
//

#include "pch.h"
#include "framework.h"
#include "AyjVideoTools.h"
#include "AyjVideoToolsDlg.h"
#include "afxdialogex.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif


#include "../LPMPaicsVideoLib/VideoInterface.h"

#pragma comment(lib, "../Depends/Common/lib/LPMCommonLib.lib")

// 用于应用程序“关于”菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CAyjVideoToolsDlg 对话框



CAyjVideoToolsDlg::CAyjVideoToolsDlg(CWnd* pParent /*=nullptr*/)
	: CDialogEx(IDD_AYJVIDEOTOOLS_DIALOG, pParent)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CAyjVideoToolsDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, ID_BtnVideoShow, m_ctrlVideoShow);
	DDX_Control(pDX, IDC_EDIT_VideoPath, m_editVideoPath);
	DDX_Control(pDX, IDC_EDIT_VideoPathOut, m_editExportPath);
	DDX_Control(pDX, IDC_SLIDER_Step, m_ctrlVideoProgress);
	DDX_Control(pDX, ID_Btn_Video_Init, m_btnVideoInit);
	DDX_Control(pDX, ID_Btn_Video_Release, m_btnVideoRelease);
	DDX_Control(pDX, ID_Btn_Video_Play, m_btnVideoPlay);
	DDX_Control(pDX, ID_Btn_Video_Pause, m_btnVideoPause);
	DDX_Control(pDX, ID_Btn_Video_Cur_Time, m_ctrlShowCurTime);
	DDX_Control(pDX, ID_Btn_Video_Length, m_ctrlShowVideoLength);
	DDX_Control(pDX, IDC_STATIC_Status, m_ctrlPlayStatus);
	DDX_Control(pDX, IDC_staic_OutPregress, m_ctrlExportProgress);
	DDX_Control(pDX, IDC_PROGRESS_Export, m_ctrlExportPorgress1);
	DDX_Control(pDX, IDC_STATIC_ExportPro, m_ctrlExportProgerss2);
	DDX_Control(pDX, IDC_STATIC_ExportPath, m_ctrlExportPath);
	DDX_Control(pDX, ID_Btn_Video_Export, m_btnExport);
	DDX_Control(pDX, ID_Btn_Video_StopExport, m_btnStopExport);
}

BEGIN_MESSAGE_MAP(CAyjVideoToolsDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_BN_CLICKED(ID_Btn_Video_Path_Get, &CAyjVideoToolsDlg::OnBnClickedBtnVideoPathGet)
	ON_BN_CLICKED(ID_Btn_Video_Init, &CAyjVideoToolsDlg::OnBnClickedBtnVideoInit)
	ON_EN_CHANGE(IDC_EDIT_VideoPath, &CAyjVideoToolsDlg::OnEnChangeEditVideopath)
	ON_BN_CLICKED(ID_Btn_Video_Export, &CAyjVideoToolsDlg::OnBnClickedBtnVideoExport)
	ON_BN_CLICKED(ID_Btn_Video_Release, &CAyjVideoToolsDlg::OnBnClickedBtnVideoRelease)
	ON_BN_CLICKED(ID_Btn_Video_Play, &CAyjVideoToolsDlg::OnBnClickedBtnVideoPlay)
	ON_BN_CLICKED(ID_Btn_Video_Pause, &CAyjVideoToolsDlg::OnBnClickedBtnVideoPause)
	ON_BN_CLICKED(ID_Btn_Video_Path_GetOut, &CAyjVideoToolsDlg::OnBnClickedBtnVideoPathGetout)
	ON_BN_CLICKED(IDCANCEL, &CAyjVideoToolsDlg::OnBnClickedCancel)
	ON_MESSAGE(UM_UPDATE_PALYER_STATUS, &CAyjVideoToolsDlg::OnUpdatePlayerStatus)
	ON_MESSAGE(UM_UPDATE_PLAY_TIME, &CAyjVideoToolsDlg::OnUpdatePlayingTime)
	ON_MESSAGE(UM_EXPORT_VIDEO_PRO, &CAyjVideoToolsDlg::OnExportProgress)


	ON_WM_HSCROLL()
	ON_BN_CLICKED(ID_Btn_Video_StopExport, &CAyjVideoToolsDlg::OnBnClickedBtnVideoStopexport)
	ON_WM_LBUTTONDOWN()
END_MESSAGE_MAP()


// CAyjVideoToolsDlg 消息处理程序
#include<unordered_set>  
BOOL CAyjVideoToolsDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将“关于...”菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != nullptr)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// TODO: 在此添加额外的初始化代码
	RECT rect;
	m_ctrlVideoShow.GetWindowRect(&rect);
	int nW = rect.right - rect.left;
	int nH = rect.bottom - rect.top;
	//	渲染
	if (m_pObjRenderPlay == nullptr)
	{
		HWND hWnd = m_ctrlVideoShow.GetSafeHwnd(); // 取得控件的指针
		m_pObjRenderPlay = LPM::RenderCreate((unsigned long)hWnd, m_nRenderPlayWidth, n_nRenderPlayHeight, true);
	}

	//m_ctrlVideoShow.SetWindowRect(rect);
	m_btnVideoPlay.EnableWindow(FALSE);
	m_btnVideoPause.EnableWindow(FALSE);
	m_btnVideoInit.EnableWindow(TRUE);
	m_btnVideoRelease.EnableWindow(FALSE);

	m_btnExport.EnableWindow(TRUE);
	m_btnStopExport.EnableWindow(FALSE);

	m_editVideoPath.SetWindowTextW(L"D:\\Pic\\VideoTest.video");

	m_ctrlVideoProgress.SetRange(1, m_nRange);

	//m_ctrlVideoProgress.SetLineSize(2);
	//int iSize = m_ctrlVideoProgress.GetLineSize();

	m_ctrlExportPorgress1.SetRange(1, m_nRange);
	m_ctrlExportPorgress1.SetPos(1);
	//设置和获取按下PageUp、PageDown按钮时时滑块移动的步长（移动的幅度）
	//m_ctrlVideoProgress.SetPageSize(6);


	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CAyjVideoToolsDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

void CAyjVideoToolsDlg::OnBnClickedCancel()
{
	if (m_pVideoExport)
	{
		m_pVideoExport->Stop();
		m_pVideoExport->Release();
		delete m_pVideoExport;
		m_pVideoExport = nullptr;
	}

	if (m_pObjRenderPlay)
		LPM::RenderRelease(m_pObjRenderPlay);
	if (m_pVideoPlayer)
		LPM::IAyjVideoPlayer::Release(m_pVideoPlayer);


	CDialogEx::OnCancel();
}

LRESULT CAyjVideoToolsDlg::OnUpdatePlayerStatus(WPARAM wParam, LPARAM lParam)
{
	LPM::enPlayerStatus event = (LPM::enPlayerStatus)wParam;
	CString text = L"";
	switch (event) {
	case LPM::enReaderPlaying:
		text.Append(L"Playing");
		break;
	case LPM::enReaderStopped:
		text.Append(L"Stopped");
		break;
	case LPM::enReaderPaused:
		text.Append(L"Paused");
		break;
	case LPM::enReaderEndReached:
		text.Append(L"Play End");
		break;
	default:
		text.Append(L"unkonw");
		break;
	}
	m_ctrlPlayStatus.SetWindowText(text);
	return 0;
}

LRESULT CAyjVideoToolsDlg::OnUpdatePlayingTime(WPARAM wParam, LPARAM lParam)
{
	//if (2 == lParam)
	{
		CString str(TimerGetStamp(wParam).c_str());
		m_ctrlShowCurTime.SetWindowTextW(str);

		int nPos = (float)m_nRange/m_llVideoLength * wParam;
		m_ctrlVideoProgress.SetPos(nPos);
		
	}
	return LRESULT();
}

LRESULT  CAyjVideoToolsDlg::OnExportProgress(WPARAM wParam, LPARAM lParam)
{
	long long llCur = wParam;
	long long llLenth = lParam;
	float fPos = (float)llCur / llLenth;
	int nPos = fPos * m_nRange;
	m_ctrlExportPorgress1.SetPos(nPos);

	char szProgress[100] = {};
	sprintf_s(szProgress, "%0.2f%%",  fPos * 100);
	m_ctrlExportProgerss2.SetWindowTextW(CStringW(szProgress));


	char sz[100] = {};
	sprintf_s(sz, "%s", TimerGetStamp(llCur).c_str());
	m_ctrlExportProgress.SetWindowTextW(CStringW(sz));
	if (llCur >= llLenth)
	{
		OnBnClickedBtnVideoStopexport();
	}

	return LRESULT(0);
}

void CAyjVideoToolsDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CAyjVideoToolsDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

int CAyjVideoToolsDlg::OnPlayerStatusCb(LPM::enPlayerStatus _enStatus)
{
	PostMessage(UM_UPDATE_PALYER_STATUS, _enStatus, 0);
	return 0;
}
//  播放器帧回调
int CAyjVideoToolsDlg::OnPlayerFrameCb(LPM::FrameBase* _objFrame)
{
	if (_objFrame->format == 0 && _objFrame->width == m_nRenderPlayWidth && n_nRenderPlayHeight == _objFrame->height)
	{
		LPM::RenderPutYuv420pData(m_pObjRenderPlay, _objFrame->data, _objFrame->width, _objFrame->height);
	}

	PostMessage(UM_UPDATE_PLAY_TIME, _objFrame->pts, 0);

	return 0;
}

void CAyjVideoToolsDlg::OnBnClickedBtnVideoPathGet()
{
	BOOL bIsOpen = TRUE;
	CString defaultDir = L"D:\\";
	CString fileName = L"";
	CString filter = L"文件 (*.video;)|*.video;||";
	CFileDialog openFileDlg(bIsOpen, defaultDir, fileName, OFN_HIDEREADONLY | OFN_READONLY, filter, NULL);
	openFileDlg.GetOFN().lpstrInitialDir = defaultDir;
	INT_PTR result = openFileDlg.DoModal();
	CString filePath = defaultDir + "\\test.mp4";
	if (result == IDOK)
	{
		filePath = openFileDlg.GetPathName();
		m_editVideoPath.SetWindowTextW(filePath);
	}
}

void CAyjVideoToolsDlg::OnBnClickedBtnVideoInit()
{
	std::string strUuid = LPM::GetBoardUUID();
	if (strUuid != "28D6C712-29FE-8549-A8B1-FEA5F13F4810" && "A47D6951-136A-358B-F170-F02F744F4EFC" != strUuid)
	{
		AfxMessageBox(L"此设备, 无权限");
		return;
	}
	CString strVideoPath;
	m_editVideoPath.GetWindowText(strVideoPath);
	if (strVideoPath.IsEmpty())
		return;
	
	strVideoPath = strVideoPath.Left(strVideoPath.GetLength() - 6);
	if (!m_pVideoPlayer)
	{
		m_pVideoPlayer = LPM::IAyjVideoPlayer::Create();
		int nRet = m_pVideoPlayer->PlayerInit(LPM::W2U(strVideoPath.GetBuffer()).c_str(), this);
		if (nRet <0)
		{
			AfxMessageBox(L"初始化错误");
			LPM::IAyjVideoPlayer::Release(m_pVideoPlayer);
			return;
		}

		m_btnVideoPlay.EnableWindow(TRUE);
		m_btnVideoPause.EnableWindow(TRUE);
		m_btnVideoInit.EnableWindow(FALSE);
		m_btnVideoRelease.EnableWindow(TRUE);

		m_llVideoLength = m_pVideoPlayer->PlayerGetLength();
		CString strLength(TimerGetStamp(m_llVideoLength).c_str());
		m_ctrlShowVideoLength.SetWindowText(strLength);
	}
}

void CAyjVideoToolsDlg::OnBnClickedBtnVideoRelease()
{
	if (m_pVideoPlayer)
	{
		LPM::IAyjVideoPlayer::Release(m_pVideoPlayer);
		m_btnVideoPlay.EnableWindow(FALSE);
		m_btnVideoPause.EnableWindow(FALSE);
		m_btnVideoInit.EnableWindow(TRUE);
		m_btnVideoRelease.EnableWindow(FALSE);

		m_llVideoLength = 0;
	}
	if (m_pVideoExport)
	{
		m_pVideoExport->Release();
		delete m_pVideoExport;
		m_pVideoExport = nullptr;
	}
}


void CAyjVideoToolsDlg::OnBnClickedBtnVideoPlay()
{
	if (m_pVideoPlayer)
	{
		m_pVideoPlayer->PlayerPlay();
	}
}


void CAyjVideoToolsDlg::OnBnClickedBtnVideoPause()
{
	if (m_pVideoPlayer)
	{
		m_pVideoPlayer->PlayerPause();
	}
}

void CAyjVideoToolsDlg::OnEnChangeEditVideopath()
{

}


void CAyjVideoToolsDlg::OnBnClickedBtnVideoExport()
{
	std::string strUuid = LPM::GetBoardUUID();
	if (strUuid != "28D6C712-29FE-8549-A8B1-FEA5F13F4810" && "A47D6951-136A-358B-F170-F02F744F4EFC" != strUuid)
	{
		AfxMessageBox(L"此设备, 无权限");
		return;
	}

	CString strVideoPath;
	m_editVideoPath.GetWindowText(strVideoPath);
	if (strVideoPath.IsEmpty())
		return;

	strVideoPath = strVideoPath.Left(strVideoPath.GetLength() - 6);

	CString strVideoPathExport;
	m_editExportPath.GetWindowText(strVideoPathExport);
	if (strVideoPathExport.IsEmpty())
		return;

	time_t sttime = {};
	struct tm tim = {};
	sttime = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
	wchar_t wszFileName[256] = {};
	localtime_s(&tim, &sttime);

	int sec = tim.tm_sec;           // second (0-61, allows for leap seconds)
	int min = tim.tm_min;           // minute (0-59)
	int hour = tim.tm_hour;          // hour (0-23)
	int mon = tim.tm_mon + 1;       // month (0-11)
	int mday = tim.tm_mday;          // day of the month (1-31)
	int year = tim.tm_year + 1900;    // years since 1900

	swprintf_s(wszFileName, L"%04d%02d%02d_%02d%02d%02d.flv", year, mon, mday, hour, min, sec);

	CString strPath = strVideoPathExport + wszFileName;// _T("")
	
	if (!m_pVideoExport)
	{
		m_pVideoExport = new CAyjVideoExport(this->GetSafeHwnd());
		int nRet = m_pVideoExport->Init(LPM::W2U(strVideoPath.GetBuffer()).c_str(), CStringA(strPath).GetBuffer(), m_nRenderPlayWidth, n_nRenderPlayHeight);
		if (nRet < 0)
		{
			AfxMessageBox(L"导出错误");
			delete m_pVideoExport;
			m_pVideoExport = nullptr;
			return;
		}
		m_pVideoExport->Start();

		m_ctrlExportPath.SetWindowText(CString("Output Path:")+strPath);
		m_btnExport.EnableWindow(FALSE);
		m_btnStopExport.EnableWindow(TRUE);
	}
}

std::string CAyjVideoToolsDlg::TimerGetStamp(unsigned long long _lStamp)
{
	unsigned long long lTime = _lStamp;

	int ms = lTime % 1000;				//  毫秒
	int secs = (lTime /= 1000) % 60;	//	秒
	int mins = (lTime /= 60) % 60;		//	分
	int hours = (lTime /= 60) % 60;		//	时

	char sz[50] = {};
	sprintf_s(sz, 50, "%02d:%02d:%02d %03d", hours, mins, secs, ms);

	return std::string(sz);
}

std::string CAyjVideoToolsDlg::TimerGetStampEx(unsigned long long _lStamp)
{
	unsigned long long lTime = _lStamp;

	int ms = lTime % 1000;				//  毫秒
	int secs = (lTime /= 1000) % 60;	//	秒
	int mins = (lTime /= 60) % 60;		//	分
	int hours = (lTime /= 60) % 60;		//	时

	char sz[50] = {};
	sprintf_s(sz, 50, "%02d%02d%02d%03d", hours, mins, secs, ms);

	return std::string(sz);
}

void CAyjVideoToolsDlg::OnBnClickedBtnVideoPathGetout()
{
	BROWSEINFO bi;
	wchar_t name[MAX_PATH];
	ZeroMemory(&bi, sizeof(BROWSEINFO));
	bi.hwndOwner = AfxGetMainWnd()->GetSafeHwnd();
	bi.pszDisplayName = name;
	bi.lpszTitle = L"选择文件夹目录";
	bi.ulFlags = BIF_RETURNFSANCESTORS;
	LPITEMIDLIST idl = SHBrowseForFolder(&bi);
	if (idl == NULL)
		return ;
	CString strDirectoryPath;
	SHGetPathFromIDList(idl, strDirectoryPath.GetBuffer(MAX_PATH));
	strDirectoryPath.ReleaseBuffer();
	if (strDirectoryPath.IsEmpty())
		return ;
	if (strDirectoryPath.Right(1) != "\\")
		strDirectoryPath += "\\";

	m_editExportPath.SetWindowTextW(strDirectoryPath);
}



void CAyjVideoToolsDlg::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar)
{
	CSliderCtrl* pSlider = (CSliderCtrl*)pScrollBar;
	
	int nPosT = 0;
	if (pSlider->GetDlgCtrlID() == m_ctrlVideoProgress.GetDlgCtrlID())
	{
		nPosT = m_ctrlVideoProgress.GetPos();
		
		long long llCur = (nPosT*m_llVideoLength) / m_nRange;
		if (m_pVideoPlayer)
		{
			m_pVideoPlayer->PlayerSeek(llCur);
		}
		
	}
	__super::OnHScroll(nSBCode, nPos, pScrollBar);
}


void CAyjVideoToolsDlg::OnBnClickedBtnVideoStopexport()
{
	if (m_pVideoExport)
	{
		m_pVideoExport->Stop();
		m_pVideoExport->Release();
		delete m_pVideoExport;
		m_pVideoExport = nullptr;

		m_btnExport.EnableWindow(TRUE);
		m_btnStopExport.EnableWindow(FALSE);
	}
}


void CAyjVideoToolsDlg::OnLButtonDown(UINT nFlags, CPoint point)
{
	// TODO: 在此添加消息处理程序代码和/或调用默认值

	__super::OnLButtonDown(nFlags, point);
}


BOOL CAyjVideoToolsDlg::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult)
{
	// TODO: 在此添加专用代码和/或调用基类
	NMHDR* nmhdr = (LPNMHDR)lParam;
	if (IDC_SLIDER_Step == wParam && NM_RELEASEDCAPTURE == nmhdr->code)
	{
		CPoint point;
		GetCursorPos(&point); //获取点击坐标
		//do something here
		//return FALSE;
		CRect rect;
		m_ctrlVideoProgress.GetWindowRect(&rect);  //获取CSliderCtrl控件相对屏幕的位置

		CRect channelRect;
		m_ctrlVideoProgress.GetChannelRect(&channelRect); //获取滑道相对于控件的位置

		int pointX = point.x - channelRect.left - rect.left;   //clickX是上一段代码清单中获取到的点击横坐标
		int rangeWidth = channelRect.Width();

		int nPosT = (int)pointX * m_ctrlVideoProgress.GetRangeMax() / rangeWidth;
		//m_ctrlVideoProgress.SetPos(nPosT);
		long long llCur = (nPosT*m_llVideoLength) / m_nRange;
		if (m_pVideoPlayer)
		{
			m_pVideoPlayer->PlayerSeek(llCur);
		}else
			m_ctrlVideoProgress.SetPos(nPosT);
		
	}

	return __super::OnNotify(wParam, lParam, pResult);
}
