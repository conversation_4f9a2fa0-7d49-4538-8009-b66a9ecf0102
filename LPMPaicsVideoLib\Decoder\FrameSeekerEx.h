﻿/*
 * Filename:  FrameSeeker.h
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 9/9/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef FRAME_SEEKEREX_H
#define FRAME_SEEKEREX_H

#include "AYJVideoReader.h"


#include "../Common/Typedefs.h"
#include "../Depends/Common/include/LPMCommonLib.h"

class IMutex;
struct AVFormatContext;
struct AVCodecContext;
struct AVCodec;
struct AVFrame;
struct SwsContext;

class CAyjVideoReader;

namespace AYJ
{
    class CFrameSeekerEx : public IFrameSeekerEx
    {
    public:
		CFrameSeekerEx(void);
        virtual ~CFrameSeekerEx(void);

		virtual long long FrameInit(const char * _filepath);

		virtual tagVdieoFrame* FrameSeek(long long _llSeekTime);

		virtual long long FrameGetLength(void);

    private:

		void Release();

    private:
        bool m_bIsInit = false;

		CLock m_lock;

		long long m_llVideoLength = -1;
		float m_fFps = 0.0;

		std::string m_strFilePath;

		CAyjVideoReader* m_pPlayer = nullptr;
    };
}

#endif // !FRAME_SEEKER_H
