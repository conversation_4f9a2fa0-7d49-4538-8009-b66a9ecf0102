﻿/*
 * Filename:  Map.hpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/18/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/

#ifndef __MAP_H__
#define __MAP_H__
#include <common/typedefs.h>
#include <buffer/IMap.hpp>

template<typename Key, typename Val>
class CMap : public IMap<Key, Val>
{
public:
    CMap(bool lock = false, bool wait = false, MemoryReleaseHook hook = NULL);
    virtual ~CMap(void);
    virtual void Start(void);
    virtual bool Empty(void);
    virtual size_t Size(void);
    virtual bool GetVal(const Key & key, Val & val);
    virtual bool GetAllVals(std::vector<Val> & vals);
    virtual bool Pick(const Key & key, Val & val);
    virtual bool PickFront(Key & key, Val & val);
    virtual bool PickFrontVal(Val & val);
    virtual bool PeekFront(Key & key, Val & val);
    virtual bool PeekFrontVal(Val & val);
    virtual bool Push(const Key & key, const Val & val);
    virtual bool Find(const Key & key);
    virtual bool Front(Key & key, Val & val);
    virtual bool Back(Key & key, Val & val);
    virtual const std::map<Key, Val> & toStdMap(void);
    virtual bool SetFormStdMap(const std::map<Key, Val> & map);
    virtual void Stop(void);
    virtual void Clear(void);

    virtual IMap<Key, Val>& operator=(const IMap<Key, Val> & other);

private:
    void _Clear();
    void _Release(Val & val);

private:
    MemoryReleaseHook  m_hook;
};

template<typename Key, typename Val>
CMap<Key, Val>::CMap(bool lock, bool wait, MemoryReleaseHook hook)
    : IMap<Key, Val>(lock, wait)
    , m_hook(hook)
{
}

template<typename Key, typename Val>
CMap<Key, Val>::~CMap(void)
{
    Stop();
}

template<typename Key, typename Val>
inline void CMap<Key, Val>::Start(void)
{
    IMap<Key, Val>::Lock();
    m_stop = false;
    IMap<Key, Val>::Unlock();
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Empty(void)
{
    bool ret = false;
    IMap<Key, Val>::Lock();
    ret = m_map.empty();
    IMap<Key, Val>::Unlock();
    return ret;
}

template<typename Key, typename Val>
inline size_t CMap<Key, Val>::Size(void)
{
    size_t size = 0;
    IMap<Key, Val>::Lock();
    size = m_map.size();
    IMap<Key, Val>::Unlock();
    return size;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::GetVal(const Key & key, Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    if (Find(key)) {
        val = m_map[key];
        IMap<Key, Val>::Unlock();
        return true;
    }

    IMap<Key, Val>::Unlock();
    return false;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::GetAllVals(std::vector<Val>& vals)
{
    IMap<Key, Val>::Lock();

    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    auto itr = m_map.begin();
    for (; itr != m_map.end(); ++itr) {
        vals.push_back(itr->second);
    }

    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Pick(const Key & key, Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    if (Find(key)) {
        val = m_map[key];
        m_map.erase(key);
        IMap<Key, Val>::Unlock();
        return true;
    }

    IMap<Key, Val>::Unlock();
    return false;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::PickFront(Key & key, Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    key = m_map.begin()->first;
    val = m_map.begin()->second;
    m_map.erase(m_map.begin());

    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::PickFrontVal(Val & val)
{
    IMap<Key, Val>::Lock();
    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    val = m_map.begin()->second;
    m_map.erase(m_map.begin());

    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::PeekFront(Key & key, Val & val)
{
    IMap<Key, Val>::Lock();
    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    key = m_map.begin()->first;
    val = m_map.begin()->second;

    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::PeekFrontVal(Val & val)
{
    IMap<Key, Val>::Lock();
    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    while (m_map.empty() && IMap<Key, Val>::Wait()) {
        if (m_stop) {
            IMap<Key, Val>::Unlock();
            return false;
        }
    }

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    val = m_map.begin()->second;

    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Push(const Key & key, const Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }
    m_map[key] = val;
    IMap<Key, Val>::Notify();
    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Find(const Key & key)
{
    bool ret = false;
    IMap<Key, Val>::Lock();
    ret = m_map.find(key) != m_map.end();
    IMap<Key, Val>::Unlock();

    return ret;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Front(Key & key, Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }

    key = (*m_map.begin()).first;
    val = (*m_map.begin()).second;

    IMap<Key, Val>::Unlock();

    return true;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::Back(Key & key, Val & val)
{
    IMap<Key, Val>::Lock();

    if (m_map.empty()) {
        IMap<Key, Val>::Unlock();
        return false;
    }
    auto last = --m_map.end();

    key = (*last).first;
    val = (*last).second;

    IMap<Key, Val>::Unlock();

    return true;
}

template<typename Key, typename Val>
inline const std::map<Key, Val>& CMap<Key, Val>::toStdMap(void)
{
    IMap<Key, Val>::Lock();
    const auto & map = m_map;
    IMap<Key, Val>::Unlock();
    return map;
}

template<typename Key, typename Val>
inline bool CMap<Key, Val>::SetFormStdMap(const std::map<Key, Val>& map)
{
    IMap<Key, Val>::Lock();
    if (m_stop) {
        IMap<Key, Val>::Unlock();
        return false;
    }
    this->m_map = map;
    IMap<Key, Val>::Unlock();
    return true;
}

template<typename Key, typename Val>
inline void CMap<Key, Val>::Stop(void)
{
    IMap<Key, Val>::Lock();
    m_stop = true;
    _Clear();
    IMap<Key, Val>::NotifyAll();
    IMap<Key, Val>::Unlock();
}

template<typename Key, typename Val>
inline void CMap<Key, Val>::Clear(void)
{
    IMap<Key, Val>::Lock();
    _Clear();
    IMap<Key, Val>::NotifyAll();
    IMap<Key, Val>::Unlock();
}

template<typename Key, typename Val>
inline IMap<Key, Val>& CMap<Key, Val>::operator=(const IMap<Key, Val>& other)
{
    IMap<Key, Val>::Lock();
    if (this == &other) {
        IMap<Key, Val>::Unlock();
        return *this;
    }
    this->m_isWait = other.m_isWait;
    this->m_map = other.m_map;
    IMap<Key, Val>::NotifyAll();
    IMap<Key, Val>::Unlock();
    return *this;
}

template<typename Key, typename Val>
inline void CMap<Key, Val>::_Clear()
{
    auto itr = m_map.begin();
    for (; itr != m_map.end(); ++itr) {
        _Release(itr->second);
    }
    m_map.clear();
}

template<typename Key, typename Val>
inline void CMap<Key, Val>::_Release(Val & val)
{
    if (m_hook != NULL) {
        m_hook(val);
    }
}

#endif // !__MAP_H__
