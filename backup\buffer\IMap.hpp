﻿/*
 * Filename:  IMap.hpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/17/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/

#ifndef __IMAP_H__
#define __IMAP_H__
#include <IMutex.h>
#include <ICondition.h>
#include <map>

template<typename Key, typename Val>
class IMap
{
public:
    typedef void(*MemoryReleaseHook)(Val & arg);
    typedef Key key_type;
    typedef Val value_type;
public:
    enum MapType
    {
        Map = 0,
        HashMap,
    };

public:
    static IMap<Key, Val> * Create(MapType type = Map, bool lock = false,
        bool wait = false, MemoryReleaseHook hook = NULL);
    IMap(bool lock = false, bool wait = false);
    virtual ~IMap(void);
    virtual void Start(void) = 0;
    virtual bool Empty(void) = 0;
    virtual size_t Size(void) = 0;
    virtual bool GetVal(const Key & key, Val & val) = 0;
    virtual bool GetAllVals(std::vector<Val> & vals) = 0;
    virtual bool Pick(const Key & key, Val & val) = 0;
    virtual bool PickFront(Key & key, Val & val) = 0;
    virtual bool PickFrontVal(Val & val) = 0;
    virtual bool PeekFront(Key & key, Val & val) = 0;
    virtual bool PeekFrontVal(Val & val) = 0;
    virtual bool Push(const Key & key, const Val & val) = 0;
    virtual bool Find(const Key & key) = 0;
    virtual bool Front(Key & key, Val & val) = 0;
    virtual bool Back(Key & key, Val & val) = 0;
    virtual const std::map<Key, Val> & toStdMap(void) = 0;
    virtual bool SetFormStdMap(const std::map<Key, Val> & map) = 0;
    virtual void Stop(void) = 0;
    virtual void Clear(void) = 0;

    virtual IMap<Key, Val>& operator=(const IMap<Key, Val> & other) = 0;

protected:
    bool Lock(void);
    bool Unlock(void);
    bool Wait(void);
    bool Notify(void);
    bool NotifyAll(void);

public:
    const bool  m_isLock = true;
    bool        m_isWait = false;
    bool        m_stop = true;
    IMutex *    m_lock = nullptr;
    ICondition* m_condition = nullptr;
    std::map<Key, Val> m_map;
};

#include <buffer/Map.hpp>

template<typename Key, typename Val>
inline IMap<Key, Val>* IMap<Key, Val>::
Create(MapType type, bool lock, bool wait, MemoryReleaseHook hook)
{
    IMap<Key, Val> * pMap = NULL;
    switch (type) {
    case Map:
        pMap = new CMap<Key, Val>(lock, wait, hook);
        break;
    case HashMap:
    default:
        break;
    }
    return pMap;
}

template<typename Key, typename Val>
inline IMap<Key, Val>::IMap(bool lock, bool wait)
    : m_isLock(lock)
    , m_condition(NULL)
    , m_isWait(wait)
{
    if (lock) {
        m_lock = IMutex::Create();
        if (wait) {
            m_condition = ICondition::Create(m_lock);
        }
    } else {
        m_isWait = false;
    }
}

template<typename Key, typename Val>
inline IMap<Key, Val>::~IMap(void)
{
    SAFE_DELETE(m_lock);
    SAFE_DELETE(m_condition);
}

template<typename Key, typename Val>
inline bool IMap<Key, Val>::Lock(void)
{
    if (m_isLock) {
        m_lock->Lock();
    }
    return m_isLock;
}

template<typename Key, typename Val>
inline bool IMap<Key, Val>::Unlock(void)
{
    if (m_isLock) {
        m_lock->Unlock();
    }
    return m_isLock;
}

template<typename Key, typename Val>
inline bool IMap<Key, Val>::Wait(void)
{
    if (m_isWait) {
        m_condition->Sleep();
    }
    return m_isWait;
}

template<typename Key, typename Val>
inline bool IMap<Key, Val>::Notify(void)
{
    if (m_isWait) {
        m_condition->Wake();
    }
    return m_isWait;
}

template<typename Key, typename Val>
inline bool IMap<Key, Val>::NotifyAll(void)
{
    if (m_isWait) {
        m_condition->WakeAll();
    }
    return m_isWait;
}
#endif // !__IMAP_H__
