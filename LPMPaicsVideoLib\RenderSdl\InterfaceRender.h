﻿#pragma once

namespace AYJ
{
	class CRenderBase
	{
	public:
		virtual ~CRenderBase() {};

		virtual int  Init(const unsigned long _ulHandle, const int _nSrcWidth, const int _nSrcHeight) = 0;
		virtual void Release() = 0;
		virtual void PutYuv420pData(unsigned char* _pYuvBuf) = 0;

		//virtual void PutDecodedFrame(AVCodecContext *s, AVFrame* _pFrame) = 0;

		virtual void ShowWndModify() = 0;

	protected:
		bool m_bIsDxMode = false;
	};
}


