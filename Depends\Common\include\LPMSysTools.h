﻿#pragma once

/*
******		FileName	:	LPMSysTools.h
******		Describe	:	此文件是c++ 接口
******		Date		:	2021-05-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位 
*/

#include "LPMCommonLib.h"
#include <vector>
#include <string>

#include <tuple>

using namespace std;

namespace LPM
{
	//	获取当前exe 运行路径
	LPM_PUBLIC_API std::wstring GetCurExePath();
	/**
	* @brief 获取系统主板的uuid
	* @return 返回系统主板的uuid
	*/
	LPM_PUBLIC_API string GetBoardUUID();

	LPM_PUBLIC_API string GetCpuNameEx();

	LPM_PUBLIC_API string GetCpuID();
	/**
	* @brief 获取显卡信息
	* @return 返回cpu的核心数目
	*/
	LPM_PUBLIC_API string GetVideoCardInfo();
	/**
	* @brief 获取主板序列号
	* @return 成功返回字符串，失败返回0
	*/
	LPM_PUBLIC_API string GetBoardSerialNumber();
	
	/**
	* @brief 读取系统空余内存容量，单位Byte
	* @param _llTotalMem 输入参数，返回内存总量
	* @param _llTFreeMem 输入参数，其放回内存空余大小
	* @return 返回系统总内存容量，单位Byte
	*/
	LPM_PUBLIC_API long long GetSysMemory(long long& _llTotalMem, long long& _llTFreeMem);

	/**
	* @brief 获取逻辑磁盘分区数量
	* @return 返回获取逻辑磁盘分区数量
	*/
	LPM_PUBLIC_API int GetDiskCount(std::vector<std::string>& _vectDriver);
	/**
	* @brief 获取磁盘某一分区总容量
	* @param _szDriver 输入参数，"C:\\"，"D:\\"
	* @param _nTotalSize 输入参数 返回磁盘总大小 单位Mb
	* @param _nFreeSize  输入参数  返回磁盘剩余大小 单位Mb
	* @return 
	*/
	LPM_PUBLIC_API bool GetDiskSpace(const char* _szDriver, int& _nTotalSize,int& _nFreeSize);
	/**
	* @brief 获取硬盘序列号
	* @return 成功返回大小，失败返回0
	*/
	LPM_PUBLIC_API int GetDiskSerialNumberEx(std::vector<string>& _vectorDiskSerNub);

	/**
	* @brief 获取可移动硬盘与u盘 信息 string 驱动, int 磁盘总大小, int 磁盘剩余空间 (单位 Mb),  string 磁盘名称
	* @return 成功返回大小，失败返回0
	*/
	LPM_PUBLIC_API int GetRemovableStorage(std::vector<std::tuple<std::string, int ,int, string>>&  _vetDriver);

	/**
	* @brief 获取文件大小
	* @return 成功返回大小(单位字节)，失败返回0
	*/
	LPM_PUBLIC_API int GetFileSize(const std::string& _strFileNameUtf8);
	LPM_PUBLIC_API int GetFileSize(const std::wstring& _wstrFileName);

	/**
	* @brief 重置Windows电脑
	* @param _nOptType 1 关机   2 重启 3 关闭程序本身
	* @param _bForce 是否强制
	* @return 成功 返回 0
	*/
	LPM_PUBLIC_API bool ResetWindows(int _nOptType, bool _bForce);
}









