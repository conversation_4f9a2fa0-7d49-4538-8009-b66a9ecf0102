/**
 * windows摄像头影像采集器
 * <AUTHOR>
 */
#ifndef CAPTURER_BASE_H
#define CAPTURER_BASE_H

#include "../Common/Typedefs.h"


#include <vector>
#include <string>

#include <windows.h>
#include <tchar.h>
#include <dshow.h>
#include "IVideoCapturerEx.h"

#include "CaptureQEdit.h"

#include "../Depends/Common/include/LPMCommonLib.h"

DEFINE_GUID(MEDIASUBTYPE_YUV420, 0x34325659, 0x0000, 0x0010, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71);
//OUR_GUID_ENTRY(MEDIASUBTYPE_YUV420,
//	0x56555949, 0x0000, 0x0010, 0x80, 0x00, 0x00, 0xaa, 0x00, 0x38, 0x9b, 0x71)

namespace AYJ 
{
	typedef void(*funcFrameCapturedCallback)(double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nVideoFormate, void* _pUserData);
	typedef void(*funcStateCapturedCallback)(eCapDevState _eState, void* _pUserData);


    class LPM_VIDEO_API CVideoCaptureBase : public ISampleGrabberCB , public LPM::CThreadEx
    {
    public:
		CVideoCaptureBase();
		~CVideoCaptureBase();

		//	_nCaptureType = 1 ， 视频， = 2 ，麦克风
		int GetCapDevCount(const int _nCaptureType = 1);
		int GetCapDevInfo(const int _nCaptureType, const int _nDevIndex, tagDevInfo& _objDevInfo);
		
		int CaptureInit(const int _nCaptureType, const int _nDevIndex, void* _pUserData, funcFrameCapturedCallback _pFrameCb, funcStateCapturedCallback _pStateCb);
		int SetCapDevFormat(const int _nCaptureType, const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut);
		int CaptureRelease();

		int CaptureStart();
		int CapturePause();
		int CaptureStop();

		eCapDevState CaptureGetState();

	protected:

		virtual bool OpenStream();		// 打开设备，开始捕获数据
		virtual bool CloseStream();		// 关闭设备，停止捕获数据
		virtual long QueryFrame(char* buf, int buf_len);	// 捕获数据。如果成功返回数据长度，失败返回0。


		int GetCapDevSupportedFormat(const int _nCaptureType, const int _nDevId ,std::vector<tagDevSupportedFormate>& _nVectParam);

		// 实现ISampleGrabberCB接口，用于捕获视频/音频
		virtual STDMETHODIMP QueryInterface(REFIID riid, void ** ppv);
		virtual ULONG STDMETHODCALLTYPE AddRef();
		virtual ULONG STDMETHODCALLTYPE Release();
		virtual HRESULT STDMETHODCALLTYPE SampleCB(double SampleTime, IMediaSample *pSample);
		virtual HRESULT STDMETHODCALLTYPE BufferCB(double SampleTime, BYTE *sampleBuf, long BufferLen);


		virtual int ThreadMainLoop(void);

	private:
		
		//	创建 图表层  第二步 
		int CreateCaptureGraph();
		//	枚举设备 创建filter
		IBaseFilter*  CreateCaptureFilter(const int _nCaptureType, const int _nDevIndex);
		int  DestroyFilter(IBaseFilter*& _pFilter);
		//	创建SampleGrabber Filte
		int CreateSampleGrabberFilter();

		IBaseFilter* BindCaptureFilter(const int _nCaptureType, const int _nDevIndex);
		bool ConnectFilters();						// 连接各fliters
		void UnConnectFilters();					// 断开filters的连接

		int GetVideoSubFormat(REFGUID guid, std::string& _strDshowFmtOut);

		// // 设置camera_filter的视频采集参数 http://bbs.csdn.net/topics/320034752
		bool SetCameraFilterOutpinMediaType(const int _nCaptureType, ICaptureGraphBuilder2 *capture_gb2, IBaseFilter* camera_filter, const tagDevSupportedFormate& _tagParam);

	private:
		static IPin* GetFirstPin(IBaseFilter* filter, PIN_DIRECTION pin_dir);

		void SendCapState(eCapDevState _eCapState);
	private:

		funcFrameCapturedCallback m_pFrameCapturedCallback = nullptr; // 数据采集回调函数
		funcStateCapturedCallback m_pStateCapturedCallback = nullptr;

		LPM::CLock m_lockCapture;

		bool  m_bConInitialized = false;

		IGraphBuilder*			m_pGraphBuilder = nullptr;		// 滤波器链表管理器
		ICaptureGraphBuilder2*	m_pCaptureGb2 = nullptr;		// 增强型捕获滤波器链表管理器
		IMediaControl*			m_pMediaCtrl= nullptr;			// 媒体控制接口
		IMediaEventEx*			m_pMediaEventEx = nullptr;
		

		IBaseFilter*			m_pCaptureFilter = nullptr;		// 捕获滤波器

		IBaseFilter*			m_pSampleGrabberFilter = nullptr;
		ISampleGrabber*			m_pSampleGrabber = nullptr;

		IPin* m_pCaptureInPin = nullptr;
		IPin* m_pCaptureOutPin = nullptr;
		

		bool m_bFilterConnected = false;
		bool m_bIsCaptureing = false;
		tagDevSupportedFormate m_tagCapFormat;

		std::wstring m_wstrDeviceName;
		eCapDevState m_eCapState = CapStateNotInit;
		void* m_pUserData = nullptr;

    };
} // namespace LPM

#endif // !IVIDOE_CAPTURER_H

