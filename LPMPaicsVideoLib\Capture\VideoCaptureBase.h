/**
 * Windows DirectShow视频采集器基础类
 * <AUTHOR>
 * @description 基于DirectShow技术的视频采集底层实现
 *              支持USB摄像头、采集卡等视频设备的枚举、配置和数据采集
 *              具备完善的错误处理、内存管理和性能优化
 */
#ifndef CAPTURER_BASE_H
#define CAPTURER_BASE_H

#include "../Common/Typedefs.h"
#include <vector>
#include <string>
#include <windows.h>
#include <tchar.h>
#include <dshow.h>
#include "IVideoCapturerEx.h"
#include "CaptureQEdit.h"
#include "../Depends/Common/include/LPMCommonLib.h"

// 定义YUV420格式GUID
DEFINE_GUID(MEDIASUBTYPE_YUV420, 0x34325659, 0x0000, 0x0010, 0x80, 0x00, 0x00, 0xAA, 0x00, 0x38, 0x9B, 0x71);

namespace AYJ
{
	// 帧数据回调函数类型定义
	typedef void(*funcFrameCapturedCallback)(double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nVideoFormate, void* _pUserData);
	// 状态变化回调函数类型定义
	typedef void(*funcStateCapturedCallback)(eCapDevState _eState, void* _pUserData);

    /**
     * @class CVideoCaptureBase
     * @brief DirectShow视频采集基础类
     *
     * 功能特性：
     * 1. 支持多种视频设备（USB摄像头、采集卡等）
     * 2. 设备枚举和信息获取
     * 3. 多种像素格式支持和自动转换
     * 4. 线程安全的采集控制
     * 5. 完善的暂停/恢复机制
     * 6. 内存泄漏防护和资源管理
     */
    class LPM_VIDEO_API CVideoCaptureBase : public ISampleGrabberCB , public LPM::CThreadEx
    {
    public:
		CVideoCaptureBase();
		~CVideoCaptureBase();

		// GetCapDevCount 获取采集设备数量
		int GetCapDevCount(const int _nCaptureType = 1);  // _nCaptureType: 1=视频, 2=音频

		// GetCapDevInfo 获取采集设备信息
		int GetCapDevInfo(const int _nCaptureType, const int _nDevIndex, tagDevInfo& _objDevInfo);

		// CaptureInit 初始化采集设备
		int CaptureInit(const int _nCaptureType, const int _nDevIndex, void* _pUserData, funcFrameCapturedCallback _pFrameCb, funcStateCapturedCallback _pStateCb);

		// SetCapDevFormat 设置采集设备格式参数
		int SetCapDevFormat(const int _nCaptureType, const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut);

		// CaptureRelease 释放采集设备资源
		int CaptureRelease();

		// CaptureStart 开始视频采集
		int CaptureStart();

		// CapturePause 暂停视频采集
		int CapturePause();

		// CaptureStop 停止视频采集
		int CaptureStop();

		// CaptureGetState 获取当前采集状态
		eCapDevState CaptureGetState();

	protected:
		// OpenStream 打开DirectShow流，开始数据捕获
		virtual bool OpenStream();

		// CloseStream 关闭DirectShow流，停止数据捕获
		virtual bool CloseStream();

		// QueryFrame 查询帧数据（预留接口）
		virtual long QueryFrame(char* buf, int buf_len);

		// GetCapDevSupportedFormat 获取设备支持的格式列表
		int GetCapDevSupportedFormat(const int _nCaptureType, const int _nDevId, std::vector<tagDevSupportedFormate>& _nVectParam);

		// ========== ISampleGrabberCB接口实现 ==========
		virtual STDMETHODIMP QueryInterface(REFIID riid, void ** ppv);
		virtual ULONG STDMETHODCALLTYPE AddRef();
		virtual ULONG STDMETHODCALLTYPE Release();
		virtual HRESULT STDMETHODCALLTYPE SampleCB(double SampleTime, IMediaSample *pSample);
		virtual HRESULT STDMETHODCALLTYPE BufferCB(double SampleTime, BYTE *sampleBuf, long BufferLen);

		// ThreadMainLoop 线程主循环（预留）
		virtual int ThreadMainLoop(void);

	private:
		// ========== DirectShow图形构建相关 ==========

		// CreateCaptureGraph 创建DirectShow捕获图形
		int CreateCaptureGraph();

		// CreateCaptureFilter 创建并枚举采集设备Filter
		IBaseFilter* CreateCaptureFilter(const int _nCaptureType, const int _nDevIndex);

		// DestroyFilter 销毁Filter并释放资源
		int DestroyFilter(IBaseFilter*& _pFilter);

		// CreateSampleGrabberFilter 创建SampleGrabber Filter
		int CreateSampleGrabberFilter();

		// BindCaptureFilter 绑定采集Filter到图形
		IBaseFilter* BindCaptureFilter(const int _nCaptureType, const int _nDevIndex);

		// ConnectFilters 连接各个Filter
		bool ConnectFilters();

		// UnConnectFilters 断开Filter连接
		void UnConnectFilters();

		// GetVideoSubFormat 获取视频子格式信息
		int GetVideoSubFormat(REFGUID guid, std::string& _strDshowFmtOut);

		// SetCameraFilterOutpinMediaType 设置摄像头Filter输出格式
		bool SetCameraFilterOutpinMediaType(const int _nCaptureType, ICaptureGraphBuilder2 *capture_gb2, IBaseFilter* camera_filter, const tagDevSupportedFormate& _tagParam);

		// ========== 工具函数 ==========

		// GetFirstPin 获取Filter的第一个指定方向的Pin
		static IPin* GetFirstPin(IBaseFilter* filter, PIN_DIRECTION pin_dir);

		// SendCapState 发送采集状态变化通知
		void SendCapState(eCapDevState _eCapState);
	private:

		// ========== 回调函数指针 ==========
		funcFrameCapturedCallback m_pFrameCapturedCallback = nullptr;  // 帧数据回调函数
		funcStateCapturedCallback m_pStateCapturedCallback = nullptr;  // 状态变化回调函数

		// ========== 线程同步 ==========
		LPM::CLock m_lockCapture;  // 采集操作互斥锁

		// ========== DirectShow核心组件 ==========
		bool m_bConInitialized = false;                // COM初始化标志

		IGraphBuilder*			m_pGraphBuilder = nullptr;      // 滤波器图形管理器
		ICaptureGraphBuilder2*	m_pCaptureGb2 = nullptr;        // 增强型捕获图形构建器
		IMediaControl*			m_pMediaCtrl = nullptr;         // 媒体控制接口
		IMediaEventEx*			m_pMediaEventEx = nullptr;      // 媒体事件接口

		IBaseFilter*			m_pCaptureFilter = nullptr;     // 采集设备Filter
		IBaseFilter*			m_pSampleGrabberFilter = nullptr; // 数据抓取Filter
		ISampleGrabber*			m_pSampleGrabber = nullptr;     // 数据抓取接口

		IPin* m_pCaptureInPin = nullptr;   // 采集Filter输入Pin
		IPin* m_pCaptureOutPin = nullptr;  // 采集Filter输出Pin

		// ========== 状态管理 ==========
		bool m_bFilterConnected = false;              // Filter连接状态
		bool m_bIsCaptureing = false;                 // 采集进行状态
		tagDevSupportedFormate m_tagCapFormat;        // 当前采集格式
		std::wstring m_wstrDeviceName;                // 设备名称
		eCapDevState m_eCapState = CapStateNotInit;   // 当前采集状态
		void* m_pUserData = nullptr;                  // 用户数据指针

    };
} // namespace LPM

#endif // !IVIDOE_CAPTURER_H

