﻿/**
 * windows摄像头影像采集器
 * <AUTHOR>
 */
#ifndef I__VIDEO_CAPTURER_H
#define I__VIDEO_CAPTURER_H

#include "../Common/Typedefs.h"
#include <vector>

//#include "IVideoCapturer.h"

namespace AYJ 
{
	//	采集设备 支持的参数
	typedef struct tagDevSupportedFormate
	{
		AyjImageFormat nFormate = eImageNone;	//	格式
		std::string strFormate = "";			//	格式

		//	视频
		int nVideoW = 0;					//	视频长度
		int nVideoH = 0;					//	视频高度
		int nFps = 0;						//	帧率
		
		//	音频
		int nChannels = 0;			 // 通道数量
		int nSamplesPerSec = 0;      // 采样率
		int nBlockAlign = 0;		 // 块对齐方式,布局  通常为 (nChannels*wBitsPerSample)/8
		int wBitsPerSample = 0;      //	位深度 多为8或16
		int nAvgBytesPerSec = 0;	 //	比特率

		tagDevSupportedFormate & tagDevSupportedFormate::operator=(const tagDevSupportedFormate & other)
		{
			if (this == &other) {
				return *this;
			}

			nFormate = other.nFormate;
			strFormate = other.strFormate;

			nVideoW = other.nVideoW;
			nVideoH = other.nVideoH;
			nFps = other.nFps;

			nChannels = other.nChannels;
			nSamplesPerSec = other.nSamplesPerSec;
			nBlockAlign = other.nBlockAlign;
			wBitsPerSample = other.wBitsPerSample;
			nAvgBytesPerSec = other.nAvgBytesPerSec;

			return *this;
		}

	}tagevSupportedFormate;

	

	//	采集卡状态
	typedef enum  eCapDevState
	{
		CapStateNotInit = -1,	//	采集设备未初始化
		CapStateRunning = 0,	//	采集中...
		CapStatePaused = 1,		//	采集暂停
		CapStateStopped = 2,	//	采集停止

		CapStateDevBreak = 3,		//	采集设备掉线(检查线路是否出错)
		CapStateDevInsertReset = 4, //	采集设备重新插入了

	}eCapDevState;

	//	采集卡设备信息
	typedef struct tagDevInfo
	{
		int nDevIndex = -1;		//	采集卡序号ID
		std::string strDevName;	//	采集卡名称
		std::string strDevId;	//	采集卡设备唯一标识

		//	采集卡所支持的参数列表
		std::vector<tagevSupportedFormate> vectDevSupportedFormate;

	}tagDevInfo;

    /**
     * 摄像头影像采集回调基类
     */
    class LPM_VIDEO_API IVideoCaptureCallbackEx
    {
    public:
        virtual ~IVideoCaptureCallbackEx(void) {}
        /**
         * 摄像头影像采集回调接口
         * @param capId 回调采集实例的编号
         * @param frame 一帧图像数据
         */
        virtual int OnFrameCallbackEx(uint32_t _unCapId, double _dSampleTime,  unsigned char* _pFrame, long _lFrameSize, int _nW, int _nH, int _nVideoFormate) = 0;
        /**
         * 摄像头状态变化通知回调接口
         * @param capId 回调采集实例的编号
         * @param status 当前采集实例的状态
         */
        virtual int OnCapturerStatusEx(uint32_t _unCapId, eCapDevState _eCapStatus) = 0;
    };
    /**
     * 摄像头影像采集的基类
     */
    class LPM_VIDEO_API IVideoCapturerEx
    {
	public:
		virtual ~IVideoCapturerEx(void) {}
		
		//	获取视频设备数量
		virtual int CaptureGetDevCount() = 0;
		//	获取视频设备信息
		virtual int CaptureGetDevInfo(const int _nDevIndex, tagDevInfo& _objDevInfo) = 0;

		//	初始化采集设备
		virtual int CaptureInit(const int _nDevIndex, IVideoCaptureCallbackEx* _pCapCallBack) = 0;
		//	释放采集设备
		virtual int CaptureRelease() = 0;

		//	设置设备的参数
		virtual int CaptureDevFormatSet(const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut) = 0;
		//	采集开始
		virtual int CaptureStart() = 0;
		//	采集暂停
		virtual int CapturePause() = 0;
		//	采集停止
		virtual int CaptureStop() = 0;

		//	获取采集状态
		virtual eCapDevState CaptureGetState() = 0;

		//	创建	采集对象
		static IVideoCapturerEx* Create();
		//	释放	采集对象
		static void Release(IVideoCapturerEx*& _pObj);
    };
} // namespace LPM

#endif // !IVIDOE_CAPTURER_H

