﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Interface">
      <UniqueIdentifier>{6fad7a91-ead5-4d1d-b32b-7a7dc0b09182}</UniqueIdentifier>
    </Filter>
    <Filter Include="Capture">
      <UniqueIdentifier>{4b9f7310-2493-4715-b079-98c75a02c611}</UniqueIdentifier>
    </Filter>
    <Filter Include="RenderSdl">
      <UniqueIdentifier>{390289f8-7e7a-46de-a4be-3431553ef7eb}</UniqueIdentifier>
    </Filter>
    <Filter Include="ImageScale">
      <UniqueIdentifier>{8c423c45-947c-400b-b388-5cb84f9b79aa}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{c6cbd048-b7db-494c-9e3a-6637ed2d87c4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Recorder">
      <UniqueIdentifier>{4ff74de7-f86d-4ddb-90f8-3ad830d02877}</UniqueIdentifier>
    </Filter>
    <Filter Include="Encoder">
      <UniqueIdentifier>{bc3ef3bf-1e26-4ed1-8d3b-bec7e4880f05}</UniqueIdentifier>
    </Filter>
    <Filter Include="Decoder">
      <UniqueIdentifier>{ca3cad79-afde-4fae-9c31-cb82e04311ee}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="LPMPaicsVideoLib.h">
      <Filter>Interface</Filter>
    </ClInclude>
    <ClInclude Include="VideoInterface.h">
      <Filter>Interface</Filter>
    </ClInclude>
    <ClInclude Include="RenderSdl\RenderSdl.h">
      <Filter>RenderSdl</Filter>
    </ClInclude>
    <ClInclude Include="ImageScale\ImageScale.h">
      <Filter>ImageScale</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>资源文件</Filter>
    </ClInclude>
    <ClInclude Include="RenderSdl\RenderDx.h">
      <Filter>RenderSdl</Filter>
    </ClInclude>
    <ClInclude Include="RenderSdl\InterfaceRender.h">
      <Filter>RenderSdl</Filter>
    </ClInclude>
    <ClInclude Include="Common\Typedefs.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Encoder\AYJVideoEncoder.h">
      <Filter>Encoder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\AYJVideoDecoder.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\AYJVideoReader.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\FrameSeekerEx.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\IAYJVideoPlayer.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="recorder\IAYJVideoWriter.h">
      <Filter>Recorder</Filter>
    </ClInclude>
    <ClInclude Include="recorder\AYJVideoWrite.h">
      <Filter>Recorder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\AYJImageDecoder.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="recorder\AYJMp4Write.h">
      <Filter>Recorder</Filter>
    </ClInclude>
    <ClInclude Include="recorder\IAYJMp4Writer.h">
      <Filter>Recorder</Filter>
    </ClInclude>
    <ClInclude Include="Capture\IVideoCapturerEx.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Capture\VideoCapturerEx.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Capture\VideoCaptureBase.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\IAYJVideoDecoder.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="Capture\AudioCapturer.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Capture\IAudioCapturer.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Capture\CaptureQEdit.h">
      <Filter>Capture</Filter>
    </ClInclude>
    <ClInclude Include="Encoder\AYJAudioEncoder.h">
      <Filter>Encoder</Filter>
    </ClInclude>
    <ClInclude Include="Encoder\IAYJVideoEncoder.h">
      <Filter>Encoder</Filter>
    </ClInclude>
    <ClInclude Include="Decoder\AYJCommVideoPlayer.h">
      <Filter>Decoder</Filter>
    </ClInclude>
    <ClInclude Include="common\AyjVideoCommDefine.h">
      <Filter>Common</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="dllmain.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="RenderSdl\RenderSdl.cpp">
      <Filter>RenderSdl</Filter>
    </ClCompile>
    <ClCompile Include="RenderSdlInterface.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
    <ClCompile Include="ImageScale\ImageScale.cpp">
      <Filter>ImageScale</Filter>
    </ClCompile>
    <ClCompile Include="RenderSdl\RenderDx.cpp">
      <Filter>RenderSdl</Filter>
    </ClCompile>
    <ClCompile Include="Common\Typedefs.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Encoder\AYJVideoEncoder.cpp">
      <Filter>Encoder</Filter>
    </ClCompile>
    <ClCompile Include="Decoder\AYJVideoDecoder.cpp">
      <Filter>Decoder</Filter>
    </ClCompile>
    <ClCompile Include="Decoder\AYJVideoReader.cpp">
      <Filter>Decoder</Filter>
    </ClCompile>
    <ClCompile Include="Decoder\FrameSeekerEx.cpp">
      <Filter>Decoder</Filter>
    </ClCompile>
    <ClCompile Include="recorder\AYJVideoWrite.cpp">
      <Filter>Recorder</Filter>
    </ClCompile>
    <ClCompile Include="Decoder\AYJImageDecoder.cpp">
      <Filter>Decoder</Filter>
    </ClCompile>
    <ClCompile Include="recorder\AYJMp4Write.cpp">
      <Filter>Recorder</Filter>
    </ClCompile>
    <ClCompile Include="Capture\VideoCapturerEx.cpp">
      <Filter>Capture</Filter>
    </ClCompile>
    <ClCompile Include="Capture\VideoCaptureBase.cpp">
      <Filter>Capture</Filter>
    </ClCompile>
    <ClCompile Include="Capture\AudioCapturer.cpp">
      <Filter>Capture</Filter>
    </ClCompile>
    <ClCompile Include="Encoder\AYJAudioEncoder.cpp">
      <Filter>Encoder</Filter>
    </ClCompile>
    <ClCompile Include="Encoder\IAYJVideoEncoder.cpp">
      <Filter>Encoder</Filter>
    </ClCompile>
    <ClCompile Include="Decoder\AYJCommVideoPlayer.cpp">
      <Filter>Decoder</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="LPMPaicsVideoLib.rc">
      <Filter>资源文件</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="res\NoSignal.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\bitmap1.bmp">
      <Filter>资源文件</Filter>
    </Image>
    <Image Include="res\NoSignal2.bmp">
      <Filter>资源文件</Filter>
    </Image>
  </ItemGroup>
</Project>