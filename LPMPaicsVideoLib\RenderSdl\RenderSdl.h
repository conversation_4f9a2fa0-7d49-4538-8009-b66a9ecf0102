﻿#pragma once

#include "InterfaceRender.h"

extern "C"
{
#define	 SDL_MAIN_HANDLED
#include "../../Depends/SDL2/include/SDL.h"
//#include "./SDL2-2.0.8/include/SDL_ttf.h"
}

#include "../../Depends/Common/include/LPMCommonLib.h"

struct AVCodecContext;
struct AVFrame;

using namespace LPM;

namespace AYJ
{
	class CRenderSdl : public CRenderBase
	{
	public:
		CRenderSdl(bool  _bMode);
		~CRenderSdl();

	public:

		virtual int  Init(unsigned long _ulHandle, const int _nSrcWidth, const int _nSrcHeight);
		virtual void Release();

		virtual void PutYuv420pData(unsigned char* _pYuvBuf);

		virtual void ShowWndModify();

		void PutDecodedFrame(AVCodecContext *s, AVFrame* _pFrame);

	private:
		void Play(unsigned char *pYuvBuf);
		void FillTexture();

	private:
		//	窗口句柄
		unsigned long    m_ulHandleWnd;
		//	显示窗口
		SDL_Window		*m_pSdlWindow;
		//	纹理
		SDL_Texture		*m_pSdlTexture;
		//	渲染
		SDL_Renderer	*m_pSdlRenderer;

		//	显示窗口高度
		int	m_nHeight;
		//	显示窗口宽度
		int m_nWidth;
	private:
		SDL_Rect		m_Rectsrc;
		SDL_Rect		m_RectDes;

		CLock	m_lock;

	private:
		bool m_bExit;

	};
}


