﻿/*
******		FileName	:	IAYJVideoEncoder.h
******		Describe	:	此文件是c++ 接口,主要负责视频的编码接口
******		Date		:	2021-12-17
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
*/

#ifndef __AYJ_VIDEO_ENCODER_H__
#define __AYJ_VIDEO_ENCODER_H__

#include "Common/Typedefs.h"

namespace AYJ 
{
	class LPM_VIDEO_API IOVideoEncoderNotify
	{
	public:
		IOVideoEncoderNotify(void) {};
		virtual~IOVideoEncoderNotify(void) {};

	public:
	
		virtual void OnEncoderDataNotify(unsigned char* _ucEncodeData, int _nDataSize, int _nW, int _nH, long long _llWritePts, long long _llFramePts, long long _llInPts, bool _bKeyFrame) = 0;
	};

	class LPM_VIDEO_API IAyjVideoEncoder
    {
    public:
		IAyjVideoEncoder() {}
		IAyjVideoEncoder(IOVideoEncoderNotify* _pEncodeNotify) {}
		virtual~IAyjVideoEncoder(void) {}

		virtual int EncoderInit(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, long long _llPtsStart, int _nIndexStart, bool bFlagH264 = true) = 0;

		virtual int Encoder(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) = 0;

		virtual int EncoderClose(void) = 0;

		virtual int EncoderRefresh() = 0;

		virtual long long EncoderGetWritePts() = 0;

		virtual int EncoderGetFps() = 0;

		virtual int EncoderMoidfyFps(int _nFps) = 0;

		//	创建 与 释放
		static IAyjVideoEncoder* Create(IOVideoEncoderNotify* _pEncodeNotify);
		static void Release(IAyjVideoEncoder*& _pObj);
           
    };
} // namespace LPM
#endif // !__RECORDER_FLV_H__
