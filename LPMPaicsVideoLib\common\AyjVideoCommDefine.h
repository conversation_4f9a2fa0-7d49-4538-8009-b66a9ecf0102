﻿/*
 * Filename:  AyjVideoCommDefine.h
 * Project :  LPMPaicsVideoLib
 * Created by TangJingFei.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/
#ifndef AyjVide_Comm_Define_H
#define AyjVide_Comm_Define_H

#include <memory.h>            //define NULL.

#ifdef LPMPAICSVIDEOLIB_EXPORTS
#define LPM_VIDEO_API  __declspec(dllexport)
#else
#define LPM_VIDEO_API  __declspec(dllimport)
#endif

namespace AYJ
{
    /**
     * 采集图像格式
     * I420: YYYYYYYY UU VV    =>YUV420P
     * YV12: YYYYYYYY VV UU    =>YUV420P
     * NV12: YYYYYYYY UVUV     =>YUV420SP
     * NV21: YYYYYYYY VUVU     =>YUV420SP
     * YUYV/YUY2: YU YV YU YV  =>YUV422
     */
    typedef enum AyjImageFormat	//	数值能直接对应ffmpeg AVPixelFormat
    {
        eImageNone = -1,
		eImageYuv420p	= 0,
		eImageYUYV422	= 1,
		eImageRGB24		= 2,
		eImageBGR24		= 3,
				
		eImageYUV411P = 7,	//	AV_PIX_FMT_YUV411P 
	
		eImageMJPG = 12,	//	AV_PIX_FMT_YUVJ420P
		eImageUYVY422 = 15,	//	AV_PIX_FMT_UYVY422 15
		eImageNV12 =  23,	//	AV_PIX_FMT_NV12		23
		eImageARGB32 = 25,
		eImageRGBA32 = 26,
		eImageABGR32 = 27,
		eImageBGRA32 = 28,
        
		eImageRGB565LE = 37,
		eImageRGB555LE = 39,
		
		eImageUYVYA44P = 81,
		
		
		eImageYVYU422 = 110,	//	AV_PIX_FMT_YVYU422	110
		eImageP010LE  = 161,	//	AV_PIX_FMT_P010LE	161
		//AV_PIX_FMT_P210LE
		eAudioPcm = 1001,
    }AyjImageFormat;

#pragma  pack(1)
	//	视频输出数据
	typedef struct tagVdieoFrame
	{
		long long		llSeq = 0;			//	视频序号, 以开始采集为第一帧序号
		long long		llPts = 0;			//	视频显示时间戳
		long long		llPtsLength = 0;	//	视频显示时间戳, 视频长度

		unsigned int	unWidth = 0;		//	视频宽度
		unsigned int	unHeight = 0;		//	视频长度
		AyjImageFormat	eFrameFormat = eImageNone;	//	图像格式 2== RGB24  3==BGR24

		unsigned char*	pVideoData = nullptr;	//	视频数据，BGR24  格式 == 3 , 给客户端后，会转成RGB24  == 2
		unsigned int	unDataSize = 0;			//	视频数据大小	
		unsigned int	unDataBufferSize = 0;	//	视频缓存大小

		int				nAiTime = 0;		//	AI 耗时 毫秒

		tagVdieoFrame()
		{
		}

		tagVdieoFrame(AyjImageFormat _eFrameFormat, int _nW, int _nH)
		{
			unWidth = _nW;
			unHeight = _nH;
			llPtsLength = 0;
			
			unDataBufferSize = 0;
			switch (_eFrameFormat)
			{
			case eImageYuv420p:
			case eImageNV12:
				unDataBufferSize = unWidth * unHeight * 3 >> 1;
				break;
			case eImageYUYV422:
			case eImageUYVY422:
			case eImageRGB565LE:
			case eImageRGB555LE:
				unDataBufferSize = unWidth * unHeight * 2;
				break;
			case eImageRGB24:
			case eImageBGR24:
				unDataBufferSize = unWidth * unHeight * 3;
				break;
			case eImageARGB32:
			case eImageRGBA32:
			case eImageABGR32:
			case eImageBGRA32:
				unDataBufferSize = unWidth * unHeight * 4;
				break;
			case eImageMJPG:
				unDataBufferSize = 1024 * 512; // jpg默认的大小
				break;
			}
			if (unDataBufferSize == 0) {
				return;
			}
			pVideoData = new unsigned char[unDataBufferSize];
			memset(pVideoData, 0, unDataBufferSize);
			
			eFrameFormat = _eFrameFormat;
			unDataSize = unDataBufferSize;
		}

		tagVdieoFrame& operator=(const tagVdieoFrame& other)
		{
			if (this == &other)
				return *this;

			if (unDataSize != other.unDataSize)
			{
				delete[] pVideoData;
				pVideoData = new unsigned char[other.unDataSize];
			}

			memcpy(pVideoData, other.pVideoData, other.unDataSize);

			llSeq = other.llSeq;
			llPts = other.llPts;
			llPtsLength = other.llPtsLength;
			unWidth = other.unWidth;
			unHeight = other.unHeight;
			eFrameFormat = other.eFrameFormat;
			unDataSize = other.unDataSize;
			unDataBufferSize = other.unDataBufferSize;
			nAiTime = other.nAiTime;

			return *this;
		}

		tagVdieoFrame(const tagVdieoFrame & other)
		{
			if (unDataSize != other.unDataSize)
			{
				delete[] pVideoData;
				pVideoData = new unsigned char[other.unDataSize];
			}

			memcpy(pVideoData, other.pVideoData, other.unDataSize);

			llSeq = other.llSeq;
			llPts = other.llPts;
			llPtsLength = other.llPtsLength;
			unWidth = other.unWidth;
			unHeight = other.unHeight;
			eFrameFormat = other.eFrameFormat;
			unDataSize = other.unDataSize;
			unDataBufferSize = other.unDataSize;
			nAiTime = other.nAiTime;
		}
		~tagVdieoFrame()
		{
			if (pVideoData != nullptr)
			{
				delete[] pVideoData;
				pVideoData = nullptr;
				unDataSize = 0;
				unDataBufferSize = 0;
			}
		}

	}tagVdieoFrame;
#pragma  pack()


} // namespace AYJ


#endif // AyjVide_Comm_Define_H
