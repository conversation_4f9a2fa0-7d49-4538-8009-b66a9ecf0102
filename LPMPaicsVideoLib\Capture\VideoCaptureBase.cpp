#include "VideoCaptureBase.h"


//#include "directshow-lib/qedit.h"
#include <algorithm>
#pragma comment(lib, "strmiids.lib")
#pragma comment(lib, "winmm.lib")
#pragma comment(lib, "uuid.lib")
#pragma comment(lib, "strmbase.lib")

#include <atomic>
#include <dvdmedia.h>
#include <map>
#include<strsafe.h>

#include "VideoCaptureBase.h"

using namespace  LPM;
namespace AYJ
{
	static atomic<bool> g_bConInit = false;
	static atomic<int>  g_nObjCount = 0;
	//	��ʼ��con ���  ��һ�� 
	static int CoInit();
	//	�ͷ� con ���
	static int CoRelease();

	LONGLONG GetMaxOfFrameArray(LONGLONG *maxFps, long size)
	{
		LONGLONG maxFPS = maxFps[0];
		for (int i = 0; i < size; i++) {
			if (maxFPS > maxFps[i])
				maxFPS = maxFps[i];
		}
		return maxFPS;
	}

	int CoInit()
	{
		if (g_bConInit)
		{
			g_nObjCount++;
			return 0;
		}
		
		//	��ʼ�� con 
		HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
		if (FAILED(hr))
		{
			if (hr == RPC_E_CHANGED_MODE)
			{
				WriteLogEx(ModuleVideo, LogError, "CoInitializeEx(NULL, COINIT_APARTMENTTHREADED) RPC_E_CHANGED_MODE, error 0x%x", hr);
			}
			g_bConInit = false;

			WriteLogEx(ModuleVideo, LogError, "CoInitializeEx(NULL, COINIT_APARTMENTTHREADED) => error 0x%x", hr);
			CoUninitialize();
		}
		else
		{
			g_nObjCount++;
			g_bConInit = true;
		}
		

		return 0;
	}

	int CoRelease()
	{
		if (!g_bConInit)
			return -1;

		g_nObjCount--;
		if (g_bConInit && g_nObjCount <= 0)
		{
			CoUninitialize();
			g_bConInit = false;
		}

		return 0;
	}

	// ���ڽ�������ıȽϺ���
	static bool SizeCompareGreater(const tagDevSupportedFormate& size1, const tagDevSupportedFormate& size2)
	{
		if(size1.nVideoW > 0 )
			return size1.nVideoW > size2.nVideoW;
		

		return size1.nSamplesPerSec < size2.nSamplesPerSec;
	}

	CVideoCaptureBase::CVideoCaptureBase()
	{
		CoInit();

		CreateCaptureGraph();

	}

	CVideoCaptureBase::~CVideoCaptureBase()
	{
		CaptureRelease();

		CoRelease();

		ThreadStop();
	}
	
	int  CVideoCaptureBase::GetVideoSubFormat(REFGUID guid, std::string& _strDshowFmtOut)
	{
		AyjImageFormat fmt = eImageNone;

		if (guid == MEDIASUBTYPE_MPEG2_VIDEO) {
			_strDshowFmtOut = "MEDIASUBTYPE_MPEG2_VIDEO";
			fmt = eImageYuv420p;	//	AV_PIX_FMT_YUV420P 0
		}
		else if (guid == MEDIASUBTYPE_YV12) {	//	�����⣬ ��ɫ���ԣ� �����
			_strDshowFmtOut = "MEDIASUBTYPE_YV12";
			fmt = eImageYuv420p;	//	AV_PIX_FMT_YUV420P 0
		}
		else if (guid == MEDIASUBTYPE_H264) {
			_strDshowFmtOut = "MEDIASUBTYPE_H264";
			fmt = eImageYuv420p;	//	AV_PIX_FMT_YUV420P	0
		}
		else if (guid == MEDIASUBTYPE_YUYV) {
			_strDshowFmtOut = "MEDIASUBTYPE_YUYV";
			fmt = eImageYUYV422;	//	AV_PIX_FMT_YUYV422	1
		}
		else if (guid == MEDIASUBTYPE_YUY2) {
			_strDshowFmtOut = "MEDIASUBTYPE_YUY2";
			fmt = eImageYUYV422;		//	AV_PIX_FMT_YUYV422	1
		}
		else if (guid == MEDIASUBTYPE_RGB24) {
			_strDshowFmtOut = "MEDIASUBTYPE_RGB24";
			fmt = eImageBGR24;		//	ע��: FFmpeg uses BGR24 for 24-bit RGB 3
		}
		else if (guid == MEDIASUBTYPE_RGB555) {
			_strDshowFmtOut = "MEDIASUBTYPE_RGB555";
			fmt = eImageRGB555LE;	//	AV_PIX_FMT_RGB555LE 39
		}
		else if (guid == MEDIASUBTYPE_RGB565) {
			_strDshowFmtOut =  "MEDIASUBTYPE_RGB565" ;	
			fmt = eImageRGB565LE;	//	AV_PIX_FMT_RGB565LE 37
		}
		else if (guid == MEDIASUBTYPE_P010) {
			_strDshowFmtOut = "MEDIASUBTYPE_P010";
			fmt = eImageP010LE;	//	AV_PIX_FMT_P010LE 161
		}
		else if (guid == MEDIASUBTYPE_P210) {
			_strDshowFmtOut = "P210";
			fmt = eImageP010LE;	//	AV_PIX_FMT_P210LE 161
		}
		else if (guid == MEDIASUBTYPE_UYVY) {	//	�����ok��
			_strDshowFmtOut = "MEDIASUBTYPE_UYVY";
			fmt = eImageUYVY422;	//	AV_PIX_FMT_UYVY422 15
		}
		else if (guid == MEDIASUBTYPE_AYUV) {
			_strDshowFmtOut = "MEDIASUBTYPE_UYVY";
			fmt = eImageUYVYA44P;	//	AV_PIX_FMT_YUVA444P 81
		}
		else if (guid == MEDIASUBTYPE_Y211) {
			_strDshowFmtOut = "MEDIASUBTYPE_Y211";
			fmt = eImageYUV411P;	//	AV_PIX_FMT_YUV411P 7
		}
		else if (guid == MEDIASUBTYPE_Y41P) {
			_strDshowFmtOut = "MEDIASUBTYPE_Y41P";
			fmt = eImageYUV411P;	//	AV_PIX_FMT_YUV411P	7
		}
		else if (guid == MEDIASUBTYPE_MJPG) {
			_strDshowFmtOut = "MEDIASUBTYPE_MJPG";
			fmt = eImageMJPG;	//	AV_PIX_FMT_YUVJ420P	12
		}
		else if (guid == MEDIASUBTYPE_NV12) {
			_strDshowFmtOut = "MEDIASUBTYPE_NV12";
			fmt = eImageNV12;		//	AV_PIX_FMT_NV12	23
		}
		else if (guid == MEDIASUBTYPE_YVYU) {
			_strDshowFmtOut = "MEDIASUBTYPE_YVYU";
			fmt = eImageYVYU422;	//	AV_PIX_FMT_YVYU422	110
		}
		else if (guid == MEDIASUBTYPE_RGB32) {
			_strDshowFmtOut = "MEDIASUBTYPE_RGB32";
			fmt = eImageBGRA32;	//	AV_PIX_FMT_RGB32
		}
		
		/*else if (guid == MEDIASUBTYPE_dvsl
			|| guid == MEDIASUBTYPE_dvsd
			|| guid == MEDIASUBTYPE_dvhd) {// If this is an external DV camera
			_strDshowFmtOut = "MEDIASUBTYPE_YUY2";
			fmt = eImageYUY422;// MS DV filter seems to create this type
		}*/
		else {
			WCHAR strGuid[39];
			StringFromGUID2(guid, strGuid, 39);
			std::wstring wstrUuid(strGuid);
			WriteLogEx(ModuleVideo, LogDebug, "Device support unknown media type %ls, guid:%s", strGuid, LPM::W2U(wstrUuid).c_str());
			if (wstrUuid == L"{*************-0010-8000-00AA00389B71}" || wstrUuid == L"{*************-0010-8000-00AA00389B71}")
			{
				_strDshowFmtOut = "NULL";
				fmt = eImageNone;	//	AV_PIX_FMT_YUV420P 0
			}
			else
			{
				WriteLogEx(ModuleVideo, LogError, "Device support unknown media type %ls, guid:%s", strGuid, guid);
				_strDshowFmtOut = "NULL";
				return eImageNone;
			}
		}
		return fmt;
	}

	//	_nCapType = 1 �� ��Ƶ�� = 2 ����˷�
	int CVideoCaptureBase::GetCapDevCount(const int _nCapType)
	{
		CAutoLock lockCapture(&m_lockCapture);

		//ö����Ƶ��׽�豸
		ICreateDevEnum *pCreateDevEnum; // �˽ӿ�����һ�����������豸ö������The System Device Enumerator exposes this interface
		HRESULT hr = CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC_SERVER, IID_ICreateDevEnum, (void**)&pCreateDevEnum);
		if (hr != NOERROR)
		{
			WriteLogEx(ModuleVideo, LogError, "Failed to create CLSID_SystemDeviceEnum, error 0x%x", hr);
			return 0;
		}

		IEnumMoniker* pDSMonikerDevEnum = 0; //�˽ӿ�����ö��һ��"����"�������ö�����ֱ��е�����
		if(1 == _nCapType) //	��Ƶ
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_VideoInputDeviceCategory, &pDSMonikerDevEnum, 0);
		else if (2 == _nCapType)//	��˷�
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_AudioInputDeviceCategory, &pDSMonikerDevEnum, 0);
		else
		{	
			//	������
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_AudioRendererCategory, &pDSMonikerDevEnum, 0);
		}
		if (hr != NOERROR)
		{
			WriteLogEx(ModuleVideo, LogError, "Failed to enumerate CLSID_SystemDeviceEnum, error 0x%x.", hr);
			return 0;
		}

		int nCount = 0;
		ULONG fetched;
		IMoniker* moniker;
		pDSMonikerDevEnum->Reset();
		while ((hr = pDSMonikerDevEnum->Next(1, &moniker, &fetched)) == S_OK)
		{
			nCount++;
			moniker->Release();
		}

		SAFE_RELEASE(pDSMonikerDevEnum);
		SAFE_RELEASE(pCreateDevEnum);

		WriteLogEx(ModuleVideo, LogInfo, "GetCapDevCount : %d", nCount);

		return nCount;
	}

	int CVideoCaptureBase::GetCapDevInfo(const int _nCapType , const int _nDevIndex, tagDevInfo& _objDevInfo)
	{
		if (_nDevIndex < 0)
			return -1;

		CAutoLock lockCapture(&m_lockCapture);

		ICreateDevEnum * pDSDevEnum = nullptr;
		HRESULT hr = CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC,	IID_ICreateDevEnum, (void **)&pDSDevEnum);
		if (hr != NOERROR)
		{
			WriteLogEx(ModuleVideo, LogError, "Failed to create CLSID_SystemDeviceEnum, error 0x%x", hr);
			return -1;
		}

		IEnumMoniker * pDSMonikerDevEnum = nullptr;
		if (1 == _nCapType) //	��Ƶ
			hr = pDSDevEnum->CreateClassEnumerator(CLSID_VideoInputDeviceCategory, &pDSMonikerDevEnum, 0);
		else if(2 == _nCapType)//	��Ƶ
			hr = pDSDevEnum->CreateClassEnumerator(CLSID_AudioInputDeviceCategory, &pDSMonikerDevEnum, 0);
		else
		{
			//	������
			hr = pDSDevEnum->CreateClassEnumerator(CLSID_AudioRendererCategory, &pDSMonikerDevEnum, 0);
		}
		
		if (hr != NOERROR)
		{
			pDSDevEnum->Release();

			WriteLogEx(ModuleVideo, LogError, "Failed to enumerate CLSID_SystemDeviceEnum, error 0x%x.", hr);
			return -1;
		}

		pDSMonikerDevEnum->Reset();// ��λ���豸
		int nIndex = 0;
		ULONG cFetched;
		IMoniker *pMonike = nullptr;
		
		while (S_OK == pDSMonikerDevEnum->Next(1, &pMonike, &cFetched))
		{
			if (nIndex++ == _nDevIndex)
			{
				IPropertyBag *pBag;
				hr = pMonike->BindToStorage(0, 0, IID_IPropertyBag, (void **)&pBag);
				if (S_OK == hr) 
				{
					std::wstring wstrDeviceId, wstrDeviceName;
					VARIANT varName;

					VariantInit(&varName);
					if (pBag->Read(L"FriendlyName", &varName, nullptr) == NOERROR) //Asks the property bag to read the named
					{
						wstrDeviceName = varName.bstrVal;
						SysFreeString(varName.bstrVal);
					}

					VariantClear(&varName);
					VariantInit(&varName);
					if (pBag->Read(L"DevicePath", &varName, nullptr) == NOERROR)
					{
						wstrDeviceId = varName.bstrVal;
						SysFreeString(varName.bstrVal);
					}
					VariantClear(&varName);

					_objDevInfo.strDevName = LPM::W2U(wstrDeviceName);
					_objDevInfo.strDevId = LPM::W2U(wstrDeviceId);
					
					pBag->Release();
					//	��ȡ��Ƶ�豸��ز���
					//if(_nCapType == 1)
					GetCapDevSupportedFormat(_nCapType, _nDevIndex, _objDevInfo.vectDevSupportedFormate);

				}

				pMonike->Release();
			}
			else
			{
				pMonike->Release();
			}

		}
		
		SAFE_RELEASE(pDSMonikerDevEnum);
		SAFE_RELEASE(pDSDevEnum);
		CoFreeUnusedLibraries();

		return _nDevIndex;
	}
	
	int CVideoCaptureBase::GetCapDevSupportedFormat(const int _nCapType,const int _nDevId, std::vector<tagDevSupportedFormate>& _nVectParam)
	{
		_nVectParam.clear();

		if (nullptr == m_pCaptureGb2)
			return -1;

		IBaseFilter* pCaptureFilter = CreateCaptureFilter(_nCapType, _nDevId);		// �����˲���
		if (!pCaptureFilter)
		{
			WriteLogEx(ModuleVideo, LogError, u8"GetCapDevSupportedFormat ,CreateCaptureFilter error");
			return -1;
		}
		IPin* pOutputCapturePin = nullptr;
		if(_nCapType <= 2)
			pOutputCapturePin = GetFirstPin(pCaptureFilter, PINDIR_OUTPUT);
		else
			pOutputCapturePin = GetFirstPin(pCaptureFilter, PINDIR_INPUT);

		if (nullptr == pOutputCapturePin)
		{
			SAFE_RELEASE(pCaptureFilter);
			WriteLogEx(ModuleVideo, LogError, u8"GetCapDevSupportedFormat ,GetFirstPin error");
			return -1;
		}
		IAMStreamConfig* pIamConfig = nullptr;
		HRESULT hr = pOutputCapturePin->QueryInterface(IID_IAMStreamConfig,	(void**)&pIamConfig);

		IAMVideoControl* pVideoControlConfig = nullptr;	//	fps
		HRESULT hrVC = pCaptureFilter->QueryInterface(IID_IAMVideoControl,(void**)&pVideoControlConfig);

		//HRESULT hrVC = pCaptureFilter->QueryInterface(IID_IAMAudioControl, (void**)&pVideoControlConfig);
		if (pIamConfig == nullptr)
		{
			SAFE_RELEASE(pCaptureFilter);
			SAFE_RELEASE(pOutputCapturePin);

			WriteLogEx(ModuleVideo, LogError, u8"GetCapDevSupportedFormat ,GetFirstPin error");
			return -1;
		}

		std::map<tagDevSupportedFormate, int> mapTemp;
		// ����Ԥѡ֧�ֵ���ɫ�ռ�����
		int nCount = 0, nSize = 0;
		pIamConfig->GetNumberOfCapabilities(&nCount, &nSize);
		for (int i = 0; i < nCount; i++)
		{
			AM_MEDIA_TYPE* am_media_type = NULL;
			VIDEO_STREAM_CONFIG_CAPS caps;
			if (pIamConfig->GetStreamCaps(i, &am_media_type, (BYTE*)&caps) == S_OK)
			{
				long long llFrameRate = 0;
				tagDevSupportedFormate tagInfo;

				bool exist = false;

				if (am_media_type->formattype == FORMAT_VideoInfo)
				{
					// this is normal camera. DV capture does not use a VIDEOINFOHEADER
					VIDEOINFOHEADER* vih = (VIDEOINFOHEADER*)am_media_type->pbFormat;
					
					llFrameRate = vih->AvgTimePerFrame;
					//tagInfo.fFps = static_cast<int> (10000000 / vih->AvgTimePerFrame);
					tagInfo.nVideoW = vih->bmiHeader.biWidth;
					tagInfo.nVideoH = vih->bmiHeader.biHeight;

					tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(am_media_type->subtype, tagInfo.strFormate);
					
				}
				else if (am_media_type->formattype == FORMAT_VideoInfo2)
				{
					VIDEOINFOHEADER2* vih2 = (VIDEOINFOHEADER2*)am_media_type->pbFormat;
					//tagInfo.fFps = (10000000.0f / vih2->AvgTimePerFrame);
					llFrameRate = vih2->AvgTimePerFrame;
					tagInfo.nVideoW = vih2->bmiHeader.biWidth;
					tagInfo.nVideoH = vih2->bmiHeader.biHeight;

					tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(am_media_type->subtype, tagInfo.strFormate);
				}
				else if (am_media_type->formattype == FORMAT_WaveFormatEx)
				{
					
					//WAVE_FORMAT_PCM

					WAVEFORMATEX *pWF = (WAVEFORMATEX *)am_media_type->pbFormat;
					tagInfo.nFormate =(AyjImageFormat)( pWF->wFormatTag + 100);//	��Ƶ��ʽ
					tagInfo.nChannels = pWF->nChannels;// 	//	ͨ������
					tagInfo.nSamplesPerSec = pWF->nSamplesPerSec;// =//	������ͨ�� 8000�� 22050�� 44100
					tagInfo.wBitsPerSample = pWF->wBitsPerSample;// = (WORD)(nBytesPerSample * 8); λ��� 8��16
					tagInfo.nBlockAlign = pWF->nBlockAlign;// = (WORD)(nBytesPerSample * nChannels);
					tagInfo.nAvgBytesPerSec = pWF->nAvgBytesPerSec;//= lBytesPerSecond; //	ƽ���������� nAvgBytesPerSec

					tagInfo.strFormate = "WAVE_FORMAT_PCM";					
				}
				
				if (pVideoControlConfig)
				{
						LONGLONG *pFrameDurationList = nullptr;
						LONGLONG maxFps = 0;
						long lListSize = -1;
						SIZE size = {};

						HRESULT hRet = pVideoControlConfig->GetFrameRateList(pOutputCapturePin,	i, size, &lListSize,	&pFrameDurationList);
						
						if (hRet == S_OK && lListSize > 0 && 0 != (maxFps = GetMaxOfFrameArray(pFrameDurationList, lListSize)))
							tagInfo.nFps = static_cast<int> (10000000.0f / maxFps);
						else 
							tagInfo.nFps = 	static_cast<int> (10000000.0f / llFrameRate);
				}

				bool bExist = false;
				for (auto it : _nVectParam)
				{
					//	��Ƶ
					if (_nCapType == 1)
					{
						if (it.nVideoW == tagInfo.nVideoW && it.nVideoH == tagInfo.nVideoH && it.nFormate == tagInfo.nFormate && it.nFps == tagInfo.nFps && it.strFormate == tagInfo.strFormate)
						{
							bExist = true;
							break;
						}
					}
					else if(_nCapType == 2)
					{
						//	��Ƶ
						if (it.nSamplesPerSec == tagInfo.nSamplesPerSec && it.nFormate == tagInfo.nFormate 
							&& it.nChannels == tagInfo.nChannels && it.wBitsPerSample == tagInfo.wBitsPerSample && it.nAvgBytesPerSec == tagInfo.nAvgBytesPerSec)
						{
							bExist = true;
							break;
						}
					}
				}
				if (!bExist)
					_nVectParam.push_back(tagInfo);
			}
		}
		// ��fps_list�Ӵ�С˳������
		if (_nVectParam.size() > 0)
			std::sort(_nVectParam.begin(), _nVectParam.end(), SizeCompareGreater); // std::less����std::greater�������� http://blog.csdn.net/ihadl/article/details/7400929
		
		for (auto it : _nVectParam)
			WriteLogEx(ModuleVideo, LogDebug, u8"w:%-4d,h:%-4d, fomate:%3d--%s, nFps:%-4d", it.nVideoW, it.nVideoH, it.nFormate, it.strFormate.c_str(), (int)it.nFps);
		
		SAFE_RELEASE(pIamConfig);
		SAFE_RELEASE(pVideoControlConfig);
		SAFE_RELEASE(pOutputCapturePin);

		DestroyFilter(pCaptureFilter);

		return 0;
	}
 
	bool CVideoCaptureBase::SetCameraFilterOutpinMediaType(const int _nCaptureType, ICaptureGraphBuilder2* _pCaptureGb2, IBaseFilter* _pCapFilter, const tagDevSupportedFormate& _tagParam)
	{
		if (_pCaptureGb2 == nullptr || _pCapFilter == nullptr)
			return false;
		
		IPin* pOutputCapturePin = GetFirstPin(_pCapFilter, PINDIR_OUTPUT);
		if (nullptr ==pOutputCapturePin)
		{
			WriteLogEx(ModuleVideo, LogError, u8"SetCameraFilterOutpinMediaType ,GetFirstPin error");
			return false;
		}
		IAMStreamConfig* pStreamConfig = nullptr;
		HRESULT hr = pOutputCapturePin->QueryInterface(IID_IAMStreamConfig,	(void**)&pStreamConfig);

		//IAMStreamConfig* pStreamConfig = NULL;
		//HRESULT hr = _pCaptureGb2->FindInterface(&PIN_CATEGORY_CAPTURE, &MEDIATYPE_Interleaved, _pCapFilter, IID_IAMStreamConfig, (void **)&pStreamConfig);
		
		IAMVideoControl* pVideoControlConfig = nullptr;	//	fps
		HRESULT hrVC = _pCapFilter->QueryInterface(IID_IAMVideoControl, (void**)&pVideoControlConfig);
		

		if (nullptr == pStreamConfig)
		{
			WriteLogEx(ModuleVideo, LogError, u8"SetCameraFilterOutpinMediaType error");
			SAFE_RELEASE(pOutputCapturePin);
			SAFE_RELEASE(pStreamConfig);
			SAFE_RELEASE(pVideoControlConfig);

			return false;
		}

		if (pStreamConfig == nullptr)
			return false;

		// ����Ԥѡ֧�ֵ���ɫ�ռ�����
		AM_MEDIA_TYPE* pMediaType = nullptr;
		//
		int nCount = 0, nSize = 0;
		pStreamConfig->GetNumberOfCapabilities(&nCount, &nSize);
		for (int i = 0; i < nCount; i++)
		{
			long long llFrameRate = 0;
			AM_MEDIA_TYPE* pAmMediaType = nullptr;
			VIDEO_STREAM_CONFIG_CAPS caps;
			tagDevSupportedFormate tagInfo;

			if (pStreamConfig->GetStreamCaps(i, &pAmMediaType, (BYTE*)&caps) == S_OK)
			{
				if (pAmMediaType->formattype == FORMAT_VideoInfo)
				{
					// this is normal camera. DV capture does not use a VIDEOINFOHEADER
					VIDEOINFOHEADER* vih = (VIDEOINFOHEADER*)pAmMediaType->pbFormat;
					llFrameRate = vih->AvgTimePerFrame;
					tagInfo.nVideoW = vih->bmiHeader.biWidth;
					tagInfo.nVideoH = vih->bmiHeader.biHeight;

					tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(pAmMediaType->subtype, tagInfo.strFormate);
				}
				else if (pAmMediaType->formattype == FORMAT_VideoInfo2)
				{
					VIDEOINFOHEADER2* vih2 = (VIDEOINFOHEADER2*)pAmMediaType->pbFormat;
					llFrameRate = vih2->AvgTimePerFrame;
					tagInfo.nVideoW = vih2->bmiHeader.biWidth;
					tagInfo.nVideoH = vih2->bmiHeader.biHeight;

					tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(pAmMediaType->subtype, tagInfo.strFormate);
				}
				else if(pAmMediaType->formattype == FORMAT_WaveFormatEx)
				{
					WAVEFORMATEX *pWF = (WAVEFORMATEX *)pAmMediaType->pbFormat;
					tagInfo.nFormate = (AyjImageFormat)(pWF->wFormatTag + 100);//	��Ƶ��ʽ
					tagInfo.nChannels = pWF->nChannels;// = (WORD)nChannels;	//	ͨ������
					tagInfo.nSamplesPerSec = pWF->nSamplesPerSec;// = nFrequency;	//	������ͨ�� 8000�� 22050�� 44100
					tagInfo.wBitsPerSample = pWF->wBitsPerSample;// = (WORD)(nBytesPerSample * 8); λ��� 8��16
					tagInfo.nBlockAlign = pWF->nBlockAlign;// = (WORD)(nBytesPerSample * nChannels);
					tagInfo.nAvgBytesPerSec = pWF->nAvgBytesPerSec;//= lBytesPerSecond; //	ƽ���������� nAvgBytesPerSec
				}
				
				
				if (pVideoControlConfig)
				{
					LONGLONG *pFrameDurationList = nullptr;
					LONGLONG maxFps = 0;
					long lListSize = -1;
					SIZE size = {};

					HRESULT hRet = pVideoControlConfig->GetFrameRateList(pOutputCapturePin, i, size, &lListSize, &pFrameDurationList);

					if (hRet == S_OK && lListSize > 0 && 0 != (maxFps = GetMaxOfFrameArray(pFrameDurationList, lListSize)))
						tagInfo.nFps = static_cast<int> (10000000.0f / maxFps);
					else
						tagInfo.nFps = static_cast<int> (10000000.0f / llFrameRate);
				}
				if (_nCaptureType == 1)
				{
					if (_tagParam.nFormate == tagInfo.nFormate && _tagParam.nVideoH == tagInfo.nVideoH 
						&& _tagParam.nVideoW == tagInfo.nVideoW && _tagParam.nFps == tagInfo.nFps && _tagParam.strFormate == tagInfo.strFormate)
					{
						pMediaType = pAmMediaType;
						break;
					}
				}
				else if(_nCaptureType == 2)
				{
					if (_tagParam.nFormate == tagInfo.nFormate && _tagParam.nAvgBytesPerSec == tagInfo.nAvgBytesPerSec 
						&& _tagParam.nChannels == tagInfo.nChannels && _tagParam.nSamplesPerSec == tagInfo.nSamplesPerSec && _tagParam.nBlockAlign == tagInfo.nBlockAlign)
					{
						pMediaType = pAmMediaType;

						IAMBufferNegotiation* pNeg = nullptr;
						pOutputCapturePin->QueryInterface(IID_IAMBufferNegotiation, (void **)&pNeg);
						if (pNeg)
						{
							ALLOCATOR_PROPERTIES prop = { 0 };
							prop.cbBuffer = 4096*5;
							prop.cBuffers = 6;
							prop.cbAlign = _tagParam.wBitsPerSample * _tagParam.nChannels;
							HRESULT hRet = pNeg->SuggestAllocatorProperties(&prop);

							pNeg->Release();
						}

						break;
					}
				}

				
			}
		}

		hr = pStreamConfig->SetFormat(pMediaType); //MEDIATYPE_Audio
		hr = m_pSampleGrabber->SetMediaType(pMediaType);// MEDIASUBTYPE_PCM
		SAFE_RELEASE(pVideoControlConfig);
		SAFE_RELEASE(pStreamConfig);
		SAFE_RELEASE(pOutputCapturePin);
		

		return (hr == NOERROR);
	}

	int CVideoCaptureBase::SetCapDevFormat(const int _nCaptureType, const tagDevSupportedFormate _tagFormatIn, tagDevSupportedFormate& _tagFormatOut)
	{
		if (m_pGraphBuilder == nullptr)
			return -1;
		
		CAutoLock lockCapture(&m_lockCapture);

		if (m_bIsCaptureing || m_bFilterConnected)
		{
			WriteLogEx(ModuleVideo, LogError, "SetCapDevFormat error , device is capturing ");
			return -2;
		}

		this->CloseStream();
		this->UnConnectFilters();

		//params_temp.pixel_fmt = (enum AVPixelFormat)fmt;

		if (SetCameraFilterOutpinMediaType(_nCaptureType, m_pCaptureGb2, m_pCaptureFilter, _tagFormatIn) ) // ����ý�����ͻ�����connect filter�ɹ���OK��
		{
			// �����Ʋɼ�֡�ʣ�ת�����ϲ�frame duration_ms������ʵ��
			//this->UpdateCaptureFrameRate(params_temp.frame_rate);

			bool bRet = ConnectFilters();

			if (!bRet)
				return -1;

			// ��ȡfilter���Ӵ��ݵ�ý����Ϣ

			long long llFrameRate = 0;
			AM_MEDIA_TYPE connected_media_type;
			m_pSampleGrabber->GetConnectedMediaType(&connected_media_type);
				
			tagDevSupportedFormate& tagInfo = _tagFormatOut;
			if (connected_media_type.formattype == FORMAT_VideoInfo)
			{
				// this is normal camera. DV capture does not use a VIDEOINFOHEADER
				VIDEOINFOHEADER* vih = (VIDEOINFOHEADER*)connected_media_type.pbFormat;
				llFrameRate = vih->AvgTimePerFrame;
				tagInfo.nVideoW = vih->bmiHeader.biWidth;
				tagInfo.nVideoH = vih->bmiHeader.biHeight;

				tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(connected_media_type.subtype, tagInfo.strFormate);
			}
			else if (connected_media_type.formattype == FORMAT_VideoInfo2)
			{
				VIDEOINFOHEADER2* vih2 = (VIDEOINFOHEADER2*)connected_media_type.pbFormat;
				//tagInfo.fFps = (10000000.0f / vih2->AvgTimePerFrame);
				llFrameRate = vih2->AvgTimePerFrame;
				tagInfo.nVideoW = vih2->bmiHeader.biWidth;
				tagInfo.nVideoH = vih2->bmiHeader.biHeight;

				tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(connected_media_type.subtype, tagInfo.strFormate);
			}
			else if (connected_media_type.formattype == FORMAT_VideoInfo2)
			{
				VIDEOINFOHEADER2* vih2 = (VIDEOINFOHEADER2*)connected_media_type.pbFormat;
				//tagInfo.fFps = (10000000.0f / vih2->AvgTimePerFrame);
				llFrameRate = vih2->AvgTimePerFrame;
				tagInfo.nVideoW = vih2->bmiHeader.biWidth;
				tagInfo.nVideoH = vih2->bmiHeader.biHeight;

				tagInfo.nFormate = (AyjImageFormat)GetVideoSubFormat(connected_media_type.subtype, tagInfo.strFormate);
			}
			else if (connected_media_type.formattype == FORMAT_WaveFormatEx)
			{

				//WAVE_FORMAT_PCM

				WAVEFORMATEX *pWF = (WAVEFORMATEX *)connected_media_type.pbFormat;
				tagInfo.nFormate = (AyjImageFormat)(pWF->wFormatTag + 100);//	��Ƶ��ʽ
				tagInfo.nChannels = pWF->nChannels;// = (WORD)nChannels;	//	ͨ������
				tagInfo.nSamplesPerSec = pWF->nSamplesPerSec;// = nFrequency;	//	������ͨ�� 8000�� 22050�� 44100
				tagInfo.wBitsPerSample = pWF->wBitsPerSample;// = (WORD)(nBytesPerSample * 8); λ��� 8��16
				tagInfo.nBlockAlign = pWF->nBlockAlign;// = (WORD)(nBytesPerSample * nChannels);
				tagInfo.nAvgBytesPerSec = pWF->nAvgBytesPerSec;//= lBytesPerSecond; //	ƽ���������� nAvgBytesPerSec

				tagInfo.strFormate = "WAVE_FORMAT_PCM";
			}



			tagInfo.nFps = _tagFormatIn.nFps;
			m_tagCapFormat = tagInfo;

			WriteLogEx(ModuleVideo, LogInfo, u8"CaptureDevFormatSet info w:%d, h:%d, fps:%d, format:%s", tagInfo.nVideoW , tagInfo.nVideoH, tagInfo.nFps, tagInfo.strFormate.c_str());

		}

		return 0;
	}
	
	int CVideoCaptureBase::CaptureInit(const int _nCaptureType, const int _nDevIndex, void* _pUserData, funcFrameCapturedCallback _pFrameCb, funcStateCapturedCallback _pStateCb)
	{
		CAutoLock lockCapture(&m_lockCapture);

		if (m_pCaptureFilter)
			return 1;
		
		int  nRet =CreateCaptureGraph();
		if (0 == nRet)
		{
			m_pCaptureFilter = CreateCaptureFilter(_nCaptureType, _nDevIndex);
			if (!m_pCaptureFilter)
				return -1;

			WriteLogEx(ModuleVideo, LogInfo, u8"CaptureInit nDevIndex:%d, dev name:%s", _nDevIndex, LPM::W2U(m_wstrDeviceName).c_str());
		}
			
		if (0 == nRet)
		{
			m_pUserData = _pUserData;
			m_pFrameCapturedCallback = _pFrameCb;
			m_pStateCapturedCallback = _pStateCb;

			nRet = CreateSampleGrabberFilter();
		}

		return nRet;
	}

	// CaptureRelease 释放采集资源（修复内存泄漏）
	int CVideoCaptureBase::CaptureRelease()
	{
		// 1. 先停止采集
		CapturePause();
		CaptureStop();

		CAutoLock lockCapture(&m_lockCapture);

		// 2. 清理SampleGrabber回调（防止回调中访问已释放对象）
		if (m_pSampleGrabber)
		{
			m_pSampleGrabber->SetCallback(nullptr, 0);
		}

		// 3. 断开所有Filter连接
		UnConnectFilters();

		// 4. 移除并释放所有Filter
		if (m_pGraphBuilder)
		{
			IEnumFilters *pEnum = nullptr;
			HRESULT hr = m_pGraphBuilder->EnumFilters(&pEnum);
			if (SUCCEEDED(hr) && pEnum)
			{
				IBaseFilter *pFilter = nullptr;
				while (S_OK == pEnum->Next(1, &pFilter, nullptr))
				{
					// 移除Filter
					m_pGraphBuilder->RemoveFilter(pFilter);
					// 释放Filter引用
					SAFE_RELEASE(pFilter);
					// 重置枚举器
					pEnum->Reset();
				}
				SAFE_RELEASE(pEnum);
			}
		}

		// 5. 销毁自定义Filter
		DestroyFilter(m_pCaptureFilter);
		DestroyFilter(m_pSampleGrabberFilter);

		// 6. 释放所有COM接口（按依赖顺序）
		SAFE_RELEASE(m_pSampleGrabber);
		SAFE_RELEASE(m_pSampleGrabberFilter);
		SAFE_RELEASE(m_pCaptureInPin);
		SAFE_RELEASE(m_pCaptureOutPin);
		SAFE_RELEASE(m_pMediaEventEx);
		SAFE_RELEASE(m_pCaptureGb2);
		SAFE_RELEASE(m_pCaptureFilter);
		SAFE_RELEASE(m_pMediaCtrl);
		SAFE_RELEASE(m_pGraphBuilder);

		// 7. 重置状态变量
		m_bFilterConnected = false;
		m_bIsCaptureing = false;
		m_eCapState = CapStateNotInit;
		m_wstrDeviceName.clear();

		WriteLogEx(ModuleVideo, LogInfo, u8"CaptureRelease completed for device: %s", LPM::W2U(m_wstrDeviceName).c_str());

		return 0;
	}

	// CaptureStart 开始视频采集
	int CVideoCaptureBase::CaptureStart()
	{
		CAutoLock lockCapture(&m_lockCapture);

		bool bRet = false;

		// 1. 检查媒体控制接口是否有效
		if (!m_pMediaCtrl)
		{
			WriteLogEx(ModuleVideo, LogError, "CaptureStart failed: MediaControl is null");
			return -1;
		}

		// 2. 检查当前状态，如果是暂停状态则恢复，否则重新启动
		if (m_eCapState == CapStatePaused)
		{
			// 从暂停状态恢复
			HRESULT hr = m_pMediaCtrl->Run();
			if (SUCCEEDED(hr))
			{
				bRet = true;
				m_bIsCaptureing = true;
				SendCapState(CapStateRunning);
				WriteLogEx(ModuleVideo, LogInfo, "CaptureStart: Resume from pause successful");
			}
			else
			{
				WriteLogEx(ModuleVideo, LogError, "CaptureStart: Resume from pause failed, hr=0x%x", hr);
			}
		}
		else
		{
			// 正常启动流程
			bRet = OpenStream();
			WriteLogEx(ModuleVideo, LogInfo, "CaptureStart: %s", bRet ? "Normal start successful" : "Normal start failed");
		}

		return bRet ? 0 : -1;
	}
	// CapturePause 暂停视频采集（保持Filter连接）
	int CVideoCaptureBase::CapturePause()
	{
		CAutoLock lockCapture(&m_lockCapture);

		// 1. 检查当前是否正在采集
		if (!m_pMediaCtrl || !m_bIsCaptureing)
		{
			WriteLogEx(ModuleVideo, LogWarning, "CapturePause: Not capturing or MediaControl is null");
			return 0;
		}

		// 2. 暂停DirectShow图形（保持Filter连接状态）
		HRESULT hr = m_pMediaCtrl->Pause();
		if (SUCCEEDED(hr))
		{
			// 注意：不设置m_bIsCaptureing = false，保持采集状态标志
			SendCapState(CapStatePaused);
			WriteLogEx(ModuleVideo, LogInfo, "CapturePause: Pause successful");
		}
		else
		{
			WriteLogEx(ModuleVideo, LogError, "CapturePause: Pause failed, hr=0x%x", hr);
			return -1;
		}

		return 0;
	}

	int CVideoCaptureBase::CaptureStop()
	{
		CAutoLock lockCapture(&m_lockCapture);

		bool bRet = false;

		if (m_pMediaCtrl && m_bIsCaptureing)
		{
			bRet = CloseStream();

			SendCapState(CapStateStopped);

			WriteLogEx(ModuleVideo, LogInfo, "CaptureStop : %s", bRet == true ? "Capture Stop ok" : "Capture Stop error");
		}

		return bRet == true ? 0 : -1;
	}

	eCapDevState CVideoCaptureBase::CaptureGetState()
	{
		return m_eCapState;
	}

	int CVideoCaptureBase::CreateCaptureGraph()
	{
		if (m_pGraphBuilder == nullptr)
		{
			// ����IGraphBuilder�ӿ�
			HRESULT hr = CoCreateInstance(CLSID_FilterGraph, NULL, CLSCTX_INPROC_SERVER, IID_IGraphBuilder, (void**)&m_pGraphBuilder);
			if (FAILED(hr))
			{
				WriteLogEx(ModuleVideo, LogError, "Failed to create graph builder");
				return -1;
			}

			// ����ICaptureGraphBuilder2�ӿ�
			hr = CoCreateInstance(CLSID_CaptureGraphBuilder2, NULL, CLSCTX_INPROC, IID_ICaptureGraphBuilder2, (void**)&m_pCaptureGb2);
			if (FAILED(hr))
			{
				WriteLogEx(ModuleVideo, LogError, "Failed to create graph builder");
				return -1;
			}
			// ��ʼ���˲�������������IGraphBuilder
			m_pCaptureGb2->SetFiltergraph(m_pGraphBuilder);

			// ��ѯý����ƽӿ�
			hr = m_pGraphBuilder->QueryInterface(IID_IMediaControl, (void**)&m_pMediaCtrl);
			if (FAILED(hr))
			{
				WriteLogEx(ModuleVideo, LogError, "Failed to create graph builder");
				return -1;
			}
			//	ý���¼�
			hr = m_pGraphBuilder->QueryInterface(IID_IMediaEventEx, (void**)&m_pMediaEventEx);
			if (SUCCEEDED(hr))
			{
				//long lCode = 0;
				//m_pMediaEventEx->WaitForCompletion(INFINITE, &lCode);
				ThreadStart();
			}
		}

		return 0;
	}

	IBaseFilter* CVideoCaptureBase::BindCaptureFilter(const int _nCapType, const int _nDevIndex)
	{
		// �����豸ö����
		ICreateDevEnum *pCreateDevEnum = nullptr;
		if (CoCreateInstance(CLSID_SystemDeviceEnum, NULL, CLSCTX_INPROC_SERVER, IID_ICreateDevEnum, (void**)&pCreateDevEnum) != NOERROR)
		{
			return nullptr;
		}

		// ������Ƶ�����豸��ö����
		IEnumMoniker *pEnumMoniker = nullptr;

		

		HRESULT hr = E_FAIL;

		if (1 == _nCapType) //	��Ƶ
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_VideoInputDeviceCategory, &pEnumMoniker, 0);
		else if (2 == _nCapType)//	��˷�
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_AudioInputDeviceCategory, &pEnumMoniker, 0);
		else
		{
			//	������
			hr = pCreateDevEnum->CreateClassEnumerator(CLSID_AudioRendererCategory, &pEnumMoniker, 0);
		}
		if (hr != NOERROR)
		{
			pCreateDevEnum->Release();
			return nullptr;
		}

		IBaseFilter* pCameraFilter = nullptr;

		pEnumMoniker->Reset();// ��λ���豸
		unsigned long nFetched;
		IMoniker *pMoniker;
		int nIndex = 0;
		// ��ȡ�豸
		while (pEnumMoniker->Next(1, &pMoniker, &nFetched) == S_OK)
		{
			// ��ȡ���豸�����Լ�
			IPropertyBag *bag;
			if (SUCCEEDED(pMoniker->BindToStorage(0, 0, IID_IPropertyBag, (void**)&bag)))
			{
				if (_nDevIndex == nIndex)
				{
					VARIANT var;
					VariantInit(&var);
					var.vt = VT_BSTR;// ������Ƕ���������
					if (bag->Read(L"FriendlyName", &var, nullptr) == NOERROR)//if (bag->Read(L"DevicePath", &var, NULL) == NOERROR)
					{
						m_wstrDeviceName = var.bstrVal;

						SysFreeString(var.bstrVal);
						
						// �ɼ��豸�벶���˲�������
						pMoniker->BindToObject(0, 0, IID_IBaseFilter, (void**)&pCameraFilter);
						SysFreeString(var.bstrVal); //�ͷŶ�����������Դ�������ͷ�

						WriteLogEx(ModuleVideo, LogInfo, u8"capture dev name:%s", LPM::W2U(m_wstrDeviceName.c_str()).c_str());

						VariantClear(&var);
							
					}
					SysFreeString(var.bstrVal); //�ͷŶ�����������Դ�������ͷ�
					VariantClear(&var);

					bag->Release();
					pMoniker->Release();
					break;
				}else
					bag->Release();
			}

			pMoniker->Release();

			nIndex++;
		}

		SAFE_RELEASE(pEnumMoniker);
		SAFE_RELEASE(pCreateDevEnum);

		return pCameraFilter;
	}

	IBaseFilter*  CVideoCaptureBase::CreateCaptureFilter(const int _nCaptureType, const int _nDevIndex)
	{
		if (_nDevIndex < 0)
			return nullptr;

		IBaseFilter* pCaptureFilter = BindCaptureFilter(_nCaptureType, _nDevIndex);
		if (pCaptureFilter)
		{
			HRESULT hr = S_FALSE;
			if(_nCaptureType == 1)
				hr = m_pGraphBuilder->AddFilter(pCaptureFilter, L"Video Capture Filter");
			else if(_nCaptureType == 2)
				hr = m_pGraphBuilder->AddFilter(pCaptureFilter, L"Audio Capture Filter");
			else if (_nCaptureType == 3)
				hr = m_pGraphBuilder->AddFilter(pCaptureFilter, L"Audio Renderer Filter");
			
			if (FAILED(hr))
			{
				DestroyFilter(pCaptureFilter);
				SAFE_RELEASE(pCaptureFilter);
				pCaptureFilter = nullptr;
			}
		}

		return pCaptureFilter;
	}
	
	int  CVideoCaptureBase::DestroyFilter(IBaseFilter*& _pFilter)
	{
		if (m_pGraphBuilder && _pFilter)
		{
			m_pGraphBuilder->RemoveFilter(_pFilter);
		}

		if (_pFilter)
			SAFE_RELEASE(_pFilter);
		

		return 0;
	}

	int CVideoCaptureBase::CreateSampleGrabberFilter()
	{
		if (m_pSampleGrabberFilter == nullptr)
		{
			HRESULT hr = CoCreateInstance(CLSID_SampleGrabber, NULL, CLSCTX_INPROC_SERVER, IID_IBaseFilter, reinterpret_cast<void**>(&m_pSampleGrabberFilter));
			if (FAILED(hr))
				return -1;
			
			// ��SampleGrabberFilter�����������в�ѯISampleGrabber�ӿ�
			hr = m_pSampleGrabberFilter->QueryInterface(IID_ISampleGrabber, reinterpret_cast<void**>(&m_pSampleGrabber));
			//this->SetCaptureMediaType();

			// ��_sample_grabber_filter������Graph��
			hr = m_pGraphBuilder->AddFilter(m_pSampleGrabberFilter, L"SampleGrabber");// ���뵽Graph��
			if (FAILED(hr))
				return -1;

			if (m_pFrameCapturedCallback)
			{
				// �ϲ�ͨ��__fFrameCapturedCallback()�ص�������ȡ�ɼ�����

				// ���ûص�(CallBack),ʹGrabber�ܹ�ͨ��BufferCB�Զ���ɲɼ����ݡ� ����,��DirectShow��������������,mCB.bufferCB���Զ�ִ��,��ȡGraph�е�����.
				hr = m_pSampleGrabber->SetBufferSamples(false);  // don''t buffer the samples as they pass through
				hr = m_pSampleGrabber->SetOneShot(false);	    // only grab one at a time, stop stream after grabbing one sample
				hr = m_pSampleGrabber->SetCallback(this, 1);     // 1��ʾͨ��BufferCB()�ص���mCBΪCSampleGrabber����set the callback, so we can grab the one sample
			}
			else
			{
				// �ϲ�ͨ��QueryFrame()��ȡ�ɼ�����
				hr = m_pSampleGrabber->SetBufferSamples(true);
				hr = m_pSampleGrabber->SetOneShot(false);	    // only grab one at a time, stop stream after grabbing one sample
				hr = m_pSampleGrabber->SetCallback(nullptr, 0);     // 0��ʾͨ��SampleCB()�ص���
			}
			
		}
		else
			return -1;

		return 0;
	}

	bool CVideoCaptureBase::ConnectFilters()
	{
		if (m_pGraphBuilder && !m_bFilterConnected)
		{
			// ����capture filter��sample grabber filter
			if (m_pCaptureOutPin == nullptr && m_pCaptureInPin == nullptr)
			{
				m_pCaptureOutPin = GetFirstPin(m_pCaptureFilter, PINDIR_OUTPUT);
				m_pCaptureInPin = GetFirstPin(m_pSampleGrabberFilter, PINDIR_INPUT);

				if (m_pCaptureOutPin == nullptr || m_pCaptureInPin == nullptr)
				{
					SAFE_RELEASE(m_pCaptureOutPin);
					SAFE_RELEASE(m_pCaptureInPin);
					WriteLogEx(ModuleVideo, LogError, u8"ConnectFilters error m_pCaptureOutPin : %s,m_pCaptureInPin : %s ", m_pCaptureOutPin == nullptr ? "nullptr" : "ok", m_pCaptureInPin == nullptr ? "nullptr" : "ok");
				}

				// ��������֪���ǲ��Ǳ����ڹ���ͼ������ǰ���òɼ�֡��
				//SetCameraCaptureFPS(_capture_gb2, _capture_filter, 12);x80040217
				//HRESULT hr = m_pGraphBuilder->Connect(m_pCaptureOutPin, m_pCaptureInPin);// capture filter --> SampleGrabber
				HRESULT hr = m_pGraphBuilder->ConnectDirect(m_pCaptureOutPin, m_pCaptureInPin, nullptr);

				if (SUCCEEDED(hr))
					m_bFilterConnected = true;
				else
					WriteLogEx(ModuleVideo, LogError, u8"capture error ConnectDirect HRESULT:%0x 0x", hr);
				
			}

			//capture_out_pin->Release();
			//grabber_in_pin->Release();
		}

		return m_bFilterConnected;
	}
	
	void CVideoCaptureBase::UnConnectFilters()
	{
		if (m_bFilterConnected)
		{
			// ����capture filter��sample grabber filter
			if (m_pCaptureOutPin != nullptr)
			{
				m_pGraphBuilder->Disconnect(m_pCaptureOutPin);
				//m_pGraphBuilder->Disconnect(pCaptureOutPin);
				//IPin* pNullPin = nullptr;
				//m_pCaptureOutPin->ConnectedTo(&pNullPin);
			}
			
			if (m_pCaptureInPin)
			{
				m_pGraphBuilder->Disconnect(m_pCaptureInPin);
				//m_pGraphBuilder->Disconnect(pCaptureInPin);
				//IPin* pNullPin = nullptr;
				
				//m_pCaptureInPin->ConnectedTo(&pNullPin);
			}
			m_bFilterConnected = false;
		}

		SAFE_RELEASE(m_pCaptureOutPin);
		SAFE_RELEASE(m_pCaptureInPin);
	}

	IPin* CVideoCaptureBase::GetFirstPin(IBaseFilter* filter, PIN_DIRECTION pin_dir)
	{
		IPin* pin = nullptr;

		IEnumPins *pEnum = 0;
		HRESULT hr = filter->EnumPins(&pEnum);
		if (FAILED(hr))
		{
			return nullptr;
		}
		IPin* pin_iter = 0;
		while (pEnum->Next(1, &pin_iter, nullptr) == S_OK)
		{
			PIN_DIRECTION pin_type;
			pin_iter->QueryDirection(&pin_type);
			if (pin_type == pin_dir)
			{
				pEnum->Release();
				return pin_iter;
			}

			pin_iter->Release();
		}

		// Did not find a matching pin.
		pEnum->Release();
		return nullptr;
	}


	void CVideoCaptureBase::SendCapState(eCapDevState _eCapState)
	{
		if (m_pStateCapturedCallback && _eCapState != m_eCapState)
		{
			m_eCapState = _eCapState;
			m_pStateCapturedCallback(m_eCapState, m_pUserData);
		}
	}

	//	����
	bool CVideoCaptureBase::OpenStream()
	{
		if (m_bFilterConnected )
		{
			HRESULT hr = E_FAIL;
		
			if (FAILED(m_pMediaCtrl->Run()))
			{
				WriteLogEx(ModuleVideo, LogError, u8"capture OpenStream m_pMediaCtrl->Run() HRESULT:%0x 0x", hr);
				return false;
			}
			else
			{
				SendCapState(CapStateRunning);
				m_bIsCaptureing = true;
			}
		}
	
		return m_bIsCaptureing;

	}

	bool CVideoCaptureBase::CloseStream()
	{
		if (m_bIsCaptureing)
		{
			HRESULT hr = m_pMediaCtrl->Stop();
			m_bIsCaptureing = false;
		}

		return true;
	}

	long CVideoCaptureBase::QueryFrame(char* buf, int buf_len)
	{
		return 0;
	}

	STDMETHODIMP CVideoCaptureBase::QueryInterface(REFIID riid, void ** ppv)
	{
		return S_OK;
	}
	ULONG STDMETHODCALLTYPE CVideoCaptureBase::AddRef()
	{
		return S_OK;
	}
	ULONG STDMETHODCALLTYPE CVideoCaptureBase::Release()
	{
		return S_OK;
	}
	//
	HRESULT STDMETHODCALLTYPE CVideoCaptureBase::SampleCB(double SampleTime, IMediaSample *pSample)
	{
		return S_OK;
	}
	HRESULT STDMETHODCALLTYPE CVideoCaptureBase::BufferCB(double SampleTime, BYTE *sampleBuf, long BufferLen)
	{
		if (m_pFrameCapturedCallback && BufferLen > 0)
		{
			(*m_pFrameCapturedCallback)(SampleTime, sampleBuf, BufferLen, m_tagCapFormat.nFormate, m_pUserData);
			return S_OK;
		}
		else
			return S_FALSE;
	}

	int CVideoCaptureBase::ThreadMainLoop(void)
	{
			// Disregard if we don't have an IMediaEventEx pointer.
		if (m_pMediaEventEx == nullptr) 
			return 0 ;
		while (!ThreadIsStop())
		{
			long evCode = 0;
			long long param1 = 0, param2 = 0;
			HRESULT hr = E_FAIL;
			// Get all the events
			if (m_pMediaEventEx)
			{
				if (S_OK == (hr = m_pMediaEventEx->GetEvent(&evCode, &param1, &param2, 0)))//ѭ����ѯ�¼�
				{
					//WriteLogEx(ModuleVideo, LogDebug, u8"GetEvent code:%ld , para1:%lld, para2:%lld", evCode, param1, param2);
					m_pMediaEventEx->FreeEventParams(evCode, param1, param2);

					switch (evCode)
					{
					case 31:
					{
						if (0 == param2 )
						{
							SendCapState(CapStateDevBreak);
						}
						else if (1 == param2)
						{
							//	�豸���²���
							SendCapState(CapStateDevInsertReset);
						}
					}
					break;
					
					// Sent by filter supporting IAMExtDevice
					// Param1 has the new mode
					// Param2 has the device ID of the sending object
					case EC_COMPLETE:  // Fall through.
					case EC_USERABORT: // Fall through.
					case EC_ERRORABORT:
						//CleanUp();
						PostQuitMessage(0);
						break;
					}
				}
			}
			
			LPM::SleepExEx(100);
		}
		
		
		return 0;
	}
}

