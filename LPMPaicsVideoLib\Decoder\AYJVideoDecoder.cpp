#include "AyjVideoDecoder.h"
#include "ImageScale/ImageScale.h"

#include <io.h>

namespace AYJ
{
	CAyjVideoDecoder::CAyjVideoDecoder()
	{
		
	}

	CAyjVideoDecoder::~CAyjVideoDecoder()
	{
		DecoderClose();
	}

	int CAyjVideoDecoder::DecoderInit(int _nVideoW, int _nVideoH, bool _bFlagH264)
	{
		if (m_bInit)
			return -2;

		if (_nVideoW <= 0 || _nVideoH <= 0)
		{
			WriteLogEx(ModuleVideo, LogError, "DecoderInit error _nVideoW:%d,_nVideoH:%d ", _nVideoW, _nVideoH);
			return -1;
		}
		
		CAutoLock lockTemp(&m_lockApi);

		if (_bFlagH264)
			m_pCodec = avcodec_find_decoder(AV_CODEC_ID_H264);
		else
			m_pCodec = avcodec_find_decoder(AV_CODEC_ID_HEVC);
		
		if (!m_pCodec)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_find_decoder fail");
			return -1;
		}
		m_pCodecCtx = avcodec_alloc_context3(m_pCodec);
		if (!m_pCodecCtx)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_alloc_context3 fail");
			return -1;
		}
		m_pCodecCtx->coded_width = _nVideoW;
		m_pCodecCtx->coded_height = _nVideoH ;

		if (avcodec_open2(m_pCodecCtx, m_pCodec, NULL) < 0)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_open2 fail");
			return -1;
		}

		m_bInit = true;
		av_init_packet(&m_objPacket);

		m_pFrameBuff = av_frame_alloc();
		m_pFrameBuff->format = 0;
		m_pFrameBuff->width = _nVideoW;
		m_pFrameBuff->height = _nVideoH;
		av_frame_get_buffer(m_pFrameBuff, 32);

		m_objFrameOut = new tagVdieoFrame(eImageYuv420p, _nVideoW, _nVideoH);

		return 0;
	}
	
	int CAyjVideoDecoder::DecoderClose()
	{
		CAutoLock lockTemp(&m_lockApi);

		if (m_pCodecCtx != nullptr) {
			avcodec_free_context(&m_pCodecCtx);
			m_pCodecCtx = nullptr;
		}

		avcodec_close(m_pCodecCtx);

		if (m_pFrameBuff)
		{
			av_frame_free(&m_pFrameBuff);
			m_pFrameBuff = nullptr;
		}
		if (m_objFrameOut)
		{
			delete m_objFrameOut;
			m_objFrameOut = nullptr;
		}

		return 0;
	}

	int CAyjVideoDecoder::DecoderGetInfo(int& _nVideoWOut, int& _nVideoH)
	{
		int nRet = -1;
		if (m_bInit && m_pCodecCtx)
		{
			_nVideoWOut = m_pCodecCtx->coded_width;
			_nVideoH = m_pCodecCtx->coded_height;;
			nRet = 0;
		}

		return nRet;
	}

	int CAyjVideoDecoder::DecoderFlush()
	{
		if (m_pCodecCtx)
			avcodec_flush_buffers(m_pCodecCtx);
		if (m_pCodecCtx && m_pCodecCtx->codec->flush)
			m_pCodecCtx->codec->flush(m_pCodecCtx);

		return 0;
	}

	int CAyjVideoDecoder::Decoder(unsigned char* _ucData, int _nDataSize, AVFrame& _pAvFrameOut)
	{
		if (!m_bInit)
			return -1;
		
		CAutoLock lockTemp(&m_lockApi);

		//av_packet_from_data(&m_objPacket, _ucData, _nDataSize);
		m_objPacket.data = _ucData;
		m_objPacket.size = _nDataSize;
		m_objPacket.stream_index = 0;
		if (m_objPacket.stream_index == AVMEDIA_TYPE_VIDEO)
		{
			int nRet = avcodec_send_packet(m_pCodecCtx, &m_objPacket);
			if (nRet >=0)
			{
				nRet = avcodec_receive_frame(m_pCodecCtx, &_pAvFrameOut);
				if (nRet >= 0)
				{
					return 0;
				}
			}
		}

		return -1;
	}

	int CAyjVideoDecoder::Decoder(long long _llPts, unsigned char* _ucData, int _nDataSize, tagVdieoFrame** _pObjFrameOut)
	{
		if (!m_bInit)
			return -1;

		CAutoLock lockTemp(&m_lockApi);
		//av_packet_from_data(&m_objPacket, _ucData, _nDataSize);

		m_objPacket.data = _ucData;
		m_objPacket.size = _nDataSize;

		m_objPacket.stream_index = 0;
		m_objPacket.pts = _llPts;

		if (m_objPacket.stream_index == AVMEDIA_TYPE_VIDEO)
		{
			int nRet = avcodec_send_packet(m_pCodecCtx, &m_objPacket);
			if (nRet >= 0)
			{
				nRet = avcodec_receive_frame(m_pCodecCtx, m_pFrameBuff);
				if (nRet >= 0)
				{
					if (!m_objFrameOut)
						return -1;

					//SavePic(m_pFrameBuff, "D://Pic//TestFrame.png");
					int nVideoH = m_pFrameBuff->height;
					int nVideoW = m_pFrameBuff->width;
					//	Y
					for (int i = 0; i < nVideoH; i++)
						memcpy(m_objFrameOut->pVideoData + nVideoW * i, m_pFrameBuff->data[0] + m_pFrameBuff->linesize[0] * i, nVideoW);
					// U
					for (int j = 0; j < nVideoH / 2; j++)
						memcpy(m_objFrameOut->pVideoData + nVideoW * nVideoH + nVideoW / 2 * j, m_pFrameBuff->data[1] + m_pFrameBuff->linesize[1] * j, nVideoW / 2);
					// V
					for (int k = 0; k < nVideoH / 2; k++)
						memcpy(m_objFrameOut->pVideoData + nVideoW * nVideoH + nVideoW / 2 * (nVideoH / 2) + nVideoW / 2 * k, m_pFrameBuff->data[2] + m_pFrameBuff->linesize[2] * k, nVideoW / 2);

					m_objFrameOut->eFrameFormat = eImageYuv420p;
					m_objFrameOut->unWidth = nVideoW;
					m_objFrameOut->unHeight = nVideoH;
					m_objFrameOut->llPts = m_pFrameBuff->pts;
					m_objFrameOut->unDataSize = nVideoW * nVideoH * 3 / 2;

					*_pObjFrameOut = m_objFrameOut;

					return 0;
				}
			}
		}
		return -1;
	}


	IAyjVideoDecoder* IAyjVideoDecoder::Create()
	{
		IAyjVideoDecoder* pInstance =(IAyjVideoDecoder*) new CAyjVideoDecoder();

		return pInstance;
	}

	void IAyjVideoDecoder::Release(IAyjVideoDecoder*& _pObj)
	{
		if (_pObj)
		{
			IAyjVideoDecoder* pInstance = (IAyjVideoDecoder*)_pObj;
			delete pInstance;
			_pObj = nullptr;
		}
	}
}