#include "VideoInterface.h"
#include "RenderSdl/InterfaceRender.h"

#include "./RenderSdl/RenderSdl.h"
#include "./RenderSdl/RenderDx.h"

#include "ImageScale/ImageScale.h"

#include "../Depends/Common/include/LPMCommonLib.h"

using namespace LPM;

namespace AYJ
{
	void* RenderCreate(unsigned long _ulHandle, const int _nSrcWidth, const int _nSrcHeight, bool _bIsDxMode)
	{
		int nRet = -1;
		CRenderBase* pRender = nullptr;
		if (_bIsDxMode)
		{
			pRender = new CRenderDx(_bIsDxMode);
		}
		else
		{
			pRender = new CRenderSdl(_bIsDxMode);
		}

		if (pRender)
		{
			nRet = pRender->Init(_ulHandle, _nSrcWidth, _nSrcHeight);
		}

		if (nRet != 0)
		{
			WriteLogEx(ModuleVideo, LogError, "CreateRenderSdl error");
			pRender->Release();
			delete pRender;
			pRender = nullptr;
		}

		return pRender;
	}

	int RenderRelease(void*& _pObj)
	{
		int nRet = -1;
		CRenderBase* pRender = (CRenderBase*)_pObj;
		if (pRender)
		{
			nRet = 0;
			pRender->Release();
			delete pRender;
			_pObj = nullptr;
		}

		return nRet;
	}

	int RenderPutYuv420pData(void* _pObj, unsigned char* _pYuvBuf, int _nW, int _nH)
	{
		CRenderBase* pRender = (CRenderBase*)_pObj;
		if (pRender)
		{
			pRender->PutYuv420pData(_pYuvBuf);
		}

		return 0;
	}

}
