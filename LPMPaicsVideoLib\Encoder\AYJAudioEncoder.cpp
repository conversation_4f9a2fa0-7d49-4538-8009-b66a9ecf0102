﻿/*
 * Filename:  CAyjAuidoEncoder.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 5/16/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */

#include "AYJAudioEncoder.h"
#include "../common/typedefs.h"

#include <fstream>
#include "../recorder/AYJVideoWrite.h"

#include "../ImageScale/ImageScale.h"

namespace AYJ
{

	CAyjAuidoEncoder::CAyjAuidoEncoder(): m_bOpen(false)
    {
    }

	CAyjAuidoEncoder::~CAyjAuidoEncoder()
    {
		EncoderClose();
    }

	int CAyjAuidoEncoder::EncoderInit(int _nSampleRate, int _nChannels, int _nBitsPerSample, int _nAvgBytesPerSec, const char* _pSaveAacPath)
	{
		int nRet = -1;
		if (m_bOpen)
			return nRet;
		
		CAutoLock tempLock(&m_lockApi);

		m_llEncodePts = 0;
		
		m_pAVCodec = avcodec_find_encoder(AV_CODEC_ID_AAC);

		if (!m_pAVCodec)
		{
			WriteLogEx(ModuleVideo, LogError, "EncoderInit Codec not found");
			return nRet;
		}

		m_pAVContext = avcodec_alloc_context3(m_pAVCodec);
		if (!m_pAVContext)
		{
			WriteLogEx(ModuleVideo, LogError, "Could not allocate video codec context");
			return nRet;
		}
		m_pAVContext->codec_id = AV_CODEC_ID_AAC;
		m_pAVContext->codec_type = AVMEDIA_TYPE_AUDIO;
		m_pAVContext->sample_rate = _nSampleRate;//	采样率
		m_pAVContext->channels = _nChannels;			//	 通道数
		m_pAVContext->sample_fmt = AV_SAMPLE_FMT_FLTP;// //AAC 支持float类型 
		m_pAVContext->bit_rate = 80000;// _nAvgBytesPerSec;	//	// 码率 192000
		m_pAVContext->channel_layout =  av_get_default_channel_layout(_nChannels);
		m_pAVContext->time_base = AVRational{ 1, m_pAVContext->sample_rate };
		m_pAVContext->profile = FF_PROFILE_AAC_LOW;    //
			//	声道类型
		m_pAVContext->flags |= AV_CODEC_FLAG_GLOBAL_HEADER;	//	音频帧使用公共头部
		m_pAVContext->strict_std_compliance = FF_COMPLIANCE_EXPERIMENTAL;

		m_nBitsPerSample = _nBitsPerSample;

		if (_nBitsPerSample == 8)
			m_nSrcFormate = AV_SAMPLE_FMT_U8;
		else if (_nBitsPerSample == 16)
			m_nSrcFormate = AV_SAMPLE_FMT_S16;
		else 
			m_nSrcFormate =  AV_SAMPLE_FMT_S32;

		//	打开音频的编码器
		if ((nRet = avcodec_open2(m_pAVContext, m_pAVCodec, nullptr)) < 0)
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_open2 Could not open codec");
			return nRet;
		}

		//m_pAVContext->frame_size = 22050;
		//创建输出上下文结构体
		if (_pSaveAacPath)
		{
			m_strAacFileName = _pSaveAacPath;

			avformat_alloc_output_context2(&m_pFormateContext, nullptr, nullptr, _pSaveAacPath);//初始化结构体
			if (m_pFormateContext)
			{
				//创建音频流 
				m_pAudioStream = avformat_new_stream(m_pFormateContext, nullptr);
				m_pAudioStream->codecpar->codec_tag = 0;//在一个文件中，音频流 视频流 字幕流的下标分别为 0 1 2 
				// 编码器参数复制到音频流上,省得再设置一遍
				avcodec_parameters_from_context(m_pAudioStream->codecpar, m_pAVContext);
				// 第三个参数，0音频、1视频
				av_dump_format(m_pFormateContext, 0, _pSaveAacPath, 1);
				//打开输出文件流
				int nRet = avio_open(&m_pFormateContext->pb, _pSaveAacPath, AVIO_FLAG_READ_WRITE);
				if (nRet == 0)
					avformat_write_header(m_pFormateContext, nullptr);//写入头部信息
			}
		}

		// 设置参数， 1. 上下文 2.输出声道布局 4.输出采样率, 5.输入声道布局 6.输入样本格式 7.输入采样率 8.配音 9.日志
		m_pAudioConvertCtx = swr_alloc_set_opts(m_pAudioConvertCtx, m_pAVContext->channel_layout, m_pAVContext->sample_fmt, m_pAVContext->sample_rate,
			AV_CH_LAYOUT_STEREO, m_nSrcFormate, _nSampleRate, 0, 0);
		if (!m_pAudioConvertCtx)
			return -1;

		// 初始化
		if (swr_init(m_pAudioConvertCtx) < 0)
			return -1;

		m_pAVFrame = av_frame_alloc();
		if (m_pAVFrame == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, "av_frame_alloc error");
			return nRet;
		}

		m_pAVFrame->format = m_pAVContext->sample_fmt;
		m_pAVFrame->channels = m_pAVContext->channels;
		m_pAVFrame->channel_layout = m_pAVContext->channel_layout;
		m_pAVFrame->nb_samples = m_pAVContext->frame_size;	//	// 每一帧音频的样本数量  一般情况下，一帧PCM是由2048次采样组成的
		nRet = av_frame_get_buffer(m_pAVFrame, 0);

		if (nRet < 0) {
			WriteLogEx(ModuleVideo, LogError, "Could not allocate the video frame data");
			return nRet;
		}

		m_pAVPacket = av_packet_alloc();
		// 包初始化
		av_init_packet(m_pAVPacket);

		m_bOpen = true;
		
		return nRet;
	}


	int  CAyjAuidoEncoder::Encoder(unsigned char* _ucDataIn, int _nDataSizeIn, unsigned char**  _szDataOut, int& _nDataSizeOut, long long _llPts)
	{
		CAutoLock tempLock(&m_lockApi);
		_nDataSizeOut = 0;
		if (m_pAudioConvertCtx == nullptr || !m_bOpen)
			return  -1;

		int nReadSize = m_pAVFrame->nb_samples * m_pAVFrame->channels *(m_nBitsPerSample /8); //双声道，float
		
		int nCount = _nDataSizeIn / nReadSize;
		int ndd = _nDataSizeIn % nReadSize;

		for (int nIndex = 0; nIndex < nCount; nIndex++)
		{
			const uint8_t* pData[1];
			pData[0] = (uint8_t*)_ucDataIn + nIndex* nReadSize;

			// 重采样 1.重采样上下文 2.重采样后的数据 3.样本数量不变 4.重采样之前的数据
			int nLen = swr_convert(m_pAudioConvertCtx, m_pAVFrame->data, m_pAVFrame->nb_samples, pData, m_pAVFrame->nb_samples);
			if (nLen <= 0) 
				break;

			int nRet = avcodec_send_frame(m_pAVContext, m_pAVFrame);

			while (nRet >= 0)
			{
				av_packet_unref(m_pAVPacket);

				nRet = avcodec_receive_packet(m_pAVContext, m_pAVPacket);

				if (nRet == AVERROR(EAGAIN) || nRet == AVERROR_EOF)
				{
					// 有可能是编码器需要先缓冲一部分数据，并不是真正的编码错误
					break;
				}
				else if (nRet < 0)
				{
					// 产生了真正的编码错误
					WriteLogEx(ModuleVideo, LogError, "Error during encoding");
					break;
				}
				m_llEncodePts += m_pAVFrame->nb_samples;
				_nDataSizeOut += m_pAVPacket->size;

				/*if (nullptr == m_szEncoderAacData)
				{
					m_nEncoderAacDataSize = _nDataSizeOut * 10;
					m_szEncoderAacData = new unsigned char(m_nEncoderAacDataSize);
				}

				if (_nDataSizeOut > m_nEncoderAacDataSize)
				{
					delete m_szEncoderAacData;
					m_nEncoderAacDataSize = _nDataSizeOut * 2;
					m_szEncoderAacData = new unsigned char(m_nEncoderAacDataSize);
				}*/
				
				if (m_pFormateContext)
				{
					//编码后的数据写入acc文件中
					m_pAVPacket->stream_index = 0;//音频流 0
					m_pAVPacket->dts = m_llEncodePts;
					m_pAVPacket->pts = m_llEncodePts;

					//转换时间基
					av_packet_rescale_ts(m_pAVPacket, m_pAVContext->time_base, m_pAudioStream->time_base);
					av_interleaved_write_frame(m_pFormateContext, m_pAVPacket);
				}
			}
		}

		return  _nDataSizeOut;
	}

	int CAyjAuidoEncoder::EncoderRefresh()
	{
		if (nullptr == m_pAVContext || nullptr == m_pAVPacket)
			return -1;
		
		int nRet = avcodec_send_frame(m_pAVContext, nullptr);

		if (nRet < 0)
		{
			return -1; std::make_tuple(-1, nullptr, 0, 0, false);
		}

		std::vector<std::tuple<int, unsigned char*, bool>> vectOut;

		while (nRet >= 0)
		{
			av_packet_unref(m_pAVPacket);
			nRet = avcodec_receive_packet(m_pAVContext, m_pAVPacket);
			if (nRet == AVERROR(EAGAIN) || nRet == AVERROR_EOF)
				break;
			else if (nRet < 0)
			{
				WriteLogEx(ModuleVideo, LogError, "Error during encoding");
				break;
			}
			
			m_llEncodePts += m_pAVFrame->nb_samples;

			if (m_pFormateContext)
			{
				m_pAVPacket->stream_index = 0;//音频流 0
				m_pAVPacket->pts = m_llEncodePts;
				m_pAVPacket->dts = m_llEncodePts;
				av_interleaved_write_frame(m_pFormateContext, m_pAVPacket);
			}
			//if (m_pPreMain)
			//	m_pPreMain->VideoWriteToFile(m_pAVPacket->data, m_pAVPacket->size, m_nVideoW, m_nVideoH, m_llEncodePts, m_pAVPacket->pts, 0, bIFrame);
		}
		if(m_pFormateContext)
			av_write_trailer(m_pFormateContext);

		return 0;
	}

	long long CAyjAuidoEncoder::EncoderGetWritePts()
	{
		return m_llEncodePts;
	}

	int CAyjAuidoEncoder::EncoderClose(void)
	{
		CAutoLock tempLock(&m_lockApi);

		EncoderRefresh();

		if (m_bOpen) {
			avcodec_close(m_pAVContext);
			m_bOpen = false;
		}
		if (m_pAVCodec)
		{
			m_pAVCodec = nullptr;
		}

		if (m_pAVContext != nullptr) {
			avcodec_free_context(&m_pAVContext);
			m_pAVContext = nullptr;
		}

		if (m_pAVFrame != nullptr) {
			av_frame_free(&m_pAVFrame);
			m_pAVFrame = nullptr;
		}
		if (m_pFrameTemp != nullptr) {
			av_frame_free(&m_pFrameTemp);
			m_pFrameTemp = nullptr;
		}

		if (m_pAVPacket != nullptr) {
			av_packet_free(&m_pAVPacket);
			m_pAVPacket = nullptr;
		}
		if (m_pAudioConvertCtx)
		{
			swr_free(&m_pAudioConvertCtx);
			m_pAudioConvertCtx = nullptr;
		}
		if (m_pFormateContext)
		{
			//关闭打开的文件io流
			avio_close(m_pFormateContext->pb);

			avformat_free_context(m_pFormateContext);
			m_pFormateContext = nullptr;
		}
		m_bOpen = false;

		m_pAVCodec = nullptr;
		
		return 0;
	}

	int CAyjAuidoEncoder::InitFilters(const char* _szFiltersDescr)
	{
		char args[512] = {};
		int ret = 0;
		/*const AVFilter *buffersrc = avfilter_get_by_name("buffer");
		const AVFilter *buffersink = avfilter_get_by_name("buffersink");
		AVFilterInOut *outputs = avfilter_inout_alloc();
		AVFilterInOut *inputs = avfilter_inout_alloc();

		enum AVPixelFormat pix_fmts[] = { AV_PIX_FMT_YUV420P, AV_PIX_FMT_NONE };

		m_pFilterGraph = avfilter_graph_alloc();
		if (!outputs || !inputs || !m_pFilterGraph) {
			ret = AVERROR(ENOMEM);
			goto end;
		}

		snprintf(args, sizeof(args),
			"video_size=%dx%d:pix_fmt=%d:time_base=%d/%d:pixel_aspect=%d/%d",
			m_iPicW, m_iPicH, AV_PIX_FMT_YUV420P,
			m_pAVContext->time_base.num, m_pAVContext->time_base.den,
			m_pAVContext->time_base.num, m_pAVContext->time_base.den);

		ret = avfilter_graph_create_filter(&buffersrc_ctx, buffersrc, "in",
			args, NULL, m_pFilterGraph);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer source\n");
			goto end;
		}

		ret = avfilter_graph_create_filter(&buffersink_ctx, buffersink, "out",
			NULL, NULL, m_pFilterGraph);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer sink\n");
			goto end;
		}

		ret = av_opt_set_int_list(buffersink_ctx, "pix_fmts", pix_fmts,
			AV_PIX_FMT_NONE, AV_OPT_SEARCH_CHILDREN);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot set output pixel format\n");
			goto end;
		}


		outputs->name = av_strdup("in");
		outputs->filter_ctx = buffersrc_ctx;
		outputs->pad_idx = 0;
		outputs->next = NULL;


		inputs->name = av_strdup("out");
		inputs->filter_ctx = buffersink_ctx;
		inputs->pad_idx = 0;
		inputs->next = NULL;

		if ((ret = avfilter_graph_parse_ptr(m_pFilterGraph, _szFiltersDescr,
			&inputs, &outputs, NULL)) < 0)
			goto end;

		if ((ret = avfilter_graph_config(m_pFilterGraph, NULL)) < 0)
			goto end;

	end:
		avfilter_inout_free(&inputs);
		avfilter_inout_free(&outputs);
		*/
		return ret;
	}

} // namespace LPM
