/*
 * Filename:  DequeQueue.hpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/16/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
*/
#ifndef __DEQUE_QUEUE_H__
#define __DEQUE_QUEUE_H__

#include <buffer/IQueue.hpp>
#include <deque>

template <typename T>
class DequeQueue : public IQueue<T>
{
public:
    typedef T value_type;
    typedef void(*MemoryReleaseHook)(value_type & obj);
public:
    DequeQueue(bool lock, bool wait, MemoryReleaseHook hook);
    virtual ~DequeQueue(void);
    virtual void Start(void);
    virtual bool Push(T & t);
    virtual bool PushFront(T & t);
    virtual bool PopFront(void);
    virtual bool Empty(void);
    virtual size_t Size(void);
    virtual bool Pick(T & t);
    virtual bool Peek(T & t);
    virtual void Stop(void);
    virtual void Clear(void);

private:
    void _Clear(void);

private:
    std::deque<T> m_queue;
    MemoryReleaseHook m_hook;
    bool m_stop;
};

template <typename T>
DequeQueue<T>::DequeQueue(bool lock, bool wait, MemoryReleaseHook hook)
    : IQueue<T>(lock, wait)
    , m_stop(false)
    , m_hook(hook)
{
}

template <typename T>
DequeQueue<T>::~DequeQueue(void)
{
    Stop();
}

template <typename T>
inline void DequeQueue<T>::Start(void)
{
    IQueue<T>::Lock();
    m_stop = false;
    IQueue<T>::Unlock();
}

template <typename T>
inline bool DequeQueue<T>::Push(T & t)
{
    IQueue<T>::Lock();
    if (m_stop) {
        IQueue<T>::Unlock();
        return false;
    }
    m_queue.push_back(t);
    IQueue<T>::Notify();
    IQueue<T>::Unlock();

    return true;
}

template<typename T>
inline bool DequeQueue<T>::PushFront(T & t)
{
    IQueue<T>::Lock();
    if (m_stop) {
        IQueue<T>::Unlock();
        return false;
    }
    m_queue.push_front(t);
    IQueue<T>::Notify();
    IQueue<T>::Unlock();

    return true;
}

template<typename T>
inline bool DequeQueue<T>::PopFront(void)
{
    IQueue<T>::Lock();
    if (m_stop) {
        IQueue<T>::Unlock();
        return false;
    }
    m_queue.pop_front();
    IQueue<T>::Notify();
    IQueue<T>::Unlock();

    return true;
}
template <typename T>
inline bool DequeQueue<T>::Empty(void)
{
    bool empty = true;

    IQueue<T>::Lock();
    empty = m_queue.empty();
    IQueue<T>::Unlock();

    return empty;
}

template <typename T>
inline size_t DequeQueue<T>::Size(void)
{
    size_t size = 0;

    IQueue<T>::Lock();
    size = m_queue.size();
    IQueue<T>::Unlock();

    return size;
}

template <typename T>
inline bool DequeQueue<T>::Pick(T & t)
{
    IQueue<T>::Lock();

    if (m_stop) {
        IQueue<T>::Unlock();
        return false;
    }

    while (m_queue.empty() && IQueue<T>::Wait()) {
        if (m_stop) {
            IQueue<T>::Unlock();
            return false;
        }
    }

    if (m_queue.empty()) {
        IQueue<T>::Unlock();
        return false;
    }

    t = m_queue.front();
    m_queue.pop_front();

    IQueue<T>::Unlock();

    return true;
}

template<typename T>
inline bool DequeQueue<T>::Peek(T & t)
{
    IQueue<T>::Lock();

    if (m_stop) {
        IQueue<T>::Unlock();
        return false;
    }

    while (m_queue.empty() && IQueue<T>::Wait()) {
        if (m_stop) {
            IQueue<T>::Unlock();
            return false;
        }
    }

    if (m_queue.empty()) {
        IQueue<T>::Unlock();
        return false;
    }

    t = m_queue.front();

    IQueue<T>::Unlock();

    return true;
}

template <typename T>
inline void DequeQueue<T>::Stop(void)
{
    IQueue<T>::Lock();
    m_stop = true;
    _Clear();
    IQueue<T>::NotifyAll();
    IQueue<T>::Unlock();
}

template<typename T>
inline void DequeQueue<T>::Clear(void)
{
    IQueue<T>::Lock();
    _Clear();
    IQueue<T>::NotifyAll();
    IQueue<T>::Unlock();
}

template <typename T>
inline void DequeQueue<T>::_Clear(void)
{
    size_t size = m_queue.size();
    for (size_t i = 0; i < size; i++) {
        if (m_hook != NULL) {
            m_hook(m_queue[i]);
        }
    }
    m_queue.clear();
}
#endif
