﻿ /*
 ******		FileName	:	IAYJWriteVideo.h
 ******		Describe	:	此文件是c++ 接口,主要负责视频的文件写入
 ******		Date		:	2021-09-07
 ******		Author		:	TangJinFei
 ******		Copyright	:	Guangzhou AiYunJi Inc.
 ******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
 */
#ifndef __I_AYJ_MP4_WRITE_H__
#define __I_AYJ_MP4_WRITE_H__

#include "common/typedefs.h"


namespace AYJ 
{
    class LPM_VIDEO_API IAyjMp4Writer
    {
   
	public:
		virtual ~IAyjMp4Writer(void) {}

		virtual int Mp4WriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps,  bool bFlagH264 = true) = 0;

		virtual int Mp4Write(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) = 0;

		virtual int Mp4WriteClose(void) = 0;

		virtual long long Mp4WriteGetTimes(void) = 0;

		static IAyjMp4Writer* Create();
		static void Release(IAyjMp4Writer*& _pObj);
    };
}// namespace LPM
#endif // !__I_AYJ_WRITECIDEO_H__
