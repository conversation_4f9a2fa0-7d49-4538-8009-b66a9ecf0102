﻿/*
 * Filename:  ImageUtils.h
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/19/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef __IMAGEUTILS_H__
#define __IMAGEUTILS_H__

#include <common/typedefs.h>
#include <libyuv/libyuv.h>

 // "fmode" determines the quality of scale
CORE_EXPORT
int I420Scale(const uint8_t* srcI420, int srcw, int srch,
    uint8_t* dstI420, int dstw, int dsth,
    libyuv::FilterModeEnum fmode = libyuv::kFilterNone);

CORE_EXPORT
int I420ToRGBA(const uint8_t* srcI420, uint8_t* dstARGB, int w, int h);

CORE_EXPORT
int I420ToARGB(const uint8_t* srcI420, uint8_t* dstARGB, int w, int h);

CORE_EXPORT
int BGRAToI420(const uint8_t* srgRGBA, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int ARGBToI420(const uint8_t* srcARGB, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int RGBAToI420(const uint8_t* srcARGB, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int ABGRToI420(const uint8_t* srcARGB, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int RGB24ToI420(const uint8_t* srcRGB24, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int NV21ToI420(const uint8_t* srcNV21, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int NV12ToI420(const uint8_t* srcNV12, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int YUY2ToI420(const uint8_t* srcYUY2, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int YUYVToI420(const uint8_t* srcYUYV, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int I420ToNV12(const uint8_t* srcI420, uint8_t* dstNV12, int w, int h);

CORE_EXPORT
int I420Copy(const uint8_t* srcI420, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int I420Mirror(const uint8_t* srcI420, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int YV12ToI420(const uint8_t* srcYV12, uint8_t* dstI420, int w, int h);

CORE_EXPORT
int I420ToRGB24(const uint8_t* srcI420, uint8_t* dstRGB24, int w, int h);

CORE_EXPORT
int I420RotatePlane(const uint8_t* srcI420, uint8_t* dstI420,
    int w, int h, libyuv::RotationMode mode);

CORE_EXPORT
int I420Rotate(const uint8_t* srcI420, uint8_t* dstI420,
    int w, int h, libyuv::RotationMode mode);

CORE_EXPORT
int RGB24ToBGR24(uint8_t* srcRGB, int w, int h);

CORE_EXPORT
int I420Crop(uint8_t * src, uint32_t srcSize,
    uint32_t srcW, uint32_t srcH,
    uint8_t * dst, uint32_t dstSize,
    uint32_t dstW, uint32_t dstH,
    uint32_t x, uint32_t y, libyuv::FourCC cc = libyuv::FOURCC_I420);

CORE_EXPORT
int CalcSizeToScale(uint32_t & w, uint32_t & h, 
    double srcScale, double curScale, uint32_t dstw, uint32_t dsth);

CORE_EXPORT
int FillBlackBorder(uint8_t *src, uint32_t srcw, uint32_t srch,
    uint8_t * dst, uint32_t dstw, uint32_t dsth);

#endif
