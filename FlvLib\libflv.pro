QT -= gui

TEMPLATE = lib

CONFIG += staticlib

HEADERS += \
    amf0.h \
    amf3.h \
    flv-demuxer.h \
    flv-muxer.h \
    flv-parser.h \
    flv-proto.h \
    flv-reader.h \
    flv-writer.h \
    mp3-header.h \
    mpeg4-aac.h \
    mpeg4-avc.h \
    mpeg4-bits.h \
    mpeg4-hevc.h

SOURCES += \
    amf0.c \
    amf3.c \
    flv-demuxer.c \
    flv-demuxer-script.c \
    flv-muxer.c \
    flv-parser.c \
    flv-reader.c \
    flv-writer.c \
    hevc-annexbtomp4.c \
    hevc-mp4toannexb.c \
    mp3-header.c \
    mpeg4-aac.c \
    mpeg4-aac-asc.c \
    mpeg4-annexbtomp4.c \
    mpeg4-avc.c \
    mpeg4-hevc.c \
    mpeg4-mp4toannexb.c

CONFIG(release, debug|release) {
    DESTDIR += $$PWD/../../../out/$$(Platform)/Release/
    OBJECTS_DIR += $$PWD/out/$$(Platform)/Release/
    MOC_DIR += $$PWD/out/$$(Platform)/Release/
} else {
    DESTDIR += $$PWD/../../../out/$$(Platform)/Debug/
    OBJECTS_DIR += $$PWD/out/$$(Platform)/Debug/
    MOC_DIR += $$PWD/out/$$(Platform)/Debug/
}
