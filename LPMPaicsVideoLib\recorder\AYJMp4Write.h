/*
 * Filename:  AYJMp4Write.h
 * Project :  LPMPaicsVideoLib
 * Created by <PERSON><PERSON> on 7/2/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 *
 * Description: MP4视频文件写入器类定义
 *              基于FFmpeg库实现H264视频编码和MP4封装
 *              支持多种像素格式输入，自动转换为YUV420P进行编码
 *              具备线程安全机制和性能优化
 */
#ifndef __AYJ_MP4_WRITE_H__
#define __AYJ_MP4_WRITE_H__

#include "IAYJMp4Writer.h"
#include "../Depends/Common/include/LPMCommonLib.h"

extern "C"
{
#include "libavformat/avformat.h"
#include "libswscale/swscale.h"
#include "libavutil/opt.h"
#include "libswresample/swresample.h"
#include "libavutil/imgutils.h"
}

#include <fstream>

using namespace LPM;

namespace AYJ
{
    /**
     * @class CAyjMp4Writer
     * @brief MP4视频文件写入器实现类
     *
     * 功能特性：
     * 1. 支持H264视频编码
     * 2. 支持多种输入像素格式（RGB24/32, BGR24/32, YUV420P等）
     * 3. 自动像素格式转换和尺寸缩放
     * 4. 线程安全的写入操作
     * 5. 内存泄漏防护和资源自动管理
     * 6. 性能优化的编码流程
     */
    class CAyjMp4Writer : public IAyjMp4Writer
    {
    public:
        CAyjMp4Writer();
        ~CAyjMp4Writer();

        // Mp4WriteInit 初始化MP4写入器
        virtual int Mp4WriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264 = true);

        // Mp4Write 写入视频帧数据
        virtual int Mp4Write(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts);

        // Mp4WriteClose 关闭写入器并释放资源
        virtual int Mp4WriteClose(void);

        // Mp4WriteGetTimes 获取当前写入时间戳
        virtual long long Mp4WriteGetTimes(void);

    private:
        // WriteHead 写入MP4文件头信息
        bool WriteHead();

        // WriteEnd 写入MP4文件尾部并刷新缓冲区
        bool WriteEnd();

        // WriteFrame 写入编码后的视频帧
        bool WriteFrame(AVPacket* _pPkt);

        // AddVideoStream 添加视频流到输出上下文
        int AddVideoStream(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264 = true);

        // InitSws 初始化像素格式转换上下文
        int InitSws(int _nVideoFormat, int _nVideoInW, int _nVideoInH, int _nVideoOutW, int _nVideoOutH);

        // InitInData 初始化输入数据帧
        int InitInData(unsigned char* _ucData, int _nVideoFormat, int _nVideoInW, int _nVideoInH);

    private:
        // ========== FFmpeg核心组件 ==========
        AVFormatContext *m_pFormatCnt = nullptr;    // MP4封装输出上下文
        AVCodecContext  *m_pCodecCnt = nullptr;     // H264视频编码器上下文
        AVStream        *m_pVideoStream = nullptr;  // 视频流对象
        SwsContext      *m_pSwsCnt = nullptr;       // 像素格式转换上下文

        // ========== 数据帧和数据包 ==========
        AVFrame         *m_pFrameYuv = nullptr;     // YUV420P输出帧
        AVFrame         *m_pFrameIn = nullptr;      // 输入数据帧
        AVPacket        *m_pPacketEncode = nullptr; // 编码输出数据包

        // ========== 配置参数 ==========
        std::string m_strMp4Path;                   // 视频保存路径（UTF-8格式）
        bool m_bH264 = true;                        // 是否使用H264编码
        int  m_nGop = 0;                           // GOP大小（关键帧间隔）
        float m_fFps = 0.0;                        // 帧率

        // ========== 视频参数 ==========
        int m_nVideoFormatIn = 0;                  // 输入像素格式
        int m_nVideoOutW = 1920;                   // 输出视频宽度
        int m_nVideoOutH = 1080;                   // 输出视频高度
        int m_nVideoInW = 0;                       // 输入视频宽度
        int m_nVideoInH = 0;                       // 输入视频高度

        // ========== 状态控制 ==========
        long long m_llWritePts = 0;                // 写入时间戳计数器
        CLock m_lockWrite;                         // 线程安全锁
        bool m_bIsInit = false;                    // 初始化状态标志
    };
} // namespace AYJ
#endif // !__AYJ_MP4_WRITE_H__
