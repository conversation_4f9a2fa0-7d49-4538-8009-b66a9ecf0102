/*
 * Filename:  RecorderFlv.cpp
 * Project :  LMPCore * Created by <PERSON><PERSON> on 7/2/2019. 
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef __AYJ_MP4_WRITE_H__
#define __AYJ_MP4_WRITE_H__

#include "IAYJMp4Writer.h"

#include "../Depends/Common/include/LPMCommonLib.h"

extern "C"
{

#include "libavformat/avformat.h"
#include "libswscale/swscale.h"
#include "libavutil/opt.h"
#include "libswresample/swresample.h"
#include "libavutil/imgutils.h"

}

#include <fstream>

using namespace LPM;

namespace AYJ
{
    class CAyjMp4Writer : public IAyjMp4Writer
        
    {
    public:
		CAyjMp4Writer();
        ~CAyjMp4Writer();

		virtual int Mp4WriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264 = true) ;

		virtual int Mp4Write(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) ;

		virtual int Mp4WriteClose(void);

		virtual long long Mp4WriteGetTimes(void);

	private:
		bool WriteHead();
		bool WriteEnd();
		bool WriteFrame(AVPacket* _pPkt);

		int AddVideoStream(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool bFlagH264 = true);

		int InitSws(int _nVideoFormat,  int _nVideoInW, int _nVideoInH , int _nVideoOutW, int _nVideoOutH);
		int InitInData(unsigned char* _ucData, int _nVideoFormat, int _nVideoInW, int _nVideoInH);

    private:

		AVFormatContext *m_pFormatCnt = nullptr;	//封装mp4输出上下文
		AVCodecContext	*m_pCodecCnt = nullptr;		//视频编码器上下文
		//AVCodecContext *ac = nullptr;	//音频编码器上下文
		AVStream		*m_pVideoStream = nullptr;		//视频流
		SwsContext		*m_pSwsCnt = nullptr;		//像素转换的上下文

		AVFrame			*m_pFrameYuv = nullptr;		//输出yuv
		AVFrame			*m_pFrameIn = nullptr;		//输出yuv

		AVPacket		*m_pPacketEncode = nullptr;

		//	视频保存路径 utf-8 格式
		std::string m_strMp4Path;

		bool m_bH264 = true;
		int  m_nGop = 0;
		float m_fFps = 0.0;

		int m_nVideoFormatIn = 0;
		int m_nVideoOutW = 1920;
		int m_nVideoOutH = 1080;
		int m_nVideoInW = 0;
		int m_nVideoInH = 0;

		long long m_llWritePts = 0;
		CLock m_lockWrite;

		bool m_bIsInit = false;
       
    };
} // namespace LPM
#endif // !__RECORDER_FLV_H__
