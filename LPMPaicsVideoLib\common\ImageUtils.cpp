﻿/*
 * Filename:  ImageUtils.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 4/19/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */
#include <common/ImageUtils.h>
#include <string.h>

int I420Scale(const uint8_t* srcI420, int srcw, int srch,
    uint8_t* dstI420, int dstw, int dsth,
    libyuv::FilterModeEnum fmode)
{
    if (srcI420 == NULL || srcw <= 0 || srch <= 0 ||
        dstI420 == NULL || dstw <= 0 || dsth <= 0) {
        return -1;
    }
    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + srcw * srch;
    const uint8_t * psrcv = psrcu + (srcw*srch >> 2);

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + dstw * dsth;
    uint8_t * pdstv = pdstu + (dstw*dsth >> 2);

    int dst_y = dstw;
    int dst_u = dstw >> 1;
    int dst_v = dst_u;

    int src_y = srcw;
    int src_u = srcw >> 1;
    int src_v = src_u;

    return libyuv::I420Scale(psrcy, src_y, psrcu, src_u, psrcv, src_v, srcw, srch,
        pdsty, dst_y, pdstu, dst_u, pdstv, dst_v, dstw, dsth, fmode);

}

int I420ToRGBA(const uint8_t* srcI420, uint8_t* dstRGBA, int w, int h)
{
    if (srcI420 == NULL ||
        dstRGBA == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420ToRGBA(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v, dstRGBA, w * 4, w, h);
}

int I420ToARGB(const uint8_t* srcI420, uint8_t* dstARGB, int w, int h)
{
    if (srcI420 == NULL ||
        dstARGB == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420ToARGB(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v, dstARGB, w * 4, w, h);
}


int I420ToRGB24(const uint8_t* srcI420, uint8_t* dstRGB24, int w, int h)
{
    if (srcI420 == NULL ||
        dstRGB24 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420ToRGB24(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v, dstRGB24, w * 3, w, h);
}

int I420RotatePlane(const uint8_t* srcI420, uint8_t* dstI420,
    int w, int h, libyuv::RotationMode mode)
{
    return libyuv::RotatePlane(srcI420, w / 4,
        dstI420, h / 4, w, h, mode);
}

int I420Rotate(const uint8_t * srcI420, uint8_t * dstI420,
    int w, int h, libyuv::RotationMode mode)
{
    if (srcI420 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int src_stride_y = w;
    int src_stride_u = w >> 1;
    int src_stride_v = w >> 1;

    int dst_stride_y = h;
    int dst_stride_u = h >> 1;
    int dst_stride_v = h >> 1;

    return libyuv::I420Rotate(psrcy, src_stride_y, psrcu, src_stride_u,
        psrcv, src_stride_v,
        pdsty, dst_stride_y, pdstu, dst_stride_u,
        pdstv, dst_stride_v,
        w, h, mode);
}

int RGB24ToBGR24(uint8_t * srcRGB, int w, int h)
{
    for (int j = 0; j < h; j++) {
        for (int i = 0; i < w; i++) {
            if (*srcRGB != *(srcRGB + 2)) {

                // swap R&B
                *srcRGB ^= *(srcRGB + 2);
                *(srcRGB + 2) ^= *srcRGB;
                *srcRGB ^= *(srcRGB + 2);
            }
            srcRGB += 3;
        }
    }
    return 0;
}


int RGB24ToI420(const uint8_t* srcRGB24, uint8_t* dstI420, int w, int h)
{
    if (srcRGB24 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::RGB24ToI420(srcRGB24, w * 3, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int NV21ToI420(const uint8_t* srcNV21, uint8_t* dstI420, int w, int h)
{
    if (srcNV21 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcNV21;
    const uint8_t * psrcuv = srcNV21 + w * h;

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;
    int stride_vu = w;

    return libyuv::NV21ToI420(psrcy, stride_y, psrcuv, stride_vu,
        pdsty, stride_y, pdstu, stride_u,
        pdstv, stride_v, w, h);
}

int NV12ToI420(const uint8_t* srcNV12, uint8_t* dstI420, int w, int h)
{
    if (srcNV12 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcNV12;
    const uint8_t * psrcuv = srcNV12 + w * h;

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;
    int stride_uv = w;

    return libyuv::NV12ToI420(psrcy, stride_y, psrcuv, stride_uv,
        pdsty, stride_y, pdstu, stride_u,
        pdstv, stride_v, w, h);
}

int YUY2ToI420(const uint8_t * srcYUY2, uint8_t * dstI420, int w, int h)
{
    if (srcYUY2 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcYUY2;
    const int src_stride_yuy2 = w;

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::YUY2ToI420(psrcy, src_stride_yuy2, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int YUYVToI420(const uint8_t * srcYUYV, uint8_t * dstI420, int w, int h)
{
    uint8_t *y = dstI420;
    uint8_t *u = dstI420 + w * h;
    uint8_t *v = dstI420 + w * h + w * h / 4;

    bool bIsU = 1;
    int i = 0, j = 0;
    int iBaseHeight = 0;
    int idxY = 0, idxU = 0, idxV = 0;
    int yuyvSize = 2 * w * h;

    // yuyv frame size is width*height*2
    // discard even rows u v
    for (i = 0; i < yuyvSize; i += 2) {
        *(y + idxY++) = *(srcYUYV + i);
    }
    for (i = 0; i < h; i += 2) {
        iBaseHeight = i * w * 2;
        for (j = iBaseHeight + 1; j < iBaseHeight + w * 2; j += 2) {
            if (bIsU) {
                *(u + idxU++) = *(srcYUYV + j);
                bIsU = false;
            } else {
                *(v + idxV++) = *(srcYUYV + j);
                bIsU = true;
            }
        }
    }
    return 0;
}

int I420ToNV12(const uint8_t* srcI420, uint8_t* dstNV12, int w, int h)
{
    if (srcI420 == NULL ||
        dstNV12 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);

    uint8_t * pdsty = dstNV12;
    uint8_t * pdstuv = pdsty + w * h;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;
    int stride_uv = w;

    return libyuv::I420ToNV12(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v,
        pdsty, stride_y, pdstuv, stride_uv,
        w, h);
}

int I420Copy(const uint8_t* srcI420, uint8_t* dstI420, int w, int h)
{
    if (srcI420 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420Copy(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v,
        pdsty, stride_y, pdstu, stride_u,
        pdstv, stride_v, w, h);
}

int I420Mirror(const uint8_t* srcI420, uint8_t* dstI420, int w, int h)
{
    if (srcI420 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcI420;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420Mirror(psrcy, stride_y, psrcu, stride_u,
        psrcv, stride_v,
        pdsty, stride_y, pdstu, stride_u,
        pdstv, stride_v, w, h);
}

int YV12ToI420(const uint8_t* srcYV12, uint8_t* dstI420, int w, int h)
{
    if (srcYV12 == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }

    const uint8_t * psrcy = srcYV12;
    const uint8_t * psrcu = psrcy + w * h;
    const uint8_t * psrcv = psrcu + (w*h >> 2);

    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::I420Copy(psrcy, stride_y, psrcv, stride_v,
        psrcu, stride_u,
        pdsty, stride_y, pdstu, stride_u,
        pdstv, stride_v, w, h);
}

int BGRAToI420(const uint8_t* srgRGBA, uint8_t* dstI420, int w, int h)
{
    if (srgRGBA == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::ARGBToI420(srgRGBA, w * 4, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int ABGRToI420(const uint8_t *srgABGR, uint8_t *dstI420, int w, int h)
{
    if (srgABGR == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::RGBAToI420(srgABGR, w * 4, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int  ARGBToI420(const uint8_t* srcARGB, uint8_t* dstI420, int w, int h)
{
    if (srcARGB == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::BGRAToI420(srcARGB, w * 4, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int RGBAToI420(const uint8_t* srcARGB, uint8_t* dstI420, int w, int h)
{
    if (srcARGB == NULL ||
        dstI420 == NULL ||
        w <= 0 || h <= 0) {
        return -1;
    }
    uint8_t * pdsty = dstI420;
    uint8_t * pdstu = pdsty + w * h;
    uint8_t * pdstv = pdstu + (w*h >> 2);;

    int stride_y = w;
    int stride_u = w >> 1;
    int stride_v = w >> 1;

    return libyuv::ABGRToI420(srcARGB, w * 4, pdsty, stride_y,
        pdstu, stride_u, pdstv, stride_v, w, h);
}

int I420Crop(uint8_t * src, uint32_t srcSize, uint32_t srcW, uint32_t srcH,
    uint8_t * dst, uint32_t dstSize, uint32_t dstW, uint32_t dstH,
    uint32_t x, uint32_t y, libyuv::FourCC cc)
{
    if (x + dstW > srcW || y + dstH > srcH) {
        return -1;
    }

    if (x % 2 != 0 || y % 2 != 0) {
        return -1;
    }

    int dstYSize = dstW * dstH;
    int dstUSize = dstYSize >> 2;
    uint8_t * dstY = dst;
    uint8_t * dstU = dstY + dstYSize;
    uint8_t * dstV = dstU + dstUSize;

    return libyuv::ConvertToI420(src, srcW*srcH * 3 >> 1,
        dstY, dstW, dstU, dstW >> 1, dstV, dstW >> 1,
        x, y, srcW, srcH, dstW, dstH, libyuv::kRotate0, cc);
}

int CalcSizeToScale(uint32_t & w, uint32_t & h, double srcScale, double curScale, uint32_t dstw, uint32_t dsth)
{
    //if (srcScale * PROPORTION_OF_PRECISION == curScale * PROPORTION_OF_PRECISION) {
    //    return 0;
    //}
    if (srcScale < curScale) {
        uint32_t width = (uint32_t)(w * srcScale / curScale);
        w = width - width % 2;
    } else {
        uint32_t height = (uint32_t)(h * curScale / srcScale);
        h = height - height % 2;
    }

    double scaleFactorOfWidth = (double)dstw / w;
    double scaleFactorOfHeight = (double)dsth / h;
    if (scaleFactorOfWidth * h > dsth) {
        h = dsth;
        w = (uint32_t)(w * scaleFactorOfHeight);
        w = w - w % 2;
    } else {
        w = dstw;
        h = (uint32_t)(h * scaleFactorOfWidth);
        h = h - h % 2;
    }
    return 0;
}

int FillBlackBorder(uint8_t * src, uint32_t srcw, uint32_t srch, 
    uint8_t * dst, uint32_t dstw, uint32_t dsth)
{
    if (src == nullptr || dst == nullptr
        || (srcw != dstw && srch != dsth)) {
        return -1;
    }
    uint32_t fill = 0;
    bool isHorizontalFill = srch == dsth;
    if (isHorizontalFill) {
        fill = (dstw - srcw) / 2;
    } else {
        fill = (dsth - srch) / 2;
    }
    uint8_t * srcy = src;
    uint8_t * srcu = src + srcw * srch;
    uint8_t * srcv = srcu + srcw * srch / 4;
    uint8_t * dsty = dst;
    uint8_t * dstu = dst + dstw * dsth;
    uint8_t * dstv = dstu + dstw * dsth / 4;
    // Y
    for (uint32_t i = 0; i < dsth; ++i) {
        for (uint32_t j = 0; j < dstw; ++j) {
            uint32_t scanning = i;
            uint32_t boundary = dsth - fill;
            if (isHorizontalFill) {
                boundary = dstw - fill;
                scanning = j;
            }
            if ((scanning >= 0 && scanning < fill) || scanning >= boundary) {
                dsty[i * dstw + j] = 0;
            } else {
                dsty[i * dstw + j] = *srcy++;
            }
        }
    }
    // U,V
    for (uint32_t i = 0; i < dsth / 2; ++i) {
        for (uint32_t j = 0; j < dstw / 2; ++j) {
            uint32_t scanning = i;
            uint32_t boundary = dsth - fill;
            if (isHorizontalFill) {
                boundary = dstw - fill;
                scanning = j;
            }
            if ((scanning >= 0 && scanning < (fill / 2))  // L
                || (scanning >= boundary / 2)) {     // R
                dstu[i*dstw / 2 + j] = 0x80;
                dstv[i*dstw / 2 + j] = 0x80;
            } else {
                dstu[i*dstw / 2 + j] = *srcu++;
                dstv[i*dstw / 2 + j] = *srcv++;
            }
        }
    }
    return 0;
}
