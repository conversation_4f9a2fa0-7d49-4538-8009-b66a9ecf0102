﻿ /*
 ******		FileName	:	IAYJWriteVideo.h
 ******		Describe	:	此文件是c++ 接口,主要负责视频的文件写入
 ******		Date		:	2021-09-07
 ******		Author		:	TangJinFei
 ******		Copyright	:	Guangzhou AiYunJi Inc.
 ******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
 */
#ifndef __I_AYJ_WRITECIDEO_H__
#define __I_AYJ_WRITECIDEO_H__


#include "Common/Typedefs.h"

namespace AYJ 
{
	typedef int(__stdcall *OnWriterDataCb)(void* _pUserData,long long _nWritellPts, const char* _szVideoInfoData, int _nVideoInfoDataSize,const unsigned char* _ucVideoData, int _nDatacVideoSize,const char* _szVideoIndexData, int _nIndexDataSize);

    class LPM_VIDEO_API IAyjVideoWriter
    {
    public:
		virtual ~IAyjVideoWriter() { }

		virtual int VideoWriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool _bIsContine = false,  bool bFlagH264 = true) = 0;

		virtual int VideoWrite(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) = 0;

		virtual int VideoWriteClose(void) = 0;

		virtual long long VideoWriteGetTimes(void) = 0;

		virtual int VideoWriteGetFps() = 0;

		virtual int VideoWriteMoidfyFps(int _nFps) = 0;

		virtual int ClearData(bool _bDelete = false) = 0;

		static IAyjVideoWriter* Create(OnWriterDataCb _pVideoWriterDataFun, void* _pUserData);
		static void Release(IAyjVideoWriter*& _pObj);

    };
} // namespace LPM
#endif // !__I_AYJ_WRITECIDEO_H__
