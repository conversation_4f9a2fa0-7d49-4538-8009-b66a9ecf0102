﻿/*
 * Filename:  RecorderFlv.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 7/2/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */

#include "AYJVideoWrite.h"
#include <fstream>

#include <experimental/filesystem>

namespace AYJ 
{
	CAyjVideoWriter::CAyjVideoWriter(OnWriterDataCb _pVideoWriterDataFun, void* _pUserData, bool _bNeedLocalSave)
	{
		m_pVideoWriterDataFun = _pVideoWriterDataFun;
		m_pUserData = _pUserData;
		m_bNeedLocalSave = _bNeedLocalSave;

		m_pObjEncoder = new CAyjVideoEncoder(this);
	}
	CAyjVideoWriter::~CAyjVideoWriter()
	{
		VideoWriteClose();

		m_pVideoWriterDataFun = nullptr;
		m_pUserData = nullptr;
	}

	int CAyjVideoWriter::VideoWriteInit(const char* _szSavePath, int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, bool _bIsContine, bool _bFlagH264)
	{
		CAutoLock lockTemp(&m_lockWrite);

		long long llWritePtsStart = 0;
		int nWriteIndexStart = 0;
		//	续接	获取视频长度
		if (_bIsContine)
		{
			std::wstring wstrVideoIndex = U2W(_szSavePath) + std::wstring(L".video_index");
			std::ifstream fVideoReaderIndex;
			fVideoReaderIndex.open(wstrVideoIndex, std::ios::binary | std::ios::in);
			if (!fVideoReaderIndex.is_open())
			{
				WriteLogEx(ModuleVideo, LogError, u8"VideoReaderInit open file error :%s", _szSavePath);
				return -1;
			}
			while (fVideoReaderIndex.peek() != EOF)
			{
				tagVideoDataIndex tagIndex;
				int nSize = sizeof(tagIndex);
				const int  nReadSize = (int)static_cast<std::size_t>(fVideoReaderIndex.rdbuf()->sgetn(reinterpret_cast<char*>(&tagIndex), nSize));
				if (nSize != nReadSize)
					break;

				nWriteIndexStart++;
				llWritePtsStart = tagIndex.llPts;
			}

			fVideoReaderIndex.close();

			std::wstring wstrVideo = U2W(_szSavePath) + std::wstring(L".video");
			std::ifstream fVideoReader;
			tagVideoData tagVideo;
			int nSize = sizeof(tagVideo);
			fVideoReader.open(wstrVideo, std::ios::binary | std::ios::in);

			if (fVideoReader.is_open())
			{
				int const nReadSize = (int)static_cast<std::size_t>(fVideoReader.rdbuf()->sgetn(reinterpret_cast<char*>(&tagVideo), nSize));
				if (nSize != nReadSize)
				{
					m_fFps = tagVideo.fFps;
					m_nGop = tagVideo.nGop;
				}
			}
		}

		int nRet = m_pObjEncoder->EncoderInit(_nVdieoW, _nVideoH, _nGopSize, _nBitrate, _fFps, llWritePtsStart, nWriteIndexStart, _bFlagH264);
		if (0 == nRet)
		{
			m_bH264 = _bFlagH264;
			m_wstrPath = U2W(_szSavePath);
			

			wstring strVideo = m_wstrPath + L".video";
			wstring strVideoIndex = m_wstrPath + L".video_index";

			if (m_bNeedLocalSave)	//	需要本地存储
			{
				//	续接
				if (_bIsContine)
				{
					m_fVideoWrite.open(strVideo, std::ios::binary | std::ios::app);
					m_fVideoDataWrite.open(strVideoIndex, std::ios::binary | std::ios::app);

					size_t tt = m_fVideoWrite.tellp();
					m_fVideoWrite.seekp(0, std::ios::end);
					tt = m_fVideoWrite.tellp();
					m_fVideoDataWrite.seekp(0, std::ios::end);

				}
				else
				{
					m_fVideoWrite.open(strVideo, std::ios::binary | std::ios::trunc);
					m_fVideoDataWrite.open(strVideoIndex, std::ios::binary | std::ios::trunc);

					m_fFps = _fFps;
					m_nGop = _nGopSize;
				}

				if (m_fVideoWrite.is_open() && m_fVideoDataWrite.is_open())
				{
					nRet = 0;
				}
				else
				{
					nRet = -2;
					WriteLogEx(ModuleVideo, LogError, "VideoWriteInit error, path:%s", _szSavePath);
				}
			}	
		}

		return nRet;
	}

	int CAyjVideoWriter::VideoWrite(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts)
	{
		CAutoLock lockTemp(&m_lockWrite);

		CRunTime objRunTime;
		int nDataSize = -1;
		if (m_fVideoWrite.is_open() && m_fVideoDataWrite.is_open())
		{
			nDataSize = m_pObjEncoder->Encoder(_ucData, _nDataSize, _nFormate, _nW, _nH, _llPts);
		}

		return nDataSize;
	}

	void CAyjVideoWriter::OnEncoderDataNotify(unsigned char* _ucEncodeData, int _nDataSize, int _nW, int _nH, long long _llWritePts, long long _llFramePts, long long _llInPts, bool _bKeyFrame)
	{
		VideoWriteToFile(_ucEncodeData, _nDataSize, _nW, _nH, _llWritePts, _llFramePts, _llInPts, _bKeyFrame);
	}

	int CAyjVideoWriter::VideoWriteToFile(unsigned char* _ucEncodeData, int _nDataSize, int _nW, int _nH, long long _llWritePts, long long _llFramePts, long long _llInPts, bool _bKeyFrame)
	{
		CRunTime objRunTime;

		int nDataSize = -1;
		
		if (!_ucEncodeData || _nDataSize <=0)
			return -1;
		nDataSize = _nDataSize;

		if (nDataSize > 0)
		{
			//	写视频位置文件
			m_objVideoDataIndex.ClearData();
			m_objVideoDataIndex.llPts = _llWritePts;
			m_objVideoDataIndex.llDataIndex = m_fVideoWrite.tellp();
			m_objVideoDataIndex.bFrameKey = _bKeyFrame;
			if (m_fVideoDataWrite.is_open())
			{
				static_cast<std::size_t>(m_fVideoDataWrite.rdbuf()->sputn(reinterpret_cast<const char*>(&m_objVideoDataIndex), sizeof(m_objVideoDataIndex)));	//	索引数据
				//m_fVideoWrite.write((const char*)&m_objVideoDataIndex, sizeof(m_objVideoDataIndex));
				m_fVideoDataWrite.flush();
			}

			//	写视频文件
			//m_objVideoIndex.ClearData();
			m_objVideoInfo.bH264 = m_bH264;
			m_objVideoInfo.bKeyFrame = _bKeyFrame;
			m_objVideoInfo.llPts = _llWritePts;
			m_objVideoInfo.nVideoW = _nW;
			m_objVideoInfo.nVideoH = _nH;
			m_objVideoInfo.nDataLength = nDataSize;
			m_objVideoInfo.nGop = m_nGop;
			m_objVideoInfo.fFps = m_fFps;

			//	发送已编码的数据给外面
		//	if (m_pWriteCb)
		//		m_pWriteCb(m_pUserData, _llWritePts, (const char*)&m_objVideoIndex, sizeof(m_objVideoIndex),
			//		_ucEncodeData, nDataSize, (const char*)& m_objVideoDataIndex, sizeof(m_objVideoDataIndex));
			int nWriteSize = 0;
			if (m_fVideoWrite.is_open())
			{
				//	写视频文件
				nWriteSize = static_cast<std::size_t>(m_fVideoWrite.rdbuf()->sputn(reinterpret_cast<const char*>(&m_objVideoInfo), sizeof(m_objVideoInfo)));//	接下来一帧的视频信息
				nWriteSize = static_cast<std::size_t>(m_fVideoWrite.rdbuf()->sputn(reinterpret_cast<const char*>(_ucEncodeData), nDataSize));	//	编码后的视频数据
			}
				
			m_fVideoWrite.flush();

			if (m_pVideoWriterDataFun != nullptr)
				m_pVideoWriterDataFun(m_pUserData, _llWritePts, (const char*)&m_objVideoInfo, sizeof(m_objVideoInfo), _ucEncodeData, nDataSize, (const char*)&m_objVideoDataIndex, sizeof(m_objVideoDataIndex));

			if (nWriteSize != nDataSize)
			{
				nDataSize = -2;
				WriteLogEx(ModuleLogic, LogError, "VideoWrite write file error , nWriteSize != nDataSize");
			}
			else
			{
				bool bIsQ = _llWritePts == _llFramePts ? true : false;
				//WriteLogEx(ModuleLogic, LogDebug, "VideoWrite write pts:%lld, llFramePts:%lld,is==%d, InPts:%lld, index:%lld, dataSize:%d, h264:%d,bKeyFrame:%d, time:%0.3f", _llWritePts, _llFramePts, bIsQ,  _llInPts, m_objVideoDataIndex.llDataIndex, nDataSize, m_objVideoIndex.bH264, _bKeyFrame, objRunTime.GetRunTime() / 1000);
			}
			
		}

		return nDataSize;
	}

	int CAyjVideoWriter::VideoWriteClose(void)
	{
		CAutoLock lockTemp(&m_lockWrite);

		if(m_pObjEncoder)
			m_pObjEncoder->EncoderClose();

		if(m_fVideoWrite.is_open())
			m_fVideoWrite.close();
		if (m_fVideoDataWrite.is_open())
			m_fVideoDataWrite.close();
		
		return 0;
	}

	long long CAyjVideoWriter::VideoWriteGetTimes(void)
	{
		if(m_pObjEncoder)
			return m_pObjEncoder->EncoderGetWritePts();

		return 0;
	}

	int CAyjVideoWriter::ClearData(bool _bDelete )
	{
		std::wstring strVideo = m_wstrPath + L".video";
		std::wstring strVideoIndex = m_wstrPath + L".video_index";

		CAutoLock lockTemp(&m_lockWrite);

		m_fVideoWrite.close();
		m_fVideoDataWrite.close();
		if (_bDelete)
		{
			std::experimental::filesystem::remove(strVideo);
			std::experimental::filesystem::remove(strVideoIndex);
		}
		else 
		{
			m_fVideoWrite.open(strVideo, std::ios::binary | std::ios::trunc);
			m_fVideoDataWrite.open(strVideoIndex, std::ios::binary | std::ios::trunc);
		}

		return 0;
	}

	int CAyjVideoWriter::VideoWriteGetFps()
	{
		return (int)m_fFps;
	}

	int CAyjVideoWriter::VideoWriteMoidfyFps(int _nFps)
	{
		size_t nWriterIndex = m_fVideoWrite.tellp();
		if (nWriterIndex > 0)
			return -1;
		m_fFps = _nFps;
		if (m_pObjEncoder)
			m_pObjEncoder->EncoderMoidfyFps(_nFps);

		return 0;
	}
	IAyjVideoWriter* IAyjVideoWriter::Create(OnWriterDataCb _pVideoWriterDataFun, void* _pUserData)
	{
		IAyjVideoWriter* pInstance = new CAyjVideoWriter(_pVideoWriterDataFun, _pUserData);

		return pInstance;
	}
	void IAyjVideoWriter::Release(IAyjVideoWriter*& _pObj)
	{
		if (_pObj)
		{
			CAyjVideoWriter* pInstance = (CAyjVideoWriter*)_pObj;
			delete pInstance;
			_pObj = nullptr;
		}
	}
    
}