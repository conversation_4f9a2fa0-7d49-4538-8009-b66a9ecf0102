﻿#pragma once

/*
******		FileName	:	AiYunjiSdk.h
******		Describe	:	此文件是c++ 接口
******		Date		:	2021-01-25
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位 
*/



#ifdef LPMCOMMONLIB_EXPORTS
#define LPM_PUBLIC_API  __declspec(dllexport)
#else
#define LPM_PUBLIC_API  __declspec(dllimport)
#endif



#define GetVarName(x) #x

#include "LPMPublic.h"
#include "LPMLog.h"
#include "LPMThread.h"
#include "LPMSysTools.h"







