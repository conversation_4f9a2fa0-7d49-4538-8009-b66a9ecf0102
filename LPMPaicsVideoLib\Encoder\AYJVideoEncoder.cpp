﻿/*
 * Filename:  CAyjVideoEncoder.cpp
 * Project :  LMPCore
 * Created by <PERSON><PERSON> on 5/16/2019.
 * Copyright © 2019 Guangzhou AiYunJi Inc. All rights reserved.
 */

#include "AYJVideoEncoder.h"
#include "../common/typedefs.h"

#include <fstream>
#include "../recorder/AYJVideoWrite.h"

#include "../ImageScale/ImageScale.h"

namespace AYJ
{
	CAyjVideoEncoder::CAyjVideoEncoder(IOVideoEncoderNotify* _pEncodeNotify)
        : m_bOpen(false), m_nVideoW(0) , m_nVideoH(0)
    {
		m_pEncodeNotify =  _pEncodeNotify;
    }

	CAyjVideoEncoder::~CAyjVideoEncoder()
    {
		EncoderClose();
    }

	int CAyjVideoEncoder::EncoderInit(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, long long _llStartPts, int _nIndexStart, bool _bFlagH264)
	{
		int nRet = -1;
		if (m_bOpen)
		{
			WriteLogEx(ModuleVideo, LogInfo, "Encodeer has opened, Video:%dx%d, bitrate:%d, gopsize:%d , fps:%f", m_nVideoW, m_nVideoH, m_nBitrate, m_nGopSize, m_nFPs);
			return 1;
		}
		CAutoLock tempLock(&m_lockApi);

		m_nVideoW = _nVdieoW;
		m_nVideoH = _nVideoH;
		m_nBitrate = _nBitrate;
		m_nGopSize = _nGopSize;
		m_nFPs = (int)_fFps;
		m_bH264 = _bFlagH264;

		m_llEncodePts = _llStartPts;
		m_nEncodeIndex = _nIndexStart;

		if (_bFlagH264)
		{
			m_pAVCodec = avcodec_find_encoder(AV_CODEC_ID_H264);
		}else
		{
			m_pAVCodec = avcodec_find_encoder_by_name("hevc_nvenc");
			//m_pAVCodec = avcodec_find_encoder(AV_CODEC_ID_H265);
		}

		if (!m_pAVCodec)
		{
			WriteLogEx(ModuleVideo, LogError, "EncoderInit Codec not found");
			return nRet;
		}

		m_pAVContext = avcodec_alloc_context3(m_pAVCodec);
		if (!m_pAVContext)
		{
			WriteLogEx(ModuleVideo, LogError, "Could not allocate video codec context");
			return nRet;
		}

		m_pAVContext->bit_rate = m_nBitrate;
		m_pAVContext->width = m_nVideoW;
		m_pAVContext->height = m_nVideoH;
		m_pAVContext->time_base.num = 1;
		m_pAVContext->time_base.den = m_nFPs;
		m_pAVContext->gop_size = m_nGopSize;
		m_pAVContext->pix_fmt = AV_PIX_FMT_YUV420P;//AV_PIX_FMT_CUDA;
		m_pAVContext->max_b_frames = 0;
		m_pAVContext->rc_max_rate = m_nBitrate;
		//m_pAVContext->rc_min_rate = m_nBitrate / 2;
		m_pAVContext->rc_buffer_size = 0;
		//m_pAVContext->bit_rate_tolerance = m_nBitrate - m_nBitrate / 2;
		//m_pAVContext->rc_initial_buffer_occupancy = m_nBitrate * 3 / 4;
		AVDictionary *param = 0;
		if (m_pAVCodec->id == AV_CODEC_ID_H264) {

			av_dict_set(&param, "x264-params", "qp=25", 0);
			//av_dict_set(&param, "preset", "slow", 0);
			//av_dict_set(&param, "tune", "zerolatency", 0);
		}
		//H.265
		if (m_pAVCodec->id == AV_CODEC_ID_H265 || m_pAVCodec->id == AV_CODEC_ID_HEVC) {

			av_dict_set(&param, "x265-params", "qp=0", 0);
			av_dict_set(&param, "x265-params", "cqp=10", 0);
			av_dict_set(&param, "x265-params", "crf=25", 0);
			//av_dict_set(&param, "preset", "fast", 0);
			//av_dict_set(&param, "tune", "zero-latency", 0);
			//av_dict_set(&param, "preset", "medium", 0);
		}

		if (avcodec_open2(m_pAVContext, m_pAVCodec, &param) < 0) 
		{
			WriteLogEx(ModuleVideo, LogError, "avcodec_open2 Could not open codec");
			return nRet;
		}

		m_pAVFrame = av_frame_alloc();
		if (m_pAVFrame == nullptr)
		{
			WriteLogEx(ModuleVideo, LogError, "av_frame_alloc error");
			return nRet;
		}

		m_pAVFrame->format = m_pAVContext->pix_fmt;
		m_pAVFrame->width = m_pAVContext->width;
		m_pAVFrame->height = m_pAVContext->height;
		nRet = av_frame_get_buffer(m_pAVFrame, 32);

		if (nRet < 0) {
			WriteLogEx(ModuleVideo, LogError, "Could not allocate the video frame data");
			return nRet;
		}
		
		m_pAVPacket = av_packet_alloc();

		m_bOpen = true;

		return nRet;
	}

	int  CAyjVideoEncoder::Encoder(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts)
	{
		std::vector <int> ii = std::vector<int>(10);
		if (!m_bOpen)
			- 1;

		if (_nW < 100 || _nH < 100 || _ucData == nullptr)
			return -1;
		
		int nRet = -1;

		if (m_pFrameTemp == nullptr || m_pFrameTemp->width != _nW || m_pFrameTemp->height != _nH)
		{
			av_frame_free(&m_pFrameTemp);
			m_pFrameTemp = av_frame_alloc();
			m_pFrameTemp->format = _nFormate;
			m_pFrameTemp->width = _nW;
			m_pFrameTemp->height = _nH;
			av_frame_get_buffer(m_pFrameTemp, 32);
		}

		CAutoLock tempLock(&m_lockApi);
		
		if (AV_PIX_FMT_YUV420P == _nFormate)
		{
			if (_nW == m_nVideoW && _nH == m_nVideoH)
			{
				av_frame_make_writable(m_pAVFrame);
				nRet = av_image_fill_arrays(m_pAVFrame->data, m_pAVFrame->linesize, _ucData, (AVPixelFormat)_nFormate, m_pAVFrame->width, m_pAVFrame->height, 1);
			}
			else
			{
				nRet = av_image_fill_arrays(m_pFrameTemp->data, m_pFrameTemp->linesize, _ucData, (AVPixelFormat)_nFormate, m_pFrameTemp->width, m_pFrameTemp->height, 1);
				if (m_pImgConvertCtx == nullptr)
				{
					m_pImgConvertCtx = sws_getContext(m_pFrameTemp->width, m_pFrameTemp->height, (AVPixelFormat)m_pFrameTemp->format,
						m_pAVFrame->width, m_pAVFrame->height, (AVPixelFormat)m_pAVFrame->format, SWS_BICUBIC, nullptr, nullptr, nullptr);
				}
				if (nRet > 0)
				{
					nRet = sws_scale(m_pImgConvertCtx, (const unsigned char* const*)m_pFrameTemp->data, m_pFrameTemp->linesize, 0, m_pFrameTemp->height, m_pAVFrame->data, m_pAVFrame->linesize);
				}
			}
		}
		else if (AV_PIX_FMT_RGB24 == _nFormate ||
			AV_PIX_FMT_BGR24 == _nFormate ||
			AV_PIX_FMT_BGR32 == _nFormate ||
			AV_PIX_FMT_RGB32 == _nFormate ||
			AV_PIX_FMT_ARGB == _nFormate ||
			AV_PIX_FMT_ABGR == _nFormate ||
			AV_PIX_FMT_RGBA == _nFormate ||
			AV_PIX_FMT_BGRA == _nFormate ||
			AV_PIX_FMT_YUYV422 == _nFormate)
		{
			nRet = av_image_fill_arrays(m_pFrameTemp->data, m_pFrameTemp->linesize, _ucData, (AVPixelFormat)_nFormate, m_pFrameTemp->width, m_pFrameTemp->height, 1);

			if (m_pImgConvertCtx == nullptr)
			{
				m_pImgConvertCtx = sws_getContext(m_pFrameTemp->width, m_pFrameTemp->height, (AVPixelFormat)m_pFrameTemp->format,
					m_pAVFrame->width, m_pAVFrame->height, (AVPixelFormat)m_pAVFrame->format, SWS_BICUBIC, nullptr, nullptr, nullptr);
			}
			if (nRet > 0)
			{
				nRet = sws_scale(m_pImgConvertCtx, (const unsigned char* const*)m_pFrameTemp->data, m_pFrameTemp->linesize, 0, m_pFrameTemp->height, m_pAVFrame->data, m_pAVFrame->linesize);
			}
		}
		else
			return -1; 

		m_pAVFrame->pts = _llPts;
		nRet = avcodec_send_frame(m_pAVContext, m_pAVFrame);
		
		if (nRet < 0)
		{
			WriteLogEx(ModuleVideo, LogError, "error sending a frame for encoding");
			return -1;
		}
		
		av_packet_unref(m_pAVPacket);

		bool bIsOk = false;
		bool bIFrame = false;

		std::vector<std::tuple<int, unsigned char*, bool>> vectOut;
		int nWirteSize = -1;
		while (nRet >= 0)
		{
			nRet = avcodec_receive_packet(m_pAVContext, m_pAVPacket);
			if (nRet == AVERROR(EAGAIN) || nRet == AVERROR_EOF)
				break;
			else if (nRet < 0)
			{
				WriteLogEx(ModuleVideo, LogError, "Error during encoding");
				break;
			}
			bIFrame = m_pAVPacket->flags & AV_PKT_FLAG_KEY;
			bIsOk = true;

			m_nEncodeIndex++;
			m_llEncodePts = m_pAVPacket->pts;// round(m_nEncodeIndex* (1000.0 / m_nFPs));
			//LPM::WriteLogEx(ModuleVideo, LogDebug, "encode:Pts:%lld, bIFrame:%d", m_llEncodePts, bIFrame);
			nWirteSize = m_pAVPacket->size;

			if (m_pEncodeNotify)
				m_pEncodeNotify->OnEncoderDataNotify( m_pAVPacket->data, m_pAVPacket->size, m_nVideoW, m_nVideoH, m_llEncodePts, m_pAVPacket->pts, _llPts, bIFrame);
		}

		if (bIsOk)
			return nWirteSize;
		
		return  -1;
	}

	int CAyjVideoEncoder::EncoderRefresh()
	{
		if (nullptr == m_pAVContext || nullptr == m_pAVPacket)
			return -1;
		
		int nRet = avcodec_send_frame(m_pAVContext, nullptr);

		if (nRet < 0)
		{
			return -1; std::make_tuple(-1, nullptr, 0, 0, false);
		}

		av_packet_unref(m_pAVPacket);

		bool bIsOk = false;
		bool bIFrame = false;

		std::vector<std::tuple<int, unsigned char*, bool>> vectOut;

		while (nRet >= 0)
		{
			nRet = avcodec_receive_packet(m_pAVContext, m_pAVPacket);
			if (nRet == AVERROR(EAGAIN) || nRet == AVERROR_EOF)
				break;
			else if (nRet < 0)
			{
				WriteLogEx(ModuleVideo, LogError, "Error during encoding");
				break;
			}
			bIFrame = m_pAVPacket->flags & AV_PKT_FLAG_KEY;
			bIsOk = true;
			m_nEncodeIndex++;
			m_llEncodePts = m_pAVPacket->pts;///round(m_nEncodeIndex* (1000.0 / m_nFPs));

			if (m_pEncodeNotify)
				m_pEncodeNotify->OnEncoderDataNotify(m_pAVPacket->data, m_pAVPacket->size, m_nVideoW, m_nVideoH, m_llEncodePts, m_pAVPacket->pts, 0, bIFrame);

		}

		return 0;
	}

	long long CAyjVideoEncoder::EncoderGetWritePts()
	{
		return m_llEncodePts;
	}

	int CAyjVideoEncoder::EncoderGetFps()
	{
		return m_nFPs;
	}

	int CAyjVideoEncoder::EncoderMoidfyFps(int _nFps)
	{
		if (m_llEncodePts <= 0)
		{
			m_nFPs = _nFps;
			return 0;
		}
		return -1;
	}

	int CAyjVideoEncoder::EncoderClose(void)
	{
		CAutoLock tempLock(&m_lockApi);

		EncoderRefresh();

		if (m_bOpen) {
			avcodec_close(m_pAVContext);
			m_bOpen = false;
		}
	
		if (m_pAVContext != nullptr) {
			avcodec_free_context(&m_pAVContext);
			m_pAVContext = nullptr;
		}

		if (m_pAVFrame != nullptr) {
			av_frame_free(&m_pAVFrame);
			m_pAVFrame = nullptr;
		}
		if (m_pFrameTemp != nullptr) {
			av_frame_free(&m_pFrameTemp);
			m_pFrameTemp = nullptr;
		}

		if (m_pAVPacket != nullptr) {
			av_packet_free(&m_pAVPacket);
			m_pAVPacket = nullptr;
		}
		if (m_pImgConvertCtx)
		{
			sws_freeContext(m_pImgConvertCtx);
			m_pImgConvertCtx = nullptr;
		}
		m_pAVCodec = nullptr;
		
		return 0;
	}

	int CAyjVideoEncoder::InitFilters(const char* _szFiltersDescr)
	{
		char args[512] = {};
		int ret = 0;
		/*const AVFilter *buffersrc = avfilter_get_by_name("buffer");
		const AVFilter *buffersink = avfilter_get_by_name("buffersink");
		AVFilterInOut *outputs = avfilter_inout_alloc();
		AVFilterInOut *inputs = avfilter_inout_alloc();

		enum AVPixelFormat pix_fmts[] = { AV_PIX_FMT_YUV420P, AV_PIX_FMT_NONE };

		m_pFilterGraph = avfilter_graph_alloc();
		if (!outputs || !inputs || !m_pFilterGraph) {
			ret = AVERROR(ENOMEM);
			goto end;
		}

		snprintf(args, sizeof(args),
			"video_size=%dx%d:pix_fmt=%d:time_base=%d/%d:pixel_aspect=%d/%d",
			m_iPicW, m_iPicH, AV_PIX_FMT_YUV420P,
			m_pAVContext->time_base.num, m_pAVContext->time_base.den,
			m_pAVContext->time_base.num, m_pAVContext->time_base.den);

		ret = avfilter_graph_create_filter(&buffersrc_ctx, buffersrc, "in",
			args, NULL, m_pFilterGraph);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer source\n");
			goto end;
		}

		ret = avfilter_graph_create_filter(&buffersink_ctx, buffersink, "out",
			NULL, NULL, m_pFilterGraph);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer sink\n");
			goto end;
		}

		ret = av_opt_set_int_list(buffersink_ctx, "pix_fmts", pix_fmts,
			AV_PIX_FMT_NONE, AV_OPT_SEARCH_CHILDREN);
		if (ret < 0) {
			av_log(NULL, AV_LOG_ERROR, "Cannot set output pixel format\n");
			goto end;
		}


		outputs->name = av_strdup("in");
		outputs->filter_ctx = buffersrc_ctx;
		outputs->pad_idx = 0;
		outputs->next = NULL;


		inputs->name = av_strdup("out");
		inputs->filter_ctx = buffersink_ctx;
		inputs->pad_idx = 0;
		inputs->next = NULL;

		if ((ret = avfilter_graph_parse_ptr(m_pFilterGraph, _szFiltersDescr,
			&inputs, &outputs, NULL)) < 0)
			goto end;

		if ((ret = avfilter_graph_config(m_pFilterGraph, NULL)) < 0)
			goto end;

	end:
		avfilter_inout_free(&inputs);
		avfilter_inout_free(&outputs);
		*/
		return ret;
	}

} // namespace LPM
