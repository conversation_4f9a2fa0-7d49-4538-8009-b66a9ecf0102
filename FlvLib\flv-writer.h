#ifndef _flv_writer_h_
#define _flv_writer_h_

#include <stddef.h>
#include <stdint.h>

#if defined(__cplusplus)
extern "C" {
#endif

void * flv_writer_create(const char * file, const char * mode, int splice);

void flv_writer_destroy(void* flv);

///Video: FLV VideoTagHeader + AVCVIDEOPACKET: AVCDecoderConfigurationRecord(ISO 14496-15) / One or more NALUs(four-bytes length + NALU)
///Audio: FLV AudioTagHeader + AACAUDIODATA: AudioSpecificConfig(14496-3) / Raw AAC frame data in UI8
///@param[in] data FLV Audio/Video Data(don't include FLV Tag Header)
///@param[in] type 8-audio, 9-video
///@param[in] version is encryption version
int flv_writer_input(void* flv, int type, const void* data, size_t bytes, uint32_t timestamp, int version);

#if defined(__cplusplus)
}
#endif
#endif /* !_flv_writer_h_ */
