﻿
// AyjVideoTools.cpp: 定义应用程序的类行为。
//
#include "pch.h"
#include "AyjVideoExport.h"
#include "../Depends/Common/include/LPMCommonLib.h"

CAyjVideoExport::CAyjVideoExport(HWND _hMainWnd)
{
	m_hWndMain = _hMainWnd;
}
CAyjVideoExport::~CAyjVideoExport()
{
	Release();
}

int CAyjVideoExport::Init(std::string _strInPath, std::string _strExportPath, int _nW, int _nH)
{
	int nRet = 0;

	if (!m_pVideoPlayer)
	{
		m_pVideoPlayer = LPM::IAyjVideoPlayer::Create();
		if (m_pVideoPlayer)
		{
			nRet = m_pVideoPlayer->PlayerInit(_strInPath.c_str(), this);
			m_llVideoLength = m_pVideoPlayer->PlayerGetLength();
			LPM::WriteLogEx(LPM::ModuleVideo, LPM::LogDebug, "video length:%lld, PlayerInit ret:%d", m_llVideoLength, nRet);
		}
	}

	if (nullptr == m_pVideoRecoder && 0 ==  nRet)
	{
		m_pVideoRecoder = LPM::IVideoRecorder::Create(LPM::VideoRecorderFmt::kFlvRecorder);
		nRet = m_pVideoRecoder->Init(_nW, _nH, int(1024 * 1024 * 1.5), 5, 0, 25);
		m_pVideoRecoder->BeginRecord(_strExportPath.c_str(), false, LPM::kRecordEncNone, false, false);

		LPM::WriteLogEx(LPM::ModuleVideo, LPM::LogDebug, "IVideoRecorder Init ret:%d", nRet);
	}

	return nRet;
}

void CAyjVideoExport::Release()
{
	if (m_pVideoPlayer)
	{
		m_pVideoPlayer->PlayerClose();
		LPM::IAyjVideoPlayer::Release(m_pVideoPlayer);
	}

	Stop();
	if (m_pVideoRecoder)
	{
		m_pVideoRecoder->EndRecord();
		LPM::IVideoRecorder::Release(m_pVideoRecoder);
	}
	
}

int CAyjVideoExport::Export(LPM::FrameBase* _objFrame)
{
	int nRet = -1;
	if (m_pVideoRecoder)
	{
		nRet = m_pVideoRecoder->Recording(_objFrame);
	}
	return nRet;
}

int CAyjVideoExport::Start()
{
	if (m_pVideoPlayer)
	{
		m_pVideoPlayer->PlayerSeek(0);
		m_pVideoPlayer->PlayerPlay();
	}
	return 0;
}

int CAyjVideoExport::Stop()
{
	if (m_pVideoPlayer)
	{
		m_pVideoPlayer->PlayerClose();
	}
	if (m_pVideoRecoder)
	{
		m_pVideoRecoder->EndRecord();
		LPM::IVideoRecorder::Release(m_pVideoRecoder);
	}
	return 0;
}
//	播放器状态回调
int CAyjVideoExport::OnPlayerStatusCb(LPM::enPlayerStatus _enStatus)
{
	return 0;
}
//  播放器帧回调
int CAyjVideoExport::OnPlayerFrameCb(LPM::FrameBase* _objFrame)
{
	Export(_objFrame);
	::PostMessage(m_hWndMain, WM_USER + 102, _objFrame->pts, m_llVideoLength);

	return 0;
}