﻿#pragma once

/*
******		FileName	:	AiYunjiSdk.h
******		Describe	:	此文件是c++ 接口
******		Date		:	2021-05-12
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位 
*/



#ifdef LPMSYSINFOLIB_EXPORTS
#define LPM_SysInfo_API  __declspec(dllexport)
#else
#define LPM_SysInfo_API  __declspec(dllimport)
#endif

#include <string>
#include <vector>

using namespace std;

namespace LPM
{
	string GetUuid();
	string GetUuidEx();

	string GetGraphicsCardInfo();
	string GetSysInfo(const wstring& _strWin32Class,const wstring& _name);
	//	获取磁盘序列号
	int GetDiskSerialNumber(std::vector<string>& _vectorDiskSerNub);
}









