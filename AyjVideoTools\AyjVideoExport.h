﻿
// AyjVideoTools.h: PROJECT_NAME 应用程序的主头文件
//

#pragma once

#include "../LPMPaicsVideoLib/Recorder/IVideoRecorder.h"
#include "../LPMPaicsVideoLib/Decoder/IAYJVideoPlayer.h"

class CAyjVideoExport : public LPM::IAyjVideoPlayerCallback
{
public:
	CAyjVideoExport(HWND _hMainWnd);
	~CAyjVideoExport();

	int Init(std::string _strInPath, std::string _strExportPath, int _nW, int _nH);
	int Start();
	int Stop();
	void Release();
	
protected:
	//	播放器状态回调
	virtual int OnPlayerStatusCb(LPM::enPlayerStatus _enStatus);
	//  播放器帧回调
	virtual int OnPlayerFrameCb(LPM::FrameBase* _objFrame);

private:
	
	int  Export(LPM::FrameBase* _objFrame);

private:

	LPM::IVideoRecorder* m_pVideoRecoder = nullptr;
	LPM::IAyjVideoPlayer* m_pVideoPlayer= nullptr;

	HWND m_hWndMain = nullptr;
	long long m_llVideoLength = 0;

};

