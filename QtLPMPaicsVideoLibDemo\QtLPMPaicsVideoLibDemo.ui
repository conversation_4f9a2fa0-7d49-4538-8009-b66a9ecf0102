<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QtLPMPaicsVideoLibDemoClass</class>
 <widget class="QWidget" name="QtLPMPaicsVideoLibDemoClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1090</width>
    <height>754</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QtLPMPaicsVideoLibDemo</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_5">
     <item>
      <widget class="QTabWidget" name="tabWidget">
       <property name="currentIndex">
        <number>0</number>
       </property>
       <widget class="QWidget" name="widget">
        <attribute name="title">
         <string>视频采集</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QGroupBox" name="groupBox">
           <property name="minimumSize">
            <size>
             <width>720</width>
             <height>0</height>
            </size>
           </property>
           <property name="title">
            <string>采集模块相关</string>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="capViewLB">
              <property name="minimumSize">
               <size>
                <width>640</width>
                <height>480</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(139, 139, 139);</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <widget class="QLabel" name="label_2">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>36</height>
                 </size>
                </property>
                <property name="text">
                 <string>采集卡序号：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="capDevNumCB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>20</height>
                 </size>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_6">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>36</height>
                 </size>
                </property>
                <property name="text">
                 <string>当前帧率：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSpinBox" name="fpsSB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="minimum">
                 <number>1</number>
                </property>
                <property name="value">
                 <number>25</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>36</height>
                 </size>
                </property>
                <property name="text">
                 <string>采集卡状态：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="capStatusLB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>36</height>
                 </size>
                </property>
                <property name="text">
                 <string>信号正常</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item row="2" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout">
              <item>
               <widget class="QPushButton" name="initCapPB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>初始化采集</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="startCapPB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>启动采集</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="stopCapPB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>停止采集</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="releaseCapPB">
                <property name="text">
                 <string>释放采集</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
       <widget class="QWidget" name="tab_2">
        <attribute name="title">
         <string>视频播放</string>
        </attribute>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <item>
          <widget class="QGroupBox" name="groupBox_2">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="title">
            <string>播放器模块相关</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_2">
            <item>
             <widget class="QLabel" name="playerViewLB">
              <property name="minimumSize">
               <size>
                <width>640</width>
                <height>480</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(139, 139, 139);</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSlider" name="playerSlider">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QLabel" name="label_5">
                <property name="text">
                 <string>播放器状态：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="playerStatusLB">
                <property name="text">
                 <string>未播放视频</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>32</height>
                 </size>
                </property>
                <property name="text">
                 <string>总时长：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="totalTimeLB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>32</height>
                 </size>
                </property>
                <property name="text">
                 <string>00:00:00:000</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_3">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>32</height>
                 </size>
                </property>
                <property name="text">
                 <string>播放时间：</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="playTimeLB">
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>32</height>
                 </size>
                </property>
                <property name="text">
                 <string>00:00:00:000</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QPushButton" name="playerInitPB">
                <property name="text">
                 <string>初始化播放器</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="playerStartPB">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>开始播放</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="playerPausePB">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>暂停播放</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="playerPlayPB">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>继续播放</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="playerStopPB">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="text">
                 <string>结束播放</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="playerReleasePB">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="text">
                 <string>释放播放器</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBox_4">
       <property name="title">
        <string>视频录制模块</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <item>
           <widget class="QLabel" name="label_7">
            <property name="text">
             <string>flv路径:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="flvPathLE">
            <property name="text">
             <string>test.flv</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_9">
            <property name="text">
             <string>水印文本：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="waterMarkTextLE">
            <property name="text">
             <string>中山大学第一附属医院</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="recordPosCB">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>生成pos文件</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="useWaterMarkCB">
            <property name="text">
             <string>添加水印</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="splitCB">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>续接视频</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_8">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="text">
             <string>加密版本：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="flvEnvVerCB">
            <property name="enabled">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="flvPathLB">
            <property name="text">
             <string/>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_7">
          <item>
           <widget class="QPushButton" name="addWaterMarkPB">
            <property name="text">
             <string>添加水印</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="flvInitPB">
            <property name="text">
             <string>初始化FLV录制模块</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="flvStartRecordPB">
            <property name="text">
             <string>开始录制FLV</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="flvStopRecordPB">
            <property name="text">
             <string>停止录制FLV</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="flvReleasePB">
            <property name="text">
             <string>释放FLV录制模块</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="title">
      <string>日志相关</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <widget class="QTextBrowser" name="logTB">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QCheckBox" name="debugLevelCB">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="text">
           <string>DebugLevel</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="clearLogPB">
          <property name="text">
           <string>清除窗口日志</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="QtLPMPaicsVideoLibDemo.qrc"/>
 </resources>
 <connections/>
</ui>
