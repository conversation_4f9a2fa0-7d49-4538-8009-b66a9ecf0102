﻿#include "RenderSdl.h"

#pragma comment (lib, "../Depends/SDL2/lib/SDL2.lib")

#pragma warning(disable:4312)

static bool g_bSdlInit = false;

namespace AYJ
{
	CRenderSdl::CRenderSdl(bool  _bMode)
	{
		m_ulHandleWnd = 0;
		m_pSdlWindow = nullptr;
		m_pSdlTexture = nullptr;
		m_pSdlRenderer = nullptr;
		m_bIsDxMode = _bMode;

		m_bExit = false;
		if (!g_bSdlInit)
		{
			if (!SDL_Init(SDL_INIT_VIDEO))
			{
				g_bSdlInit = true;
			}
		}
	}

	CRenderSdl::~CRenderSdl()
	{
		Release();
		SDL_Quit();
		g_bSdlInit = false;
	}

	int CRenderSdl::Init(unsigned long _ulHandle, const int _nSrcWidth, const int _nSrcHeight)
	{
		CAutoLock tempLock(&m_lock);

		if (!m_pSdlWindow)
		{
			m_ulHandleWnd = _ulHandle;
			//创建窗口
			m_pSdlWindow = SDL_CreateWindowFrom((const void*)m_ulHandleWnd);
			SDL_ShowWindow(m_pSdlWindow);
			if (m_pSdlWindow == nullptr)
			{
				const char* n = SDL_GetError();
				//LogWrite_my(L"SDL_GetWindowFromID errer");
				return -1;
			}
			//	创建渲染
			m_pSdlRenderer = SDL_CreateRenderer(m_pSdlWindow, -1, SDL_RENDERER_ACCELERATED);
			//SDL_SetRenderDrawColor(m_pSdlRenderer, 0xA0, 0xA0, 0xA0, 0x0F);
			SDL_RenderClear(m_pSdlRenderer);
		}
		RECT rect;
		GetWindowRect((HWND)_ulHandle, &rect);
		m_nWidth = rect.right - rect.left;
		m_nHeight = rect.bottom - rect.top;

		m_RectDes.x = m_Rectsrc.x = 0;
		m_RectDes.y = m_Rectsrc.y = 0;

		m_RectDes.w = m_nWidth;
		m_RectDes.h = m_nHeight;
		//	视频源
		m_Rectsrc.w = _nSrcWidth;
		m_Rectsrc.h = _nSrcHeight;

		if (m_pSdlTexture)
		{
			SDL_DestroyTexture(m_pSdlTexture);
			m_pSdlTexture = nullptr;
		}
		//	创建文理
		m_pSdlTexture = SDL_CreateTexture(m_pSdlRenderer, SDL_PIXELFORMAT_IYUV, SDL_TEXTUREACCESS_STREAMING, _nSrcWidth, _nSrcHeight);
		SDL_SetTextureAlphaMod(m_pSdlTexture, 255);
		SDL_SetTextureColorMod(m_pSdlTexture, 255, 255, 200);

		//int n = SDL_RenderClear(m_pSdlRenderer);
		//FillTexture();
		//n = SDL_RenderCopy(m_pSdlRenderer, m_pSdlTexture, &m_Rectsrc, &m_RectDes);

		//SDL_RenderPresent(m_pSdlRenderer);
		//SDL_Surface* pSurface = SDL_GetWindowSurface(m_pSdlWindow);
		//SDL_FillRect(pSurface, &m_RectDes, 0xff6000);
		//更新显示
		//SDL_UpdateWindowSurface(m_pSdlWindow);
		//SDL_FillRect(NULL, 0x00ff00);
		//SDL_UpdateWindowSurface(m_pSdlWindow);
		//SDL_Delay(100000);
		return 0;
	}

	void CRenderSdl::Release()
	{
		CAutoLock tempLock(&m_lock);

		if (m_pSdlTexture)
			SDL_DestroyTexture(m_pSdlTexture);
		if (m_pSdlRenderer)
			SDL_DestroyRenderer(m_pSdlRenderer);
		if (m_pSdlWindow)
			SDL_DestroyWindow(m_pSdlWindow);

		m_pSdlTexture = nullptr;
		m_pSdlRenderer = nullptr;
		m_pSdlWindow = nullptr;
		m_ulHandleWnd = 0;
	}

	void CRenderSdl::PutYuv420pData(unsigned char* _pYuvBuf)
	{
		Play(_pYuvBuf);
	}

	void CRenderSdl::PutDecodedFrame(AVCodecContext *s, AVFrame* _pFrame)
	{
	}

	void CRenderSdl::ShowWndModify()
	{
	}

	void CRenderSdl::Play(unsigned char *pYuvBuf)
	{
		CAutoLock tempLock(&m_lock);

		if (pYuvBuf != nullptr && nullptr != m_pSdlTexture && nullptr != m_pSdlRenderer)
		{
			int nRet = SDL_UpdateTexture(m_pSdlTexture, &m_Rectsrc, pYuvBuf, m_Rectsrc.w);
			nRet = SDL_RenderClear(m_pSdlRenderer);
			if (nRet < 0)
			{
				static const char * p = SDL_GetError();
				nRet = -100;
			}

			nRet = SDL_RenderCopy(m_pSdlRenderer, m_pSdlTexture, &m_Rectsrc, &m_RectDes);

			SDL_RenderPresent(m_pSdlRenderer);
		}
	}

	void CRenderSdl::FillTexture()
	{
		int n = SDL_SetRenderDrawBlendMode(m_pSdlRenderer, SDL_BLENDMODE_NONE);
		n = SDL_SetRenderDrawColor(m_pSdlRenderer, 255, 0, 0, 200);
		n = SDL_RenderFillRect(m_pSdlRenderer, NULL);
		SDL_RenderPresent(m_pSdlRenderer);
	}
}

