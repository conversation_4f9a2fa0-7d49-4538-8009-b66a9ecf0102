﻿/*
******		FileName	:	AYJVideoEncoder.h
******		Describe	:	此文件是c++ 接口,主要负责视频的编解码
******		Date		:	2021-07-20
******		Author		:	TangJinFei
******		Copyright	:	Guangzhou AiYunJi Inc.
******		Note		:	1、依赖库..., 2、所有字符编码采用u8格式 3、仅支持64位
*/
#ifndef __AJY_VIDEO_ENCODER_H__
#define __AJY_VIDEO_ENCODER_H__

#ifdef __cplusplus
extern "C" {
#endif
#include "libavcodec/avcodec.h"
#include "libavutil/imgutils.h"
#include "libavfilter/avfilter.h"
#include "libavfilter/buffersink.h"
#include "libavfilter/buffersrc.h"
#include "libswscale/swscale.h"
#include "libavutil/imgutils.h"
#include "libavutil/opt.h"
#ifdef __cplusplus
}
#endif


#include "../../Depends/Common/include/LPMCommonLib.h"
#include "Encoder/IAYJVideoEncoder.h"
#include <tuple>

using namespace LPM;

namespace AYJ
{
	class CAyjVideoWriter; 

	class CAyjVideoEncoder : public IAyjVideoEncoder
    {
    public:
		CAyjVideoEncoder(IOVideoEncoderNotify* _pEncodeNotify);
        ~CAyjVideoEncoder();

        virtual int EncoderInit(int _nVdieoW, int _nVideoH, int _nGopSize, int _nBitrate, float _fFps, long long _llPtsStart, int _nIndexStart, bool bFlagH264 = true) override;

		virtual int Encoder(unsigned char* _ucData, int _nDataSize, int _nFormate, int _nW, int _nH, long long _llPts) override;

		virtual int EncoderClose(void) override;

		virtual int EncoderRefresh()override ;

		virtual long long EncoderGetWritePts() override;

		 int EncoderGetFps() ;

		 int EncoderMoidfyFps(int _nFps) ;

	private:

		int InitFilters(const char* _szFiltersDescr);
		AVFilterGraph *m_pFilterGraph = nullptr;
		AVFilterContext *buffersink_ctx = nullptr;
		AVFilterContext *buffersrc_ctx = nullptr;

	private:

		IOVideoEncoderNotify* m_pEncodeNotify = nullptr;

    private:
		bool  m_bH264 = true;
        bool  m_bOpen = false;
		int   m_nVideoW = 0;
        int   m_nVideoH = 0;
        int   m_nBitrate = 0;
        int   m_nGopSize = 0;
		int		m_nFPs = 25;
		long long m_llEncodePts = 0;
		int m_nEncodeIndex = 0;

		AVCodec *				m_pAVCodec = nullptr;
		AVCodecContext *		m_pAVContext = nullptr;
		SwsContext*				m_pImgConvertCtx = nullptr;

		AVPacket *             m_pAVPacket = nullptr;
		AVFrame *              m_pAVFrame = nullptr;
		AVFrame *			   m_pFrameTemp = nullptr;


        int   m_bframes = 0;
       
        CLock				   m_lockApi;

    };
} // namespace lpm

#endif // !__FF_VIDEO_ENCODER_H__
