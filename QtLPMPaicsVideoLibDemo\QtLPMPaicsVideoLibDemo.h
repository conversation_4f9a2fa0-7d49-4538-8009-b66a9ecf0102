﻿#pragma once

#include <QtWidgets/QWidget>
#include "ui_QtLPMPaicsVideoLibDemo.h"
#include "../LPMPaicsVideoLib/LPMPaicsVideoLib.h"

#include <qmutex.h>
#include <qimage.h>
#include <qtimer.h>
using namespace LPM;

class QtLPMPaicsVideoLibDemo : 
    public QWidget,
    public IVideoCaptureCallback,
    public IFpsAdapterCallback,
    public IVideoPlayerCallback
{
    Q_OBJECT

    Q_ENUM(CapStatus)
    Q_ENUM(PlayerEvent)
public:
    QtLPMPaicsVideoLibDemo(QWidget *parent = Q_NULLPTR);
    ~QtLPMPaicsVideoLibDemo();

public slots:
    /** 采集模块相关 */ 
    void slotInitVideoCapturer(void);
    void slotStartVideoCapturer(void);
    void slotStopVideoCapturer(void);
    void slotReleaseVideoCapturer(void);

    void slotInitFpsAdapter(int fps);
    void slotResetFpsAdapter(int seq);
    void slotReleaseFpsAdapter(void);
    void slotReCreateFpsAdapter(int fps);

    void slotUpdateCapStatus(CapStatus status);
    void slotCapRenderFrame(const QImage&img);
    void slotUpdateCapCount(void);
    void slotUpdateCurCapDevNum(const QString & text);

    /** 视频录制模块相关 */
    void slotInitFlvRecorder(void);
    void slotStartFlvRecorder(void);
    void slotStopFlvRecorder(void);
    void slotReleaseFlvRecorder(void);
    void slotAddWaterMark(void);
    void slotRecordFrame(const FrameBase & frame);

    /** 播放器模块相关 */
    void slotInitPlayer(void);
    void slotStartPlayer(void);
    void slotStopPlayer(void);
    void slotPausePlayer(void);
    void slotPlayPlayer(void);
    void slotReleasePlayer(void);
    void slotSliderMoved(void);

    void slotUpdatePlayTime(int64_t time);
    void slotUpdatePlayerStatus(PlayerEvent event);
    void slotUpdatePlayerFrame(const FrameBase & frame);

    /** 日志相关 */
    void slotAppendLogMsg(const QString & msg);
    void slotClearLogWithWidget(void);

signals:
    /** 采集模块相关 */ 
    void sigRenderCapFrame(const QImage & img);
    void sigUpdateCapStatus(CapStatus status);

    /** 视频录制模块相关 */
    void sigRecordFlvFrame(const FrameBase& frame);

    /** 日志相关 */
    void sigAppendLogMsg(const QString & msg);

    /** 播放器相关 */
    void sigUpdatePlayTime(int64_t time);
    void sigUpdatePlayerStatus(PlayerEvent event);
    void sigUpdatePlayerFrame(const FrameBase & frame);
    
protected:
    /** 摄像头影像采集回调接口 */
    virtual int OnFrameCallback(uint32_t capId, FrameBase * frame);
    /** 摄像头状态变化通知回调接口 */
    virtual int OnCapturerStatus(uint32_t capId, CapStatus status);
    /** 帧率适配回调接口 */
    virtual int OnFpsAdpterCallback(FrameBase * frame);

    /** 播放器事件通知回调 */
    virtual int OnPlayerCallback(PlayerEvent event);
    /** 播放时间变更通知回调 */
    virtual int OnPlayerTimeChangedCallback(int64_t time);
    /** 播放器播放缓存（kFFPlayer类型暂未实现此回调） */
    virtual int OnPlayerBufferingCallback(float percent);
    /**  播放器显示帧回调*/
    virtual int OnPlayerFrameCallback(FrameBase * frame);

private:
    void initConnect(void);
    QString msToPlayTime(int64_t time);
    void resetPlayerView(void);
    QImage generalWaterMarkImg(int imgW, int imgH, const QString & text);

private:
    Ui::QtLPMPaicsVideoLibDemoClass ui;

    FrameBase         * m_pCapI420Frame = nullptr;
    FrameBase         * m_pFlvRecordFrame = nullptr;
    IVideoCapturer    * m_pVideoCapturer = nullptr;
    IFpsAdapter       * m_pFpsAdapter = nullptr;
    IVideoRecorder    * m_pFlvRecorder = nullptr;
    IVideoPlayer      * m_pVideoPlayer = nullptr;

    int                 m_iCurCapDevNum = -1;

    QMutex              m_fpsAdapterMutex;
    QTimer              m_updateCapDevCountTimer;
};
