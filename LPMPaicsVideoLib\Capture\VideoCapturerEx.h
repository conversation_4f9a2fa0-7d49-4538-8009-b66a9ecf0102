/**
 * Windows视频采集器扩展类
 * <AUTHOR>
 * @description 基于DirectShow的高级视频采集器实现
 *              在VideoCaptureBase基础上增加了无信号检测、图像处理等功能
 *              支持采集卡断线检测和自动重连机制
 */
#ifndef IVIDOE_CAPTURER_H
#define IVIDOE_CAPTURER_H

#include "../Common/Typedefs.h"
#include <vector>
#include "IVideoCapturerEx.h"
#include "VideoCaptureBase.h"
#include "ImageScale/ImageScale.h"
#include "opencv2\opencv.hpp"
#include "opencv2\highgui.hpp"

namespace AYJ
{
    /**
     * @class CVideoCapturerEx
     * @brief 高级视频采集器实现类
     *
     * 功能特性：
     * 1. 基于DirectShow的视频采集
     * 2. 智能无信号检测算法
     * 3. 采集卡断线自动检测和重连
     * 4. 多线程异步处理
     * 5. 内存优化和性能调优
     * 6. 完善的错误处理和状态管理
     */
    class CVideoCapturerEx : public IVideoCapturerEx , public LPM::CThreadEx, public LPM::CEventEx
    {
	public:
		CVideoCapturerEx();
		virtual ~CVideoCapturerEx();

		// CaptureGetDevCount 获取视频设备数量
		virtual int CaptureGetDevCount();

		// CaptureGetDevInfo 获取视频设备信息
		virtual int CaptureGetDevInfo(const int _nDevIndex, tagDevInfo& _objDevInfo);

		// CaptureInit 初始化采集设备
		virtual int CaptureInit(const int _nDevIndex, IVideoCaptureCallbackEx* _pCapCallBack);

		// CaptureRelease 释放采集设备
		virtual int CaptureRelease();

		// CaptureDevFormatSet 设置设备的参数
		virtual int CaptureDevFormatSet(const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut);

		// CaptureStart 开始采集
		virtual int CaptureStart();

		// CapturePause 暂停采集
		virtual int CapturePause();

		// CaptureStop 停止采集
		virtual int CaptureStop();

		// CaptureGetState 获取采集状态
		virtual eCapDevState CaptureGetState();

	private:
		// ========== 静态回调函数 ==========

		// FuncFrameCapturedCallback 采集卡帧数据回调
		static void FuncFrameCapturedCallback(double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nVideoFormate, void* _pUserData);

		// FuncStateCapturedCallback 采集卡状态变化回调通知
		static void FuncStateCapturedCallback(eCapDevState _eState, void* _pUserData);

		// ThreadMainLoop 主处理线程循环
		virtual int ThreadMainLoop(void);

		// ========== 无信号检测算法 ==========

		// NoSignal1 检测旧版采集卡无信号状态
		bool NoSignal1(cv::Mat _objDest, cv::Mat _ObjSrcNosignal);

		// NoSignal2 检测新版采集卡无信号状态
		bool NoSignal2(cv::Mat _objDest, cv::Mat _ObjSrcNosignal);

		// GetNoSignalBmp1 获取旧版采集卡无信号检测图片
		bool GetNoSignalBmp1(tagVdieoFrame *& nosignalBmp);

		// GetNoSignalBmp2 获取新版采集卡无信号检测图片
		bool GetNoSignalBmp2(tagVdieoFrame *& nosignalBmp);

	private:
		// ========== 核心组件 ==========
		const int m_nCaptureType = 1;              // 采集类型（1=视频）
		unsigned int m_nCaptureId = -1;            // 采集设备ID
		CVideoCaptureBase m_objCaptureBase;        // 底层采集器

		// ========== 状态管理 ==========
		bool m_bIsCaptureBreak = false;            // 采集断线标志
		IVideoCaptureCallbackEx* m_pCallBack = nullptr; // 上层回调接口

		// ========== 格式和数据缓冲 ==========
		tagDevSupportedFormate m_tagSetFormat;     // 设置的采集格式
		LPM::CLock m_lockTemp;                     // 临时数据锁
		std::vector<unsigned char> m_vectFrameDataTemp; // 临时帧数据缓冲

		// ========== 无信号检测资源 ==========
		tagVdieoFrame* g_pObjNoSignalFrame1 = nullptr; // 无信号检测图片1
		tagVdieoFrame* g_pObjNoSignalFrame2 = nullptr; // 无信号检测图片2

		// ========== 图像处理工具 ==========
		CImageConvert g_objImageCvt;               // 图像格式转换器

    };
} // namespace LPM

#endif // !IVIDOE_CAPTURER_H

