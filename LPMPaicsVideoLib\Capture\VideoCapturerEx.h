/**
 * windows摄像头影像采集器
 * <AUTHOR>
 */
#ifndef IVIDOE_CAPTURER_H
#define IVIDOE_CAPTURER_H

#include "../Common/Typedefs.h"
#include <vector>

#include "IVideoCapturerEx.h"
#include "VideoCaptureBase.h"
#include "ImageScale/ImageScale.h"

#include "opencv2\opencv.hpp"
#include "opencv2\highgui.hpp"


namespace AYJ 
{
    class  CVideoCapturerEx : public IVideoCapturerEx , public LPM::CThreadEx, public LPM::CEventEx
    {
	public:
		CVideoCapturerEx();
		virtual ~CVideoCapturerEx();

		//	获取视频设备数量
		virtual int CaptureGetDevCount();
		//	获取视频设备信息
		virtual int CaptureGetDevInfo(const int _nDevIndex, tagDevInfo& _objDevInfo) ;

		//	初始化采集设备
		virtual int CaptureInit(const int _nDevIndex, IVideoCaptureCallbackEx* _pCapCallBack);
		//	释放采集设备
		virtual int CaptureRelease();

		//	设置设备的参数
		virtual int CaptureDevFormatSet(const tagDevSupportedFormate _tagFormatmIn, tagDevSupportedFormate& _tagFormatOut) ;

		//	采集开始
		virtual int CaptureStart() ;
		//	采集暂停
		virtual int CapturePause();
		//	采集停止
		virtual int CaptureStop() ;

		//	获取采集状态
		virtual eCapDevState CaptureGetState();

	private:

		//	采集卡帧回调
		static void FuncFrameCapturedCallback(double _dSampleTime, unsigned char* _pFrame, long _lFrameSize, int _nVideoFormate, void* _pUserData);
		//	采集卡状态回调通知
		static void FuncStateCapturedCallback(eCapDevState _eState, void* _pUserData);

		//	线程
		virtual int ThreadMainLoop(void) ;

	private:

		//	检测旧的采集卡 是否 无信号输入
		bool NoSignal1(cv::Mat _objDest, cv::Mat _ObjSrcNosignal);
		//	检测新的采集卡 是否 无信号输入
		bool NoSignal2(cv::Mat _objDest, cv::Mat _ObjSrcNosignal);

		//	获取采集卡无信号图片
		bool GetNoSignalBmp1(tagVdieoFrame *& nosignalBmp);
		bool GetNoSignalBmp2(tagVdieoFrame *& nosignalBmp);

		//bool CheckSignalIsNormal(FrameBase * nosignalBmp, FrameBase * frame);

	private:
		const int m_nCaptureType = 1;	//	采集的是视频
		unsigned int m_nCaptureId = -1;
		CVideoCaptureBase m_objCaptureBase;

		bool m_bIsCaptureBreak = false;
		IVideoCaptureCallbackEx* m_pCallBack = nullptr;

		tagDevSupportedFormate m_tagSetFormat;

		LPM::CLock m_lockTemp;
		std::vector<unsigned char> m_vectFrameDataTemp;

		tagVdieoFrame* g_pObjNoSignalFrame1 = nullptr;
		tagVdieoFrame* g_pObjNoSignalFrame2 = nullptr;

		CImageConvert g_objImageCvt;

    };
} // namespace LPM

#endif // !IVIDOE_CAPTURER_H

