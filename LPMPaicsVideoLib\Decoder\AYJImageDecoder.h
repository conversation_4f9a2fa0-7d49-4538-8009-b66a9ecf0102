#ifndef __AYJ_IMAGEDECODER_H__
#define __AYJ_IMAGEDECODER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "libavformat/avformat.h"
#include "libavcodec/avcodec.h"
#include "libavutil/time.h"
#include "libswresample/swresample.h"
#include "libswscale/swscale.h"
#include "libavutil/imgutils.h"


#ifdef __cplusplus
}
#endif

#include "../../Depends/Common/include/LPMCommonLib.h"

using namespace  LPM;

namespace AYJ
{
	class CAyjImageDecoder
	{
	public:
		CAyjImageDecoder();
		~CAyjImageDecoder();

		int DecoderInit(int _nVideoW, int _nVideoH, int _nImageFormate = AV_CODEC_ID_MJPEG);
		int DecoderClose();

		int Decoder(unsigned char* _ucDataIn, int _nDataSize, unsigned char* _unDataOut);
		
	private:
		int DecoderFlush();

	private:

		AVCodecContext*		m_pCodecCtx = nullptr;

		AVCodec*			m_pCodec = nullptr;
		AVPacket			m_objPacket;
		AVFrame				m_pAvFrameOut;

		bool m_bInit = false;

		CLock m_lockApi;

	};
}

#endif //__VIDEODECODER_H__