﻿#include "QtLPMPaicsVideoLibDemo.h"
#include "../LPMPaicsVideoLib/ImageScale/ImageScale.h"

#include <cmath>

#include <qdebug.h>
#include <qmutex.h>
#include <qdir.h>
#include <qdatetime.h>
#include <qtextstream.h>
#include <qpixmap.h>
#include <qfileinfo.h>
#include <qpainter.h>
#include <qbuffer.h>
#include <qthread.h>
#include <qfontmetrics.h>

#include <qmessagebox.h>
#include <qslider.h>
#include <qlabel.h>
#include <qtextbrowser.h>
#include <qpushbutton.h>
#include <qspinbox.h>
#include <qpalette.h>
#include <qfiledialog.h>
#include <qcombobox.h>

#pragma execution_character_set("utf-8")

#if defined(_DEBUG)
#pragma comment(lib, "../x64/debug/LPMPaicsVideoLib.lib")
#else 
#pragma comment(lib, "../x64/release/LPMPaicsVideoLib.lib")
#endif
extern "C" {
#include "../Depends/FFmpeg-4.0/include/libavcodec/avcodec.h"
#include "../Depends/FFmpeg-4.0/include/libavformat/avformat.h"
#include "../Depends/FFmpeg-4.0/include/libavutil/avutil.h"
#include "../Depends/FFmpeg-4.0/include/libavutil/frame.h"
}
#pragma comment(lib, "../Depends/FFmpeg-4.0/lib/avcodec.lib")
#pragma comment(lib, "../Depends/FFmpeg-4.0/lib/avutil.lib")

static QtLPMPaicsVideoLibDemo * kDemoInstance = nullptr;
static void outputMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    static QMutex mutex;
    mutex.lock();

    QString text, level;
    switch (type) {
    case QtDebugMsg:
        level = QString("Debug");
        break;
    case QtInfoMsg:
        level = QString("Info");
        break;
    case QtWarningMsg:
        level = QString("Warning");
        break;
    case QtCriticalMsg:
        level = QString("Error");
        break;
    case QtFatalMsg:
        level = QString("Fatal");
    }

    level += ":";
    text.append(level).append(QString(7 - level.size(), ' '));
    QString logfilepath = QGuiApplication::applicationDirPath() + "/log";
    QString current = QDateTime::currentDateTime().toString("MM-dd hh:mm:ss:zzz");
    QString message = QString("%1[%2]:%3 ").arg(text).arg(current).arg(msg);
    QString logfilename = logfilepath + "/videodemo" + QDateTime::currentDateTime().toString("yyyyMMdd") + ".log";
    QDir dir(logfilepath);
    if (!dir.exists()) {
        dir.mkdir(QGuiApplication::applicationDirPath() + "/log");
    }
    QFile file(logfilename);
    int flag = QIODevice::WriteOnly | QIODevice::Append;
    if (!file.exists()) {
        flag |= QIODevice::NewOnly;
    }
    file.open((QIODevice::OpenModeFlag)flag);
    QTextStream stream(&file);
    stream << message << "\r\n";
    file.flush();
    file.close();
    mutex.unlock();

    if (kDemoInstance) {
        emit kDemoInstance->sigAppendLogMsg(message);
    }
}

/** jpg 转 yuv420p */
static bool JpegToYuv420p(char * jpgData, int jpgSize, char * yuvData, int yuvSize, int & width, int & height)
{
    bool ret = false;
    AVCodecContext *pCodecCtx = NULL;
    AVFrame *pFrame = NULL;
    AVCodec *pCodec = NULL;
    AVPacket packet;
    int result = -1;
    int frameSize = 0;
    int i = 0;
    int height_half = 0;
    int width_half = 0;
    int y_wrap = 0;
    int u_wrap = 0;
    int v_wrap = 0;

    unsigned char *y_buf = NULL;
    unsigned char *u_buf = NULL;
    unsigned char *v_buf = NULL;
    if (!jpgData || jpgSize <= 0 || !yuvData || yuvSize <= 0) {
        goto err;
    }

    pCodec = avcodec_find_decoder(AV_CODEC_ID_MJPEG);
    if (pCodec == NULL) {
        goto err;
    }
    pCodecCtx = avcodec_alloc_context3(pCodec);
    if (pCodecCtx == NULL) {
        goto err;
    }
    pCodecCtx->codec_type = AVMEDIA_TYPE_VIDEO;
    pCodecCtx->pix_fmt = AV_PIX_FMT_YUV420P;

    if (avcodec_open2(pCodecCtx, pCodec, nullptr) < 0) {
        goto err;
    }
    pFrame = av_frame_alloc();
    if (pFrame == NULL) {
        goto err;
    }

    av_new_packet(&packet, jpgSize);
    memcpy(packet.data, jpgData, jpgSize);
    result = avcodec_send_packet(pCodecCtx, &packet);
    if (result < 0) {
        goto err;
    }
    result = avcodec_receive_frame(pCodecCtx, pFrame);
    if (result == AVERROR(EAGAIN) || result == AVERROR_EOF) {
        goto err;
    }
    width = pCodecCtx->width;
    height = pCodecCtx->height;
    frameSize = width * height * 3 >> 1;
    if (frameSize > yuvSize) {
        goto err;
    }
    i = 0;
    frameSize = 0;
    height_half = height / 2;
    width_half = width / 2;
    y_wrap = pFrame->linesize[0];
    u_wrap = pFrame->linesize[1];
    v_wrap = pFrame->linesize[2];

    y_buf = pFrame->data[0];
    u_buf = pFrame->data[1];
    v_buf = pFrame->data[2];

    //save y
    for (i = 0; i < height; i++) {
        memcpy(yuvData + frameSize, y_buf, width);
        frameSize += width;
        y_buf += width;
    }
    //save u
    for (i = 0; i < height_half; i++) {
        memcpy(yuvData + frameSize, u_buf, width_half);
        frameSize += width_half;
        u_buf += width_half;
    }
    //save v
    for (i = 0; i < height_half; i++) {
        memcpy(yuvData + frameSize, v_buf, width_half);
        frameSize += width_half;
        v_buf += width_half;
    }
    ret = true;
err:
    av_frame_free(&pFrame);
    av_packet_unref(&packet);
    avcodec_close(pCodecCtx);
    return ret;
}

QtLPMPaicsVideoLibDemo::QtLPMPaicsVideoLibDemo(QWidget *parent)
    : QWidget(parent)
{
    kDemoInstance = this;
    qInstallMessageHandler(outputMessage);
    ui.setupUi(this);
    // 注册自定义类型
    qRegisterMetaType<CapStatus>("CapStatus");
    qRegisterMetaType<FrameBase>("FrameBase&");
    qRegisterMetaType<FrameBase>("FrameBase");
    qRegisterMetaType<int64_t>("int64_t");
    qRegisterMetaType<int64_t>("int64_t&");
    qRegisterMetaType<PlayerEvent>("PlayerEvent");
    // 初始化信号与槽
    initConnect();
    // 初始化帧率适配模块
    slotInitFpsAdapter(25);
    // 实时刷新当前采集卡数量信息
    m_updateCapDevCountTimer.start(500);
}

void QtLPMPaicsVideoLibDemo::slotInitVideoCapturer(void)
{
    if (m_pVideoCapturer == nullptr) {
        m_pVideoCapturer = IVideoCapturer::Create(m_iCurCapDevNum);
    }

    if (m_pVideoCapturer->Init(this, 2, 60, 1920, 1080, kImageYUY2) < 0) {
        qCritical() << "采集模块初始化失败";
        IVideoCapturer::Release(m_pVideoCapturer);
        return;
    }
    qInfo() << "采集模块初始化成功";
}

QtLPMPaicsVideoLibDemo::~QtLPMPaicsVideoLibDemo()
{
    slotReleaseFpsAdapter();
    slotReleaseVideoCapturer();
    slotReleaseFlvRecorder();
    slotReleasePlayer();
}

void QtLPMPaicsVideoLibDemo::slotAppendLogMsg(const QString & msg)
{
    ui.logTB->append(msg);
}

void QtLPMPaicsVideoLibDemo::slotStartVideoCapturer(void)
{
    if (m_pVideoCapturer) {
        if (m_pVideoCapturer->Start() < 0) {
            qCritical() << "采集模块启动失败";
            return;
        }
        qInfo() << "采集模块启动成功";
    } else {
        qWarning() << "采集模块未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotStopVideoCapturer(void)
{
    if (m_pVideoCapturer) {
        if (m_pVideoCapturer->Stop() < 0) {
            qCritical() << "采集模块停止失败";
            return;
        }
        ui.capViewLB->setPixmap(QPixmap());
        qInfo() << "采集模块停止成功";
    } else {
        qWarning() << "采集模块未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotReleaseVideoCapturer(void)
{
    if (m_pVideoCapturer) {
        m_pVideoCapturer->Stop();
        IVideoCapturer::Release(m_pVideoCapturer);
        if (m_pCapI420Frame) {
            delete m_pCapI420Frame;
            m_pCapI420Frame = nullptr;
        }
        ui.capViewLB->setPixmap(QPixmap());
        qInfo() << "采集模块释放成功";
    } else {
        qWarning() << "采集模块未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotInitFpsAdapter(int fps)
{
    QMutexLocker locker(&m_fpsAdapterMutex);
    if (m_pFpsAdapter == nullptr) {
        m_pFpsAdapter = IFpsAdapter::Create();
    }
    if (m_pFpsAdapter->Init(fps, this) < 0) {
        IFpsAdapter::Release(m_pFpsAdapter);
        qCritical() << "帧率适配模块初始化失败";
        return;
    }
    qInfo() << "帧率[" << fps << "]设置成功";
}

void QtLPMPaicsVideoLibDemo::slotResetFpsAdapter(int seq)
{
    QMutexLocker locker(&m_fpsAdapterMutex);
    if (m_pFpsAdapter) {
        if (m_pFpsAdapter->Reset(seq) < 0) {
            qCritical() << "帧率适配模块设置[" << seq << "]失败";
            return;
        }
    } else {
        qWarning() << "帧率适配模块还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotReleaseFpsAdapter(void)
{
    QMutexLocker locker(&m_fpsAdapterMutex);
    if (m_pFpsAdapter) {
        IFpsAdapter::Release(m_pFpsAdapter);
    }
    qInfo() << "帧率适配模块释放成功";
}

void QtLPMPaicsVideoLibDemo::slotReCreateFpsAdapter(int fps)
{
    slotReleaseFpsAdapter();
    slotInitFpsAdapter(fps);
}

void QtLPMPaicsVideoLibDemo::slotUpdateCapStatus(CapStatus status)
{
    QString text;
    QPalette palette;
    switch (status) {
    case LPM::kCapError:
        text = "采集异常";
        palette.setColor(QPalette::WindowText, Qt::red);
        break;
    case LPM::kCapSigNormal:
        text = "信号正常";
        palette.setColor(QPalette::WindowText, Qt::black);
        break;
    case LPM::kCapSigAbnormal:
        text = "信号异常";
        palette.setColor(QPalette::WindowText, Qt::darkGray);
        break;
    default:
        break;
    }
    ui.capStatusLB->setText(text);
    ui.capStatusLB->setPalette(palette);
}

void QtLPMPaicsVideoLibDemo::slotCapRenderFrame(const QImage & img)
{
    ui.capViewLB->setPixmap(QPixmap::fromImage(
        img.scaled(ui.capViewLB->size(), Qt::AspectRatioMode::KeepAspectRatio)
    ));
}

void QtLPMPaicsVideoLibDemo::slotUpdateCapCount(void)
{
    int count = IVideoCapturer::GetVideoCapturerCount();
    for (;;) {
        if (ui.capDevNumCB->count() == count) {
            return;
        } else if (ui.capDevNumCB->count() < count) {
            ui.capDevNumCB->addItem(QString::number(ui.capDevNumCB->count()));
        } else if (ui.capDevNumCB->count() > count) {
            ui.capDevNumCB->removeItem(ui.capDevNumCB->count() - 1);
        }
    }
}

void QtLPMPaicsVideoLibDemo::slotUpdateCurCapDevNum(const QString & text)
{
    m_iCurCapDevNum = text.toInt();
}

void QtLPMPaicsVideoLibDemo::slotInitFlvRecorder(void)
{
    if (m_pVideoCapturer == nullptr) {
        qWarning() << "请先初始化采集模块";
        return;
    }
    if (m_pFpsAdapter == nullptr) {
        qWarning() << "请先初始化帧率适配模块";
        return;
    }
    if (m_pFlvRecorder == nullptr) {
        m_pFlvRecorder = IVideoRecorder::Create(kFlvRecorder);

        if (m_pFlvRecorder) {
            if (m_pFlvRecorder->Init(
                m_pVideoCapturer->Width(),
                m_pVideoCapturer->Height(),
                1024 * 1024, 1, 0, m_pFpsAdapter->Fps()) < 0) {

                IVideoRecorder::Release(m_pFlvRecorder);
                qCritical() << "FLV录制模块初始化错误";
                return;
            }
            qInfo() << "FLV录制模块初始化成功";
        }
    } else {
        qWarning() << "FLV录制模块已经初始化了";
    }
}

void QtLPMPaicsVideoLibDemo::slotStartFlvRecorder()
{
    QString filepath = ui.flvPathLE->text();
    if (filepath.isEmpty()) {
        qWarning() << "FLV录制地址不能为空";
        return;
    }
    QFileInfo fileInfo(filepath);
    filepath = fileInfo.absoluteFilePath();
    if (m_pFlvRecorder) {
        if (m_pFpsAdapter == nullptr) {
            qWarning() << "请先初始化帧率适配模块";
            return;
        }
        m_pFpsAdapter->Reset(1);
        if (m_pFlvRecorder->BeginRecord(
            filepath.toStdString().c_str(),
            ui.recordPosCB->isChecked(),
            kRecordEncNone, ui.splitCB->isChecked(),
            ui.useWaterMarkCB->isChecked()
        ) < 0) {
            qCritical() << "FLV开始录制失败";
            return;
        }
        qInfo() << "FLV开始录制成功, 文件路径[" << filepath <<"]";
    } else {
        qWarning() << "请先初始化FLV录制模块";
    }
}

void QtLPMPaicsVideoLibDemo::slotStopFlvRecorder()
{
    if (m_pFlvRecorder) {
        if (m_pFlvRecorder->EndRecord() < 0) {
            qCritical() << "FLV停止录制失败";
            return;
        }
        qInfo() << "FLV停止录制成功，成功生成文件[" << m_pFlvRecorder->GetFilename().c_str() << "]";
    } else {
        qWarning() << "请先初始化FLV录制模块";
    }
}

void QtLPMPaicsVideoLibDemo::slotReleaseFlvRecorder(void)
{
    if (m_pFlvRecorder) {
        IVideoRecorder::Release(m_pFlvRecorder);
        qInfo() << "FLV录制模块释放成功";
    } else {
        qWarning() << "FLV录制模块还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotAddWaterMark(void)
{
    //QImage image = generalWaterMarkImg(1920, 1080, ui.waterMarkTextLE->text());
    //image.save(ui.waterMarkTextLE->text().append("-水印.png"), "PNG", 100);
    //return;

    if (m_pFlvRecorder) {
        QImage image = generalWaterMarkImg(1920, 1080, ui.waterMarkTextLE->text());
        QBuffer buffer;
        image.save(&buffer, "PNG", 100);
        image.save(ui.waterMarkTextLE->text().append("-水印.png"), "PNG", 100);
        //m_pFlvRecorder->AddWaterMark((uint8_t*)buffer.data().data(), buffer.data().size(), "png", 0, 0);
        QFile file("D:\\case\\0020116245881580\\2106\\25\\1624611254\\watermark.png");
        file.open(QIODevice::ReadOnly);
        QByteArray data = file.readAll();
        m_pFlvRecorder->AddWaterMark((uint8_t*)data.data(), data.size(), "png", 0, 0);
    } else {
        qWarning() << "请先初始化FLV录制模块";
    }
}

void QtLPMPaicsVideoLibDemo::slotRecordFrame(const FrameBase & frame)
{
    if (m_pFlvRecordFrame == nullptr) {
        m_pFlvRecordFrame = new FrameBase(frame);
    }
    *m_pFlvRecordFrame = frame;
    if (m_pFlvRecorder) {
        m_pFlvRecorder->Recording(m_pFlvRecordFrame);
    }
}

void QtLPMPaicsVideoLibDemo::slotInitPlayer(void)
{
    if (m_pVideoPlayer == nullptr) {
        m_pVideoPlayer = IVideoPlayer::CreatePlayer(kFFPlayer);

        ui.playerInitPB->setEnabled(false);
        ui.playerStartPB->setEnabled(true);
        ui.playerReleasePB->setEnabled(true);
        qInfo() << "播放器初始化成功";
    }
}

void QtLPMPaicsVideoLibDemo::slotStartPlayer(void)
{
    if (m_pVideoPlayer) {
        QString filepath = QFileDialog::getOpenFileName(this, "打开文件", ".", "*");
        if (filepath.isEmpty()) {
            qWarning() << "未选择视频";
            return;
        }
        m_pVideoPlayer->SetPalyPara(this);
        qInfo() << "选择视频文件[" << filepath << "]";
        if (m_pVideoPlayer->Init(filepath.toStdString().c_str()) < 0) {
            IVideoPlayer::Release(m_pVideoPlayer);
            qCritical() << "播放视频[" << filepath << "]失败";
            return;
        }

        //if (m_pVideoPlayer->Start(filepath.toStdString().c_str(), std::map<uint64_t, uint64_t>()) < 0) {
        //    qCritical() << "播放视频[" << filepath << "]失败";
        //    return;
        //}
        ui.playerStartPB->setEnabled(false);
        ui.playerPausePB->setEnabled(true);
        ui.playerPlayPB->setEnabled(true);
        ui.playerStopPB->setEnabled(true);
        auto totalTime = m_pVideoPlayer->GetTotalTime();
        ui.totalTimeLB->setText(msToPlayTime(totalTime));
        ui.playerSlider->setMaximum(totalTime);

        qInfo() << "成功播放视频";
    } else {
        qWarning() << "播放器还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotStopPlayer(void)
{
    if (m_pVideoPlayer) {
        //if (m_pVideoPlayer->Stop()) {
        //    qCritical() << "视频停止失败";
        //    return;
        //}
        ui.playerStopPB->setEnabled(false);
        ui.playerPausePB->setEnabled(false);
        ui.playerPlayPB->setEnabled(false);
        ui.playerStopPB->setEnabled(false);
        ui.playerStartPB->setEnabled(true);
        resetPlayerView();
        qInfo() << "视频停止成功";
    } else {
        qWarning() << "播放器还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotPausePlayer(void)
{
    if (m_pVideoPlayer) {
        if (m_pVideoPlayer->Pause()) {
            qCritical() << "视频暂停失败";
            return;
        }
        qInfo() << "暂停视频成功";
    } else {
        qWarning() << "播放器还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotPlayPlayer(void)
{
    if (m_pVideoPlayer) {
        if (m_pVideoPlayer->Play()) {
            qCritical() << "视频继续播放失败";
            return;
        }
        qInfo() << "暂停继续播放成功";
    } else {
        qWarning() << "播放器还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotReleasePlayer(void)
{
    if (m_pVideoPlayer) {
        IVideoPlayer::Release(m_pVideoPlayer);
        ui.playerInitPB->setEnabled(true);
        ui.playerPausePB->setEnabled(false);
        ui.playerPlayPB->setEnabled(false);
        ui.playerReleasePB->setEnabled(false);
        ui.playerStartPB->setEnabled(false);
        ui.playerStopPB->setEnabled(false);
        qInfo() << "播放器释放成功";
    } else {
        qWarning() << "播放器还未初始化";
    }
}

void QtLPMPaicsVideoLibDemo::slotSliderMoved(void)
{
    if (m_pVideoPlayer) {
        m_pVideoPlayer->SetPlayTime(ui.playerSlider->value());
    }
}

void QtLPMPaicsVideoLibDemo::slotUpdatePlayTime(int64_t time)
{
    ui.playerSlider->setValue(time);
    ui.playTimeLB->setText(msToPlayTime(time));
}

void QtLPMPaicsVideoLibDemo::slotUpdatePlayerStatus(PlayerEvent event)
{
    QString text;
    switch (event) {
    case LPM::kEvtPlayerPlaying:
        text = "播放中";
        break;
    case LPM::kEvtPlayerStopped:
        text = "播放器已停止";
        break;
    case LPM::kEvtPlayerPaused:
        text = "已暂停";
        break;
    case LPM::kEvtPlayerEndReached:
        text = "视频文件播放结束";
        if (QMessageBox::question(this,
            "提示", "视频播放结束，关闭播放器？",
            QMessageBox::No | QMessageBox::Yes,
            QMessageBox::Yes) == QMessageBox::Yes) {
            slotStopPlayer();
        } else {
            ui.playerPausePB->setEnabled(false);
            ui.playerPlayPB->setEnabled(false);
            resetPlayerView();
        }
        break;
    default:
        break;
    }
    ui.playerStatusLB->setText(text);
}
    
void QtLPMPaicsVideoLibDemo::slotUpdatePlayerFrame(const FrameBase & frame)
{
    if (frame.format == kImageI420) {
        QImage img(frame.width, frame.height, QImage::Format::Format_RGB888);
        I420ToBGR24(frame.data, img.bits(), frame.width, frame.height);
        BGR24ToRGB24(img.bits(), frame.width, frame.height);

        ui.playerViewLB->setPixmap(QPixmap::fromImage(
            img.scaled(ui.capViewLB->size(), Qt::AspectRatioMode::KeepAspectRatio)
        ));
    }
}

void QtLPMPaicsVideoLibDemo::slotClearLogWithWidget(void)
{
    ui.logTB->clear();
}

int QtLPMPaicsVideoLibDemo::OnFrameCallback(uint32_t capId, FrameBase * frame)
{
#if 1
    QMutexLocker locker(&m_fpsAdapterMutex);
    if (m_pVideoCapturer->CapturerID() == capId) {
        if (m_pFpsAdapter) {
            m_pFpsAdapter->UpdateFrame(frame);
        }
    }
#else 
    if (frame->seq % 2 == 0) {
        return 0;
    }
    if (m_pCapI420Frame == nullptr) {
        m_pCapI420Frame = new FrameBase(kImageI420, frame->width, frame->height);
    }
    m_pCapI420Frame->pts = frame->pts;
    m_pCapI420Frame->seq = frame->seq;

    if (frame->format == kImageYUY2) {
        QImage capImage(frame->width, frame->height, QImage::Format::Format_RGB888);

        // TODO::(Jeson) 提高预览显示效率
        YUY2ToI420(frame->data, m_pCapI420Frame->data, frame->width, frame->height);
        I420ToRGB24(m_pCapI420Frame->data, capImage.bits(), frame->width, frame->height);
        RGB24ToBGR24(capImage.bits(), frame->width, frame->height);

        emit sigRecordFlvFrame(*m_pCapI420Frame); // DirectConnection
        emit sigRenderCapFrame(capImage);
    } else if (frame->format == kImageMJPG) {

        QImage capImage;
        QByteArray jpgData((char*)frame->data, frame->size);
        capImage.loadFromData(jpgData, "JPG");
        capImage.save("jpg.jpg", "JPG", 100);
        int width = frame->width, height = frame->height;
        /////////////////////////////////////////////
        //const char * jpgFilepath = "test.jpg";
        //const char * yuvFilepath = "test.yuv";

        //int jpgBufSize = 1024 * 1024;
        //int yuvBufSize = width * height * 3 >> 1;
        //char * jpgBuf = new char[jpgBufSize];
        //char * yuvBuf = new char[yuvBufSize];

        //FILE * jpgFP = fopen(jpgFilepath, "rb");
        //bool isRead = false;
        //if (jpgFP) {
        //    int off = 0;
        //    while (true) {
        //        if (feof(jpgFP)) {
        //            break;
        //        }
        //        off += fread(jpgBuf + off, 1, 1, jpgFP);
        //    }
        //    fclose(jpgFP);
        //    isRead = true;
        //}

        //JpegToYuv420p(jpgBuf, jpgBufSize, yuvBuf, yuvBufSize, width, height);

        //FILE * yuvFP = fopen(yuvFilepath, "wb+");
        //if (yuvFP) {
        //    fwrite(yuvBuf, 1, width*height * 3 >> 1, yuvFP);
        //    fclose(yuvFP);
        //}

        //delete[] jpgBuf;
        //delete[] yuvBuf;
        /////////////////////////////////////////////////////
        // jpg to yuv420p
        if (JpegToYuv420p((char*)frame->data, frame->size,
            (char*)m_pCapI420Frame->data, m_pCapI420Frame->size, width, height)) {
            //FILE * fp = fopen("jpg_to_yuv.yuv", "wb+");
            //if (fp) {
            //    fwrite(m_pCapI420Frame->data, 1, m_pCapI420Frame->size, fp);
            //    fclose(fp);
            //}
            emit sigRecordFlvFrame(*m_pCapI420Frame); // DirectConnection
        }

        emit sigRenderCapFrame(capImage);
    }
#endif
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnCapturerStatus(uint32_t capId, CapStatus status)
{
    if (m_pVideoCapturer->CapturerID() == capId) {
        emit sigUpdateCapStatus(status);
    }
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnFpsAdpterCallback(FrameBase * frame)
{
#if 1
    if (m_pCapI420Frame == nullptr) {
        m_pCapI420Frame = new FrameBase(kImageI420, frame->width, frame->height);
    }
    m_pCapI420Frame->pts = frame->pts;
    m_pCapI420Frame->seq = frame->seq;

    if (frame->format == kImageYUY2) {
        QImage capImage(frame->width, frame->height, QImage::Format::Format_RGB888);

        // TODO::(Jeson) 提高预览显示效率
        clock_t start = clock();
        YUY2ToI420(frame->data, m_pCapI420Frame->data, frame->width, frame->height);
        I420ToBGR24(m_pCapI420Frame->data, capImage.bits(), frame->width, frame->height);
        BGR24ToRGB24(capImage.bits(), frame->width, frame->height);
        clock_t end = clock();
        //qDebug() << __FUNCTION__ << (double)(end - start) / CLOCKS_PER_SEC;

        emit sigRecordFlvFrame(*m_pCapI420Frame); // DirectConnection
        emit sigRenderCapFrame(capImage);

    } else if(frame->format == kImageMJPG){

        QImage capImage;
        QByteArray jpgData((char*)frame->data, frame->size);
        capImage.loadFromData(jpgData, "JPG");
        capImage.save("jpg.jpg", "JPG", 100);
        int width = frame->width, height = frame->height;
        /////////////////////////////////////////////
        //const char * jpgFilepath = "test.jpg";
        //const char * yuvFilepath = "test.yuv";

        //int jpgBufSize = 1024 * 1024;
        //int yuvBufSize = width * height * 3 >> 1;
        //char * jpgBuf = new char[jpgBufSize];
        //char * yuvBuf = new char[yuvBufSize];

        //FILE * jpgFP = fopen(jpgFilepath, "rb");
        //bool isRead = false;
        //if (jpgFP) {
        //    int off = 0;
        //    while (true) {
        //        if (feof(jpgFP)) {
        //            break;
        //        }
        //        off += fread(jpgBuf + off, 1, 1, jpgFP);
        //    }
        //    fclose(jpgFP);
        //    isRead = true;
        //}

        //JpegToYuv420p(jpgBuf, jpgBufSize, yuvBuf, yuvBufSize, width, height);

        //FILE * yuvFP = fopen(yuvFilepath, "wb+");
        //if (yuvFP) {
        //    fwrite(yuvBuf, 1, width*height * 3 >> 1, yuvFP);
        //    fclose(yuvFP);
        //}

        //delete[] jpgBuf;
        //delete[] yuvBuf;
        /////////////////////////////////////////////////////
        // jpg to yuv420p
        if (JpegToYuv420p((char*)frame->data, frame->size,
            (char*)m_pCapI420Frame->data, m_pCapI420Frame->size, width, height)) {
            //FILE * fp = fopen("jpg_to_yuv.yuv", "wb+");
            //if (fp) {
            //    fwrite(m_pCapI420Frame->data, 1, m_pCapI420Frame->size, fp);
            //    fclose(fp);
            //}
            emit sigRecordFlvFrame(*m_pCapI420Frame); // DirectConnection
        }

        emit sigRenderCapFrame(capImage);
    }
#else
    qDebug() << frame->seq << frame->pts;
#endif
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnPlayerCallback(PlayerEvent event)
{
    emit sigUpdatePlayerStatus(event);
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnPlayerTimeChangedCallback(int64_t time)
{
    emit sigUpdatePlayTime(time);
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnPlayerBufferingCallback(float percent)
{
    return 0;
}

int QtLPMPaicsVideoLibDemo::OnPlayerFrameCallback(FrameBase * frame)
{
    qDebug() << QDateTime::currentDateTime() << frame->seq << frame->pts;
    QThread::msleep(5);
    emit sigUpdatePlayerFrame(*frame);
    return 0;
}

void QtLPMPaicsVideoLibDemo::initConnect(void)
{
    /** 采集模块相关 */
    connect(ui.initCapPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotInitVideoCapturer);
    connect(ui.startCapPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStartVideoCapturer);
    connect(ui.stopCapPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStopVideoCapturer);
    connect(ui.releaseCapPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotReleaseVideoCapturer);
    connect(this, &QtLPMPaicsVideoLibDemo::sigUpdateCapStatus, this, &QtLPMPaicsVideoLibDemo::slotUpdateCapStatus);
    connect(this, &QtLPMPaicsVideoLibDemo::sigRenderCapFrame, this, &QtLPMPaicsVideoLibDemo::slotCapRenderFrame);
    connect(&m_updateCapDevCountTimer, &QTimer::timeout, this, &QtLPMPaicsVideoLibDemo::slotUpdateCapCount);
    connect(ui.capDevNumCB, &QComboBox::currentTextChanged, this, &QtLPMPaicsVideoLibDemo::slotUpdateCurCapDevNum);
    // 帧率适配模块
    connect(ui.fpsSB, SIGNAL(valueChanged(int)), SLOT(slotReCreateFpsAdapter(int)));

    /** 视频录制模块相关 */
    connect(ui.flvInitPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotInitFlvRecorder);
    connect(ui.flvReleasePB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotReleaseFlvRecorder);
    connect(ui.flvStartRecordPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStartFlvRecorder);
    connect(ui.flvStopRecordPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStopFlvRecorder);
    connect(ui.addWaterMarkPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotAddWaterMark);
    connect(this, &QtLPMPaicsVideoLibDemo::sigRecordFlvFrame, this, &QtLPMPaicsVideoLibDemo::slotRecordFrame, Qt::DirectConnection);

    /** 播放器模块相关 */
    connect(ui.playerInitPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotInitPlayer);
    connect(ui.playerStartPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStartPlayer);
    connect(ui.playerPausePB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotPausePlayer);
    connect(ui.playerPlayPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotPlayPlayer);
    connect(ui.playerStopPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotStopPlayer);
    connect(ui.playerReleasePB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotReleasePlayer);
    connect(ui.playerSlider, &QSlider::sliderMoved, this, &QtLPMPaicsVideoLibDemo::slotSliderMoved);
    connect(this, &QtLPMPaicsVideoLibDemo::sigUpdatePlayTime, this, &QtLPMPaicsVideoLibDemo::slotUpdatePlayTime);
    connect(this, &QtLPMPaicsVideoLibDemo::sigUpdatePlayerStatus, this, &QtLPMPaicsVideoLibDemo::slotUpdatePlayerStatus);
    connect(this, &QtLPMPaicsVideoLibDemo::sigUpdatePlayerFrame, this, &QtLPMPaicsVideoLibDemo::slotUpdatePlayerFrame);

    /** 日志相关 */
    connect(ui.clearLogPB, &QPushButton::clicked, this, &QtLPMPaicsVideoLibDemo::slotClearLogWithWidget);
    connect(this, &QtLPMPaicsVideoLibDemo::sigAppendLogMsg, this, &QtLPMPaicsVideoLibDemo::slotAppendLogMsg);
}

QString QtLPMPaicsVideoLibDemo::msToPlayTime(int64_t time)
{
    int ms = time % 1000;
    int ss = (time / 1000) % 60;
    int min = (time / 1000 / 60) % 60;
    int hor = (time / 1000 / 60 / 60) % 24;
    QString h = QString::number(hor);
    QString m = QString::number(min);
    QString s = QString::number(ss);

    if (ss < 10) {
        s = "0" + s;
    }
    if (min < 10) {
        m = "0" + m;
    }
    if (hor < 10) {
        h = "0" + h;
    }

    return h + ":" + m + ":" + s + ":" + QString::number(ms);
}

void QtLPMPaicsVideoLibDemo::resetPlayerView(void)
{
    QPalette p;
    p.setColor(QPalette::Background, QColor(139, 139, 139));
    ui.playerViewLB->setAutoFillBackground(true);
    ui.playerViewLB->setPalette(p);
    ui.playerViewLB->update();
}
static void drawRotatedText(QPainter *painter, float degrees, int x, int y, const QString &text)
{
    painter->save(); //保存原来坐标系统
    painter->translate(x, y); //平移坐标原点到 x， y
    painter->rotate(degrees); //坐标旋转degrees 度
    painter->drawText(0, 0, text); //在原点绘制文本
    painter->restore(); //回复原来的坐标系统
}

QImage QtLPMPaicsVideoLibDemo::generalWaterMarkImg(int imgW, int imgH, const QString & text)
{
#define PI 3.1415
    QFont font;
    font.setPixelSize(40);
    font.setFamily("msyh");
    QFontMetrics fm(font);
    const int append = 200;

    int h = fm.ascent() + fm.descent();
    int w = fm.width(text);

    w = std::abs(std::cos(24.0 * PI / 180) * w) + append;
    h = std::abs(std::sin(24.0 * PI / 180) * w) + append;

    QImage image(imgW, imgH, QImage::Format_ARGB32);
    image.fill(qRgba(0,0,0,100));

    QPainter painter(&image); 
    painter.setCompositionMode(QPainter::CompositionMode_DestinationOver);
    //设置画刷的组合模式CompositionMode_SourceOut这个模式为目标图像在上。
    //改变组合模式和上面的填充方式可以画出透明的图片。

    QPen pen = painter.pen();
    pen.setColor("#FFFFFF");
    //pen.setStyle(Qt::DotLine);
    painter.setOpacity(0.1);
    painter.setPen(pen);
    painter.setFont(font);

    for (int i = 0; i < image.width(); i += w) {
        for (int j = 0; j < image.height(); j += h) {
            drawRotatedText(&painter, -24, append + i, j + h, text);
        }
    }
    return image;
}
