﻿#pragma once

#include <string>
using namespace std;

#include "LPMCommonLib.h"

#ifdef __cplusplus
extern "C"
{
#endif 

namespace LPM
{
	//	日志等级
	typedef enum EmlogLeve { LogError = 0, LogInfo = 1, LogWarning = 2, LogDebug = 3 }EmlogLeve;
	//	日志模块
	typedef enum EmModuleType{	ModuleCommon	= 0,
								ModuleLogic		= 1,
								ModuleNetwork	= 2,				//	网络模块
								ModuleVideo		= 3,				//	视频模块
								ModuleUI		= 4, 
								ModuleData		= 5 }EmModuleType;	//	埋点数据 所有埋点类型，都是LogInfo

	//小于0 不写，  0 error ， 1 info  ，2 warning ,3 info 与 error,
	//			    4 error 与 info、 warning  5 error 与 info、 warning、debug , 6 debug(文件与debug output) 7 以上 只有debug信息， 没有文件
	void LPM_PUBLIC_API SetLogPath(const char* _szLogPathUtf8,const char* _szLogName = nullptr, int _nLogLeve = 5);
	

	//	0 debug  1 info  2 error
	bool LPM_PUBLIC_API WriteLogEx(const EmModuleType _emType, const EmlogLeve _emLeve, const char* _szLogInfo, ...);


	//	休眠	(_nType:1 毫秒   _nType:2 微妙   _nType:3  纳秒  _nType:-1 分种 _nType:-2 小时)
	void LPM_PUBLIC_API SleepExEx(long long _llSleepTime, int _nType = 1);
	//	休眠2	毫秒级别
	void LPM_PUBLIC_API SleepExEx2(unsigned long _lSleepTime);
}

#ifdef __cplusplus
}
#endif
