﻿/*
 * Filename:  flv-encryption-helper.h
 * Project :  libflv
 * Created by <PERSON><PERSON> on 12/06/2020.
 * Copyright © 2020 Guangzhou AiYunJi Inc. All rights reserved.
 */
#ifndef __FLV_ENCRYPTION_HELPER_H__
#define __FLV_ENCRYPTION_HELPER_H__
#include <stdio.h>
#if defined(__cplusplus)
extern "C" {
#endif
    typedef enum encry_version
    {
        encry_version_0,  //Do not encrypt
        encry_version_1,
        encry_version_max
    }encry_version;

    int get_flv_encry_version(const char *filename);
    int flv_encrypt_h264(char * h264, int size, encry_version version);
    int flv_dencrypt_h264(char * h264, int size, encry_version version);
#if defined(__cplusplus)
}
#endif
#endif // !__FLV_ENCRYPTION_HELPER_H__
